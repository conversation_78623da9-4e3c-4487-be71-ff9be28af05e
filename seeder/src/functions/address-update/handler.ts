import "source-map-support/register";
import { formatJSONResponse } from "@libs/apiGateway";
import { middyfy } from "@libs/lambda";
import { PortalDocuments, pgClient } from "@libs/pgClient";

const handler = async () => {
  const connection = await pgClient();
  await connection.connect();

  const existingCompanies = await connection.query(
    `SELECT * FROM "${PortalDocuments.company}"`
  );

  for (const company of existingCompanies.rows) {
    if (!company.address) {
      const main_address = company.main_address;
      const main_postcode = company.main_postcode;

      const split = main_address.split(",");
      const address_line_1 = [split[0], split[1]].join(",");
      const postal_town = split[2];
      const county = "";
      const display_name = [split[0], split[1], split[2]].join(",");
      const uprn = "";

      const newAddress = (
        await connection.query(
          `INSERT INTO ${PortalDocuments.entity_address} (address_line_1, postal_town, county, postcode, display_name, uprn) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
          [
            address_line_1,
            postal_town,
            county,
            main_postcode,
            display_name,
            uprn,
          ]
        )
      ).rows[0];

      await connection.query(
        `UPDATE "${PortalDocuments.company_site}" SET address_id = $1 WHERE id = $2`,
        [newAddress.id, company.id]
      );
    }
  }

  return formatJSONResponse({});
};

export const main = middyfy(handler);
