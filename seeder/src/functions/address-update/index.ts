import { handlerPath } from "@libs/handlerResolver";

import {
  ENVIRONMENT,
  PORTAL_DB_INSTANCE_SECRET_ARN,
} from "../../../serverless";

export default {
  handler: `${handlerPath(__dirname)}/handler.main`,
  ...(ENVIRONMENT !== "prod" && {
    events: [
      {
        http: {
          path: "seed",
          method: "get",
        },
      },
    ],
  }),

  iamRoleStatementsName:
    "${opt:stage, 'dev'}-${self:service}-seed-lambda-${self:provider.region}",
  iamRoleStatements: [
    {
      Effect: "Allow",
      Action: ["secretsmanager:GetSecretValue"],
      Resource: [PORTAL_DB_INSTANCE_SECRET_ARN],
    },
  ],
};
