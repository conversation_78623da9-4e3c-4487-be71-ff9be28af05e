This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where empty lines have been removed, content has been compressed (code blocks are separated by ⋮---- delimiter).

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: python/
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Empty lines have been removed from all files
- Content has been compressed - code blocks are separated by ⋮---- delimiter

## Additional Info

# Directory Structure
```
python/
  app/
    api/
      common/
        quote_failure/
          decorator.py
          emails.py
          exceptions.py
        constants.py
        dtos.py
        exceptions.py
        route.py
        services.py
      companies/
        dtos/
          email_verification.py
          request.py
          response.py
        exceptions.py
        services.py
        views.py
      contracts/
        dtos/
          exceptions.py
          request.py
          response.py
        services.py
        views.py
      providers/
        dtos.py
        views.py
      public/
        dtos/
          request.py
          response.py
        services.py
        views.py
      quotes/
        dtos/
          request.py
          response.py
        common.py
        electricity_services.py
        gas_services.py
        services.py
        views.py
      reports/
        mapper.py
        services.py
        types.py
        views.py
      security/
        dtos/
          request.py
          response.py
        services.py
        views.py
      usage/
        constants.py
        services.py
        utils.py
        views.py
      blueprint.py
    common/
      aws/
        secrets_retrieval.py
      db/
        connection.py
        session.py
      local_mode_data/
        britishgas.py
        electralink.py
        experian_aperture.py
        lambdas.py
        pdf_as_bytearray.py
        udcore.py
      postcodes_uk/
        main/
          exceptions.py
          formatter.py
          validator.py
      secrets/
        secrets.py
      security/
        csrf/
          check.py
          exceptions.py
          inject.py
        decorators.py
        session.py
      utils/
        accepts_kwarg.py
        call_endpoint.py
        case_styles.py
        company_number.py
        csv_generator.py
        decode_base64_string.py
        hide_meter_numbers.py
        human_readable.py
        is_valid_email_address.py
        json_template_renderer.py
        prevent_auto_linking.py
        random_string.py
        test_human_readable.py
        url_builder.py
      constants.py
      is_in_local_mode.py
      local_mode.py
      mpan.py
      mprn.py
    domains/
      dropoff/
        payloads.py
        scheduler.py
    integrations/
      crm/
        services.py
      electralink/
        _types.py
        api_utils.py
        get_mpan_hhcombined.py
        models.py
        mpan_additional_details.py
        mpan.py
        usage.py
      experian_aperture/
        _types.py
        api_utils.py
        lookup.py
      lambdas/
        contract_pdf_generator/
          contract_pdf_generator.py
          supplier_template_keys.py
        credit_score/
          get_credit_score.py
        messaging/
          email_contract_completion.py
          email_quote_failure.py
          email_rejoin_existing_fuzzy_matched_company.py
          email_verification.py
          sole_trader.py
        notify_scheduler/
          notify_scheduler.py
        payloads/
          email.py
          pdf.py
        common.py
      s3/
        dtos.py
        exceptions.py
        s3.py
        save_data.py
        util.py
      udcore/
        data/
          electric_quote_definitions.py
          gas_quote_definitions.py
        electricprices/
          http.py
        gasprices/
          http.py
        types.py
        utility_prices.py
    models/
      companies/
        address.py
        agreements.py
        auth.py
        banking.py
        company_aquisition_info.py
        company.py
        contact.py
        credit_score.py
        industry.py
        README.md
        site.py
        sole_trader.py
      electricity/
        tariffs.py
        usage.py
      gas/
        usage.py
      quotes/
        constants.py
        quote_list.py
        quote.py
      reports/
        quote_rag.py
      utils/
        pickle.py
      common.py
      contracts.py
      email.py
      enums.py
      industry.py
      orm_builder.py
      providers.py
      pydantic_builder.py
    repository/
      banking.py
      companies.py
      constants.py
      contracts.py
      industry.py
      provider.py
      quotes.py
      usage.py
      utility_dispatch.py
    app.py
    config.py
    constants.py
    logging.py
    server.py
  docs/
    db_migrations.md
    local_mode.md
    tests.md
  README.md
```

# Files

## File: python/app/api/common/quote_failure/decorator.py
````python
"""This module contains the decorator for sending an email on quote failure"""
⋮----
def send_internal_email_on_quote_failure_exceptions(func: Callable) -> Callable
⋮----
"""
    This is a decorator that wraps a function with a try-except block,
    catching `QuoteFailureException` and logging the error.
    If a `QuoteFailureException` is caught, an email is sent via `send_quote_failure_email_for_company_id`
    This decorator can only be used on functions that have a `request` with a `company_id` attached to it.
    :param func: the function to wrap
    :return: the created decorator
    :raises ServerError: if the request or company_id is not found
    :raises ServerError: if a `QuoteFailureException` is caught
    """
# (Jude): quick way to get the session
⋮----
@with_db_session
    async def get_session(session: AsyncSession = None)
⋮----
@wraps(func)
    async def wrapper(request: Request, *args, **kwargs)
⋮----
# TODO (James) use with_company decorator?
company_id = request.ctx.company_id
⋮----
session = await get_session()
dto = kwargs.get("dto")
⋮----
# (Jude): Stop all reminders for this company if the quote fails
company: Company = await get_company_by_id(company_id, session)
primary_email = company.primary_contact.email
# (Jude): Deschedule all reminders if the quote fails
⋮----
# TODO (James) everything is a 500 atm, it would be nice to support more specific error codes
````

## File: python/app/api/common/quote_failure/emails.py
````python
def dict_to_obj(d)
⋮----
d = {k: dict_to_obj(v) for k, v in d.items()}
⋮----
"""
    Send email internal quote failure email for a company constructed from the DTO
    This is used for a failure that happens before the Company model is created
    """
company = company_dto.company
site = company_dto.site
async def compose_soletrader_addresses()
⋮----
soletrader_addresses = []
⋮----
soletrader_address_data = await get_address_by_address_id(
address_entity = EntityAddress(
soletrader_personal_addresses = SoleTraderPersonalAddress(
⋮----
main_address = getattr(company, "address", None)
main_address_id = getattr(main_address, "id", None)
site = getattr(company_dto, "site", None)
site_address_id = getattr(site, "address_id", None)
⋮----
site_address_data = await get_address_by_address_id(site_address_id)
site_address_postcode = site_address_data.get("postcode", None)
main_company_address_data = None
main_address_postcode = None
# if main address id is not the same as the site address id then we need to get the main address data
⋮----
main_company_address_data = await get_address_by_address_id(
main_address_postcode = main_company_address_data.get("postcode", None)
⋮----
mpans = []
mprns = []
⋮----
bottom_line_mpans = site_address_data.get("mpans", [])
mprns = site_address_data.get("mprns", []).copy()
⋮----
additional_details = await get_mpan_additional_details(
⋮----
contact = company_dto.contact
soletrader_personal_details = getattr(company, "soletrader_personal_details", None)
# TODO (Stephen): Quote for electric and gas. I sign electric but gas fails.
# TODO Internal failure email lists both utilities managed which kind of
# TODO implies both are failing which is not true
utilities_managed = getattr(site, "utilities_managed", None)
agreement_set = getattr(company_dto, "agreement_set", None)
company_data = {
⋮----
# Default to the site address and postcode if none provided
⋮----
# TODO (Stephen): Rename these keys to plural will need to update email templates
⋮----
# TODO (Stephen): this needs updating for multi site
annual_gas_usage = await get_gas_meter_by_mprn(mprns[0]) if len(mprns) > 0 else None
annual_elec_usage = (
⋮----
# TODO (Stephen): Refactor to support multi site
⋮----
electricity_usage = {
⋮----
# TODO (Stephen): Refactor to support multi site
⋮----
# Todo (Olly) integrate other properties from electricity_usage!
⋮----
# Credit score is not available in this case
⋮----
"""Send internal quote failure email for a company retrieved by id"""
⋮----
# Set default values from database
electricity_usage = electricity_usage_from_db or {}
gas_usage = gas_usage_from_db or {}
# Checks if dto is not None and is an instance of SubmitUsageDto
⋮----
else:  # UtilityType.GAS
⋮----
credit_score = await get_credit_score_by_company_id(company_id, session=session)
````

## File: python/app/api/common/quote_failure/exceptions.py
````python
class QuoteFailureException(Exception)
⋮----
"""
    An exception that is raised when an error occurs during the quote process
    This is used to send an email to the internal team.
    :param code: The code of the error (human readable, not an HTTP code)
    :param message: The message of the error (for internal use)
    :param customer_message: The message to be shown to the customer
    """
def __init__(self, code: int, message: str, customer_message: str)
class QuotationFailureEmailErrors(enum.Enum)
⋮----
"""
    An enum of all the errors that can occur during the quote process
    This is used to send an email to the internal team.
    You can raise a `QuoteFailureException` by using the `exception` property of the enum.
    e.g. `raise QuotationFailureEmailErrors.INVALID_UTILITY_PARAMS.exception()`
    NOTE: when adding a new error, make sure to leave a gap in the error codes
            (e.g. if the last error code is 10001, the next error code should be 10050
            or so, unless you are adding a new error that is related to the last error)
    :param code: The code of the error (human readable, not an HTTP code)
    :param message: The message of the error (for internal use)
    """
def __new__(cls, *args, **kwds)
⋮----
value = len(cls.__members__) + 1
obj = object.__new__(cls)
⋮----
def __init__(self, code: int, message: str)
INVALID_UTILITY_PARAMS = ("10001", "Invalid utility params")
ERROR_GET_CONTRACTS = ("10002", "Error getting contracts")
CONTRACT_PDF_URL_NOT_FOUND = ("10003", "Contract PDF URL not found")
QUOTE_LIST_NOT_FOUND = ("10004", "Quote list not found")
QUOTE_NOT_FOUND = ("10005", "Quote not found")
CONTRACT_DOES_NOT_EXIST = ("10006", "Contract does not exist")
USAGE_NOT_FOUND = ("10007", "Usage not found")
ERROR_CREATING_CONTRACT = ("10008", "Error creating contract")
COMPANY_NOT_SUPPORTED = ("10009", "Company not supported")
CONTRACT_USAGE_NOT_FOUND = ("10010", "Contract usage not found")
PROVIDER_NOT_FOUND = ("10011", "Provider not found for contract")
NO_PDF_KEY_FOR_PROVIDER = ("10012", "No PDF key for provider")
ERROR_UPDATING_PDF = ("10012", "Error updating Contract PDF")
ERROR_CREATING_PDF = ("10013", "Error creating Contract PDF")
ERROR_GETTING_QUOTES = ("10014", "Error getting quotes")
ERROR_CREATING_QUOTES = ("10015", "Error creating quotes")
ERROR_GETTING_CREDIT_SCORE = ("10016", "Error getting credit score")
TIMEOUT_GETTING_CREDIT_SCORE = ("10017", "Timeout getting credit score")
ERROR_SAVING_CREDIT_SCORE = ("10018", "Error saving credit score")
NO_CREDIT_SCORE = ("10019", "No credit score")
UNSUPPORTED_BUSINESS_TYPE = ("10020", "Unsupported business type")
ERROR_GETTING_USAGE = ("10021", "Error getting usage")
INVALID_CONTRACT = ("10022", "Invalid contract")
ERROR_UPDATING_QUOTE_LIST = ("10023", "Error updating quote list")
ERROR_PROVIDERS_NOT_FOUND = ("10024", "Error providers not found")
INVALID_ID_PROVIDED = ("10026", "Invalid Contract ID provided")
ERROR_SIGNING_CONTRACT = ("10100", "Error signing contract")
COMPANY_CREATE_FAILED = ("10300", "Failed to create company")
COMPANY_CREATE_MALFORMED_REQUEST = (
COMPANY_CREATE_INVALID_VERIFICATION_CODE = (
COMPANY_CREATE_NON_ENERGISED_MPAN = (
COMPANY_CREATE_INVALID_MPAN = (
COMPANY_CREATE_COMPANY_ALREADY_EXISTS = (
COMPANY_CREATE_INDUSTRY_NOT_SUPPORTED = (
COMPANY_ACQUISITION_CREATE_FAILED = (
COMPANY_CREATE_FAILED_DIRECT_DEBIT_AGREEMENT_REQUIRED = (
COMPANY_CREATE_INVALID_MPRN = (
COMPANY_CREATE_NON_LIVE_MPRN = (
COMPANY_CREATE_NO_MPAN_OR_MPRN = (
COMPANY_CREATE_NO_POSTCODE = (
UNSUPPORTED_PROVIDER_CREATE_FAILED = (
ERROR_UPDATING_COMPANY_MPRN = ("10500", "Failed to update company MPRN")
ERROR_UPDATING_COMPANY_MPAN = ("10503", "Failed to update company MPAN")
ERROR_MPRN_NOT_FOUND = ("10501", "Failed to get MPRN: not found")
ERROR_MPAN_NOT_FOUND = ("10502", "Failed to get MPAN: not found")
````

## File: python/app/api/common/constants.py
````python
class ErrorMsgs
⋮----
NO_UTILITIES = "No utilities sent"
INVALID_UTILITIES = "Invalid utilities sent"
NONEXISTENT_UTILITIES = "One or more utilities does not exist"
````

## File: python/app/api/common/dtos.py
````python
"""Common DTOs for the API"""
⋮----
class IgnoreLazyGetterDict(GetterDict)
⋮----
"""GetterDict that ignores lazy-loaded relationships"""
def __getitem__(self, key: str)
def get(self, key, default=None)
⋮----
# if a relationship field is not already loaded as a result of explicit join, ignore it,
# so that pydantic schema doesn't trigger a lazy-load
⋮----
def _is_lazy_loaded(self, key)
def _convert_uuid_fields(dictionary: Dict[str, Any])
⋮----
"""Recursively convert UUID fields to strings in a dictionary"""
⋮----
class OrmBase(BaseModel)
⋮----
"""
    Pre-processing validator that evaluates lazy relationships before
    any other validation. NOTE: If high throughput/performance is a concern,
    you can/should probably apply this validator in a more targeted fashion
    instead of a wildcard in a base class.
    This approach is by no means slow, but adds a minor amount
    of overhead for every field
    """
⋮----
@validator("*", pre=True)
@classmethod
    def evaluate_lazy_columns(cls, value: Any)
⋮----
"""Evaluate lazy-loaded columns before any other validation
        Args:
            value (Any): The value to validate
        Returns:
            _type_: _description_
        """
⋮----
class Config
⋮----
"""Pydantic config for OrmBase"""
orm_mode = True
getter_dict = IgnoreLazyGetterDict
def dict(self, **kwargs)
⋮----
"""Convert UUID fields to strings in the dictionary"""
dictionary = super().dict()
⋮----
class TimeStampMixin(BaseModel)
⋮----
"""Timestamp mixin for models"""
created_at: Optional[datetime]
updated_at: Optional[datetime]
⋮----
@validator("created_at", "updated_at", check_fields=False)
@classmethod
    def convert_to_iso_format(cls, value)
⋮----
"""_summary_
        Args:
            value (_type_): _description_
        Returns:
            _type_: _description_
        """
⋮----
class TimeRangeMixin(BaseModel)
⋮----
"""Time range mixin for models"""
start_date: Optional[datetime]
end_date: Optional[datetime]
⋮----
@validator("end_date", "start_date", check_fields=False)
@classmethod
    def convert_to_iso_format(cls, value)
````

## File: python/app/api/common/exceptions.py
````python
class FilterException(Exception)
⋮----
"""error when filtering inside a service"""
class UtilityValidationException(Exception)
⋮----
"""Proxy message to send back as bad request when the utilities params are not correct"""
````

## File: python/app/api/common/route.py
````python
"""
    A decorator to simplify routing and other utilities for handlers.
    - Automatically parses the request body into a DTO
    If another decorator depends on `dto` being in the kwargs, it should be
    added after (below) this decorator.
    :param blueprint: The blueprint object to register the route to.
    :param method: The HTTP method for the route.
    :param path: The endpoint path for the route.
    :param request_dto: (Optional) DTO class for request body validation and parsing.
    :param with_db_session: (Optional) If True, wraps the function with with_db_session.
    :param check_csrf: (Optional) If True, wraps the function with csrf_protected.
    :return: The decorated function.
    """
def decorator(func)
⋮----
# This is the same as doing @my_blueprint.route(path, methods=[method])
decorated = blueprint.route(path, methods=[method], name=func.__name__)
# TODO (James) add openapi documentation here
# A new async function is defined here, that will be used to wrap the original function
⋮----
@wraps(func)
@decorated
        async def wrapper(request: Request, *args, **kwargs)
⋮----
# If request_dto is provided, parse the request json into the request_dto
⋮----
dto = request_dto.parse_obj(request.json)
⋮----
dto = None
⋮----
# Store the wrapped function so we can wrap it further if needed
fn = func
# Check the CSRF token and set up the session if needed
# This is the same as doing @csrf_protected on the function
⋮----
fn = csrf_protected(fn)
# If with_session is True, wrap the function with with_db_session
# this is the same as doing @with_db_session on the function
⋮----
fn = with_db_session_decorator(fn)
⋮----
# Return the wrapper function
⋮----
def prohibit_route_execution(func)
⋮----
"""
    A decorator to prohibit the execution of a specific route.
    When this decorator is applied to a function, calling that function will raise an exception
    with the message "You should not be calling this!"
    :param func: The function to be wrapped.
    :return: The wrapped function.
    """
⋮----
@wraps(func)
    def wrapper(*args, **kwargs)
````

## File: python/app/api/common/services.py
````python
async def validate_utilities_params(utilities_params: list) -> list
⋮----
"""validate_utilities_params validates the utilities params
    Args:
        utilities_params (list): utilities params
    Raises:
        UtilityValidationException: if utilities params are invalid
    Returns:
        list: list of utilities
    """
⋮----
utilities: List[int] = []
⋮----
selected_utilities = list(dict.fromkeys(utilities_params))
````

## File: python/app/api/companies/dtos/email_verification.py
````python
class SendCompanyEmailVerificationDto(OrmBase)
⋮----
"""
    This is a DTO for the company email verification
    """
email: EmailStr
class VerifyCompanyEmailCodeDto(OrmBase)
⋮----
"""
    This is a DTO for verifying company email verification code
    """
⋮----
code: str
````

## File: python/app/api/companies/dtos/request.py
````python
"""This module contains the DTOs for the companies API"""
⋮----
class AddressCreateRequestDto(BuildablePydanticBase)
⋮----
id: UUID4
address = str
meter_points: dict[str, int]
class SoleTraderPersonalAddressCreateDto(BuildablePydanticBase)
⋮----
"""This is the sole trader personal address DTO"""
address_id: str
moved_in_at: datetime
"""The date that the person moved into this address"""
moved_out_at: Optional[datetime]
"""
    The date that the person moved out of this address
    If this is None then the person is still living at this address
    """
⋮----
@validator("moved_out_at")
@classmethod
    def validate_moved_out_at(cls, moved_out_at, values)
⋮----
"""Validate that the moved out at date is after the moved in at date"""
⋮----
class SoleTraderPersonalDetailsCreateDto(BuildablePydanticBase)
⋮----
"""This is the sole trader personal details DTO"""
date_of_birth: datetime
addresses: List[SoleTraderPersonalAddressCreateDto]
⋮----
@validator("addresses")
@classmethod
    def validate_addresses(cls, addresses: List[SoleTraderPersonalAddressCreateDto])
⋮----
"""
        Validate the sole trader personal address history
        - Must have at least one address
        - Must cover the last 5 years
        - Must not have any gaps of more than 31 days between addresses
        - Must not have any overlaps between addresses
        """
⋮----
five_years_ago = datetime.now() - timedelta(
# Sort addresses by moved_in_at date
⋮----
# The initial address is used as the initial previous address to compare against the next one and it gets updated to the next address in the loop for further date comparisons
previous_address = addresses[0]
# check that the sole trader move into his last address less than 5 yrs ago
⋮----
# Check for address overlap
⋮----
# Check for gap between moved_in date and last address move out
gap = address.moved_in_at - previous_address.moved_out_at
⋮----
# Update previous_address to the current one for the next iteration.
previous_address = address
⋮----
class CompanyCreateDto(BuildablePydanticBase)
⋮----
"""This is the company create DTO
    Args:
        OrmBase (OrmBase): The base class for the DTO
    Raises:
        ValueError: If the business type is not LTD and the registration number is not None
        ValueError: If registration number is empty
        ValueError: Charity number should be empty for non charity companies
        ValueError: Charity number should be 6 to 7 digits
    """
id: Optional[UUID4]
# Must be placed before the unique fields to ensure that the validators are run in the correct order
business_type: BusinessType
address: AddressCreateRequestDto
postcode: str
registration_number: Optional[str]
charity_number: Optional[str]
soletrader_personal_details: Optional[SoleTraderPersonalDetailsCreateDto]
name: str
class Config
⋮----
"""Pydantic config class"""
use_enum_values = True
⋮----
"""Validate that the registration number is a UK registration number"""
⋮----
"""Validate that the charity number is a valid charity number"""
⋮----
class CompanyAcquisitionDto(OrmBase)
⋮----
"""This is the company acquisition DTO
    Args:
        OrmBase (OrmBase): The base class for the DTO
    """
"""The base64 encoded email of the user that was emailed"""
target_id: str
"""The campaign id of the market campaign that the user was emailed under"""
campaign_id: str
class ContactCreateDto(BuildablePydanticBase)
⋮----
"""This is the contact DTO
    Args:
        OrmBase (OrmBase): The base class for the DTO
    """
⋮----
forename: str
surname: str
email: EmailStr
phone: str
position: str
code: Optional[str]
class CompanySiteCreateDto(BuildablePydanticBase)
⋮----
"""This is the company site DTO
    Args:
        OrmBase (OrmBase): The base class for the DTO
    """
⋮----
utilities_managed: List[UtilityType]
"""
    This is an array of the utility types that Watt manages for this site
    """
⋮----
@validator("utilities_managed")
@classmethod
    def validate_utilities_managed(cls, utilities_managed: List[int])
⋮----
"""Validate that the managed utilities are present and supported"""
⋮----
# we only support electricity and gas ATM
⋮----
class CompanyAgreementSetDto(BuildablePydanticBase)
⋮----
"""This is the companies set of agreements DTO
    Args:
        OrmBase (OrmBase): The base class for the DTO
    """
authorized: bool
credit_check: bool
letter_of_authority: bool
terms_and_conditions: bool
smart_meter_agreement: bool
direct_debit_agreement: bool
⋮----
@classmethod
    def check_required_fields(cls, value: bool)
⋮----
"""Check that the required fields are set to True
        Args:
            value (bool): The value of the field (must be True
        Raises:
            ValueError: If the value is not True
        Returns:
            bool: The value
        """
⋮----
class CompanyCreateRequestDto(BuildablePydanticBase)
⋮----
"""This DTO represents the request to create a company, i.e. POST /companies"""
company: CompanyCreateDto
contact: ContactCreateDto
site: CompanySiteCreateDto
agreement_set: CompanyAgreementSetDto
acquisition_info: Optional[CompanyAcquisitionDto]
````

## File: python/app/api/companies/dtos/response.py
````python
class AddressDto(OrmBase)
⋮----
"""Address create dto"""
id: str
postcode: str
county: str
postal_town: str
address: str
address_line_1: str
address_line_2: Optional[str]
house_name: Optional[str]
house_number: Optional[str]
flat_number: Optional[str]
class SoleTraderPersonalAddressDto(OrmBase)
⋮----
"""This DTO is represents a 'previous address' for a sole trader"""
address: AddressDto
"""The address object"""
move_in_date: datetime
"""The date that the sole trader moved into the address"""
move_out_date: Optional[datetime]
"""
    The date that the sole trader moved out of the address
    This is optional, because they may still live there.
    """
⋮----
@validator("move_in_date", "move_out_date", check_fields=False)
@classmethod
    def convert_to_iso_format(cls, value)
⋮----
"""Convert the dates to an ISO (string) format in the response"""
⋮----
class SoleTraderPersonalDetailsDto(OrmBase)
⋮----
"""
    This DTO represents a SoleTraderPersonalDetails
    These details are used for credit scoring, because a sole trader is not
    a limited company.
    """
date_of_birth: datetime
"""The date of birth of the sole trader"""
addresses: List[SoleTraderPersonalAddressDto]
"""The addresses that the sole trader has lived at"""
⋮----
@validator("date_of_birth", check_fields=False)
@classmethod
    def convert_to_iso_format(cls, value)
⋮----
"""Convert the date of birth to an ISO (string) format in the response"""
⋮----
class CompanyResponseDto(OrmBase)
⋮----
"""This is the company information DTO"""
id: Optional[UUID4]
# Must be placed before the unique fields to ensure that the validators are run in the correct order
business_type: BusinessType
registration_number: Optional[str]
charity_number: Optional[str]
name: str
"""The details of the sole trader, if the business type is a sole trader"""
````

## File: python/app/api/companies/exceptions.py
````python
class CompanyEmailVerificationException(Exception)
⋮----
"""Proxy message to send back as bad request"""
class CompanyInSignedContractAndNotInRenewalWindowException(Exception)
⋮----
"""Raised when a company is found with a signed contract but is not within the renewal window."""
class CompanyGetFailedNotPerfectMatchException(Exception)
⋮----
"""Raised when a company was found but the details entered do not match perfectly,
    and we have to send an email rather than create a new company,
    so they can rejoin the session that has not yet signed the contract"""
class CompanyAcquisitionTargetEmailIsInvalidException(Exception)
⋮----
"""Raised when the target email is invalid"""
class InvalidSessionException(Exception)
⋮----
"""Raised when the session is invalid"""
````

## File: python/app/api/companies/services.py
````python
""" Service for managing companies"""
⋮----
"""Check if the email verification code is current"""
⋮----
# Stephen: Return true for development to allow postman tests to pass
⋮----
verification = await get_company_email_verification_by_email(email, session, code)
⋮----
"""Get a company by business type"""
# don't need to handle else as the business_type only has three values and will fail
# outside of the function if it has an invalid business_type (handled by DTO)
⋮----
"""Store the users hashed target email address and campaign ID into the acquisition table
    Args:
        encoded_target_email (str): The base64 encoded email address
        campaign_id (Optional[str]): The campaign ID
    """
# decode base64 target_id and check if valid email address
target_email = decode_base64_string(encoded_target_email)
# check if the email address is valid
⋮----
"""Send or resend a company email verification code to the user"""
code = generate_random_string(6)
company_email_verification = await get_company_email_verification_by_email(
# upsert instead
⋮----
"""Validate the contract renewal window
    Args:
        company (Company): Company object
        agreement_set_dto (CompanyAgreementSetDto): Agreement set dto
        contact_dto (ContactCreateDto): Contact dto
        quoting_site_address_data (dict): Address data
        mpan (Union[MPAN, None]): MPAN
        mprn (Optional[str]): MPRN
        session (AsyncSession): Session object
    Raises:
        QuotationFailureEmailErrors.ERROR_UPDATING_QUOTE_LIST.exception: Error updating quote list
        CompanyInSignedContractAndNotInRenewalWindowException: Company is in a signed contract and not in renewal window
        CompanyGetFailedNotPerfectMatchException: Company is not a perfect match
        QuotationFailureEmailErrors.COMPANY_CREATE_COMPANY_ALREADY_EXISTS.exception: Company exists but with different site address
    Returns:
        Company: The company if it is in the renewal window
    """
contact: CompanyContact = company.contacts[0] if company.contacts else None
site: CompanySite = company.sites[0] if company.sites else None
# Check if the quoting site is different to the existing site
# We currently do not support multi site therefore if the site is different we throw an error and send internal quote failure email so that SA can handle it
⋮----
# Normalize and compare postcodes
site_postcode = site.address_object.postcode.strip().lower().replace(" ", "")
quote_postcode = (
# Normalize and compare display name
# Shallow comparison using address display name we should really be using CRM's entity address guid but we currently don't support this
site_address_display_name = (
quote_address_display_name = (
⋮----
# collect latest contracts for company for the selected utilities
latest_contracts_by_utility: dict[UtilityType, Contract] = {}
⋮----
utility_contract = await get_contract(
⋮----
# Checks if the previous agreement set is different
# to the current one and if so delete the previous quote list
# only if the user has not signed the contract associated with that quote
# TODO: turn these into validation functions
⋮----
# Agreements have changed, so must delete
# previous quotes and create new ones
⋮----
quote_list = await find_available_quote_list_by_utility_type(
⋮----
# check if the users contract is signed and within
# the renewal window (240 days before contract end date)
⋮----
# Convert to date objects to ignore time components for comparison
today = date.today()
end_date = utility_contract.end_date.date()
# Calculate days between today and end date
days_until_end = (end_date - today).days
today_is_before_renewal_window = days_until_end > RENEWAL_WINDOW_DAYS
# Raise exception if contract is signed and not yet in renewal window
⋮----
# TODO (Stephen): Lookup renewal window by provider_id 'utility_contract.quote.provider_id'
# TODO (Stephen): some suppliers have different offsets take 180 some 240, 365, some 400
# TODO (DC): add historical offset table to suppliers (historical so it
#  can handle transitional periods between company policy changes)
⋮----
# Return existing company allowing user to continue previous session
# if company details match and are identical
⋮----
# TODO (Stephen): Changes needed here for multi site
⋮----
# If the user resubmits we need to update the agreements
⋮----
# If the user is quoting utilties that are not in the existing site.utilies_managed then we need to add them
⋮----
# Send email to user to rejoin existing company if the company details
# fuzzy match and are not identical
⋮----
"""Creates a company if it doesn't exist
    or retrieves it if it exists with the same details
    Args:
        company_create_dto (CompanyCreateRequestDto): The company create dto
        session (AsyncSession): The database session
    Raises:
        QuotationFailureEmailErrors.COMPANY_CREATE_MALFORMED_REQUEST.exception: Malformed request
        CompanyEmailVerificationException: Email verification failed
        QuotationFailureEmailErrors.COMPANY_CREATE_INVALID_VERIFICATION_CODE.exception: Invalid verification code
        ValueError: Invalid business type
        CompanyInSignedContractAndNotInRenewalWindowException: Company is in a signed contract and not in renewal window
        CompanyGetFailedNotPerfectMatchException: Company is not a perfect match
    Returns:
        Tuple[Company, CompanySite, CompanyContact]: The company, site and contact
    """
company_dto = company_create_dto.company
site_dto = company_create_dto.site
agreement_set_dto = company_create_dto.agreement_set
contact_dto = company_create_dto.contact
selected_utilities = site_dto.utilities_managed
# TODO: this could be extracted into a decorator
⋮----
address_data = None
⋮----
address_data = await get_address_by_address_id(site_dto.address_id)
⋮----
postcode = address_data.get("postcode", None)
⋮----
mpans = []
mprns = []
⋮----
bottom_line_mpans = address_data.get("mpans", [])
mprns = address_data.get("mprns", []).copy()
# TODO: Convert to list comprehension
⋮----
additional_details = await get_mpan_additional_details(
⋮----
# We catch the error so the failed mpan lookup does not block the flow
⋮----
company = await get_company_by_business_type(company_dto, session)
⋮----
valid_company = await validate_existing_company_and_contract_renewal_window(
⋮----
# Company doesn't exist create it
company = await _create_company_from_dto(
⋮----
"""Set company acquisition info
    Args:
        company (Company): The company
        acquisition_info (CompanyAcquisitionInfo): The acquisition info
        session (AsyncSession): The database session
    Returns:
        Company: The company
    """
⋮----
"""Get company acquisition info
    Args:
        campaign_id (str): The campaign id
        target_email (str): The target email
        session (AsyncSession): The database session
    Returns:
        CompanyAcquisitionInfo: The company acquisition info
    """
filters = [
acquisitions = await get_all_company_acquisition_info_by_filter(session, filters)
⋮----
"""Creates a company from a CompanyCreateRequestDto"""
company_dto = getattr(company_create_dto, "company", None)
address = getattr(company_dto, "address", None)
base_company_args = {
⋮----
# This main address object from the company dto is incomplete and requires
# the 'get_address_by_address_id' call below to get the full object
# "main_address_object": address,
⋮----
# Get the company address entity in full
⋮----
address_data = await get_address_by_address_id(address.id)
⋮----
address_entity = EntityAddress(
# Update the base_company_args with an address object
⋮----
create_company_fn = _get_create_company_fn(company_dto.business_type)
company: Company = await create_company_fn(company_dto, session, base_company_args)
⋮----
def _get_create_company_fn(business_type: BusinessType) -> Callable
⋮----
"""Returns the function to create a company based on the business type"""
⋮----
"""Creates a LtdCompany from a CompanyCreateDto"""
existing_company = await get_ltd_company_by_registration_number(
⋮----
company_lookup_dto = await get_companies_house(company_dto.registration_number)
# get_industry_by_id for each sic_code in company_lookup_dto.sic_codes
industries = (
# potentially has None values which create a db error
industries = list(filter(lambda x: x is not None, industries))
⋮----
"""Creates a SoleTraderCompany from a CompanyCreateDto"""
⋮----
date_of_birth = company_dto.soletrader_personal_details.date_of_birth
⋮----
addresses = []
⋮----
address_data = await get_address_by_address_id(address_dto.address_id)
⋮----
"""Creates a CharityCompany from a CompanyCreateDto"""
existing_company = await get_charity_company_by_charity_number(
````

## File: python/app/api/companies/views.py
````python
companies = Blueprint("companies", url_prefix="/companies")
⋮----
"""Create a company"""
# The session is checked by the @route decorator
⋮----
company = await create_or_get_company(dto, session)
⋮----
# pass on any actual QuoteFailureException
⋮----
# TODO (James) this can't use the `@send_internal_email_on_quote_failure_exceptions` decorator because
# company creation is a special case where we don't have a company ID yet
ex = QuotationFailureEmailErrors.COMPANY_CREATE_FAILED.exception()
⋮----
# create a new session for the new company
# rather than modifying the existing session
⋮----
# create a session in the Redis cache
# TODO (James) this should live in a service file
⋮----
# set the company ID on the request context for any middleware that needs it
⋮----
decoded_target_id = base64.b64decode(
acquisition = await get_company_acquisition_info(
⋮----
# put the company onto the response - done here to preserve cookies on response
⋮----
"""
    When a user lands on the company page via an acquisition link,
    we need to record that in the acquisition table
    """
⋮----
# TODO (Stephen): openapi response
⋮----
"""Send the initial email verification to the company contact email"""
⋮----
company_email_verification: CompanyEmailVerification = (
resend = bool(company_email_verification)
# send email verification code
⋮----
"""Verify the email verification code"""
email = dto.email
code = dto.code
# check the email verification code is correct
⋮----
valid = await check_company_email_verification_code(email, code, session)
````

## File: python/app/api/contracts/dtos/exceptions.py
````python
class GetPdfContentsException(Exception)
⋮----
"""Proxy message to send back as bad request"""
class GenerateSignedLOAException(Exception)
⋮----
"""Raised when the signed LOA could not be generated"""
class GeneratePDFContractException(Exception)
⋮----
"""Raised when the PDF contract could not be generated"""
class SignPDFContractException(Exception)
⋮----
"""Raised when the PDF contract could not be signed"""
````

## File: python/app/api/contracts/dtos/request.py
````python
"""This module contains the request DTOs for the contracts API"""
⋮----
class SubmitUsageDto(BaseModel)
⋮----
"""This DTO represents a request to submit usage"""
utility_type: UtilityType
total_annual_usage: Optional[float]
start_date: Optional[datetime]
usage: Optional[list] = list()
provider_id: Optional[UUID4]
non_watt_provider_name: Optional[str] = None
mpan: Optional[str]
mprn: Optional[str]
⋮----
@validator("utility_type")
@classmethod
    def utility_type_is_in_choices(cls, value)
⋮----
"""Check that the utility type is in the choices"""
⋮----
class ContractSigningDto(BaseModel)
⋮----
"""This DTO represents a request to sign a contract"""
id: str
signature: str
comments: Optional[str]
````

## File: python/app/api/contracts/dtos/response.py
````python
"""Contracts response DTOs."""
⋮----
class _RegistrationFlowContractData(OrmBase)
⋮----
"""Base class for the registration flow contract data"""
period: Optional[int]
total_annual_usage: float
provider_id: Optional[UUID4]
non_watt_provider_name: Optional[str] = None
mpan: Optional[str]
mprn: Optional[str]
⋮----
@root_validator
@classmethod
    def check_provider_id_or_non_watt_provider_name_exists(cls, values)
⋮----
"""Check that either provider_id or non_watt_provider_name exists"""
⋮----
class RegistrationFlowContractResponseDto(
⋮----
"""This DTO represents the contract data for the registration flow"""
def dict(self, **kwargs)
⋮----
"""Override the dict method to hide the mpan"""
dictionary = super().dict()
⋮----
# TODO: Olly - discuss: should we hide the mprn?
⋮----
class ContractResponseDto(OrmBase, TimeRangeMixin)
⋮----
"""This DTO represents a contract"""
id: UUID4
quote: QuoteDto
is_signed: bool
signature: Optional[str]
utility_type: int
status: int
class ContractSigningResponseDto(BaseModel)
⋮----
"""This DTO represents a request to sign a contract"""
id: str
signature: str
comments: Optional[str]
````

## File: python/app/api/contracts/services.py
````python
"""Services for the contracts module"""
⋮----
"""Get the contract for a company by utility type
    Args:
        utility (UtilityType): Utility type to get contract for
        company_id (str): Company id to get contract for
        session (AsyncSession): database session
        settings (DefaultSettings): application settings
        quote_id (str, optional): Quote id to get contract for. Defaults to None.
    Returns:
        Contract: Contract for the company
    """
⋮----
now_date = datetime.now()
other_filters = [
⋮----
contract = await get_contract(
⋮----
check_date = datetime.now() - relativedelta(
⋮----
"""Get active contracts for a company
    Args:
        company_id (str): Company id to get contracts for
        session (AsyncSession): database session
        settings (DefaultSettings): application settings
        quote_id (str, optional): Quote id to get contract for. Defaults to None.
    Returns:
        List[Contract]: List of active contracts for the company
    """
⋮----
other_filters = []
⋮----
contracts = await get_contracts_for_company(
⋮----
"""Generate a presigned URL for the S3 bucket
    Args:
        key (str): Key to generate presigned URL for
        bucket (str): Bucket to generate presigned URL for
        settings (DefaultSettings): application settings
        expiration (int, optional): Expiration time in seconds. Defaults to 300. Maximum is 43200 (12 hours)
    Returns:
        str: Presigned URL
    """
⋮----
s3 = boto3.client(
⋮----
async def get_pdf_contents(key: str, contract_variant: str, settings: DefaultSettings)
⋮----
"""Get the contents of the PDF file from the S3 bucket
    Args:
        key (str): Key to get contents for
        contract_variant (str): Contract variant (unsigned or signed)
        settings (DefaultSettings): application settings
    Raises:
        GetPdfContentsException: Error getting PDF contents
    Returns:
        bytes: PDF contents
    """
bucket = (
⋮----
response = s3.get_object(Bucket=bucket, Key=key)
pdf_content = response["Body"].read()
````

## File: python/app/api/contracts/views.py
````python
contracts = Blueprint("contracts", url_prefix="/contracts")
⋮----
@route(contracts, "GET", "/", with_db_session=True)
@session_decorators.with_company()
@send_internal_email_on_quote_failure_exceptions
async def get_contracts(request, company: Company, session: AsyncSession)
⋮----
"""Get all contracts for a given company"""
⋮----
utilities = await validate_utilities_params(request.args.getlist("utilities"))
is_signed_param = request.args.get("is_signed")
is_signed = (
contracts = []
# collect latest contracts for company by utility type
latest_contracts_by_utility: dict[UtilityType, Contract] = {}
⋮----
utility_contract = await get_contract(
⋮----
contract_data: Union[Contract, None] = latest_contracts_by_utility.get(
⋮----
# this will eventually change when we support multiple utility types
⋮----
# Utility params are invalid
⋮----
"""Get contract by Contract ID"""
⋮----
contract = await get_contract(
⋮----
"""Get the URL for the PDF of the specified contract"""
⋮----
contract_variant = "signed" if contract.is_signed else "unsigned"
⋮----
settings = request.app.ctx.settings
# Generate an ETag based on contract ID and timestamp
etag = f"{contract.id}-{int(datetime.now().timestamp())}"
if_none_match = request.headers.get("If-None-Match", "")
⋮----
# Get the contents of the PDF file from the S3 bucket
pdf_contents = await get_pdf_contents(contract.pdf_url, contract_variant, settings)
# Calculate the expiration time for cache (1 hour from now)
expires = datetime.now() + timedelta(hours=1)
# Return the contents of the PDF file with caching headers
response = raw(pdf_contents, content_type="application/pdf")
response.headers["Cache-Control"] = "max-age=3600"  # 3600 seconds = 1 hour
⋮----
"""Creates a contract from the specified quote"""
# this is enforced within with_company decorator, but good to do it here too
⋮----
quote_list = await find_available_quote_list_by_id(
⋮----
quote: Quote = quote_list.quotes[quote_index]
⋮----
# TODO (Stephen): Refactor code in this view action into a service method.
quoted_provider = await get_provider_by_id(quote.provider_id, session=session)
existing_contract = await get_contract_by_utility(
⋮----
# if available unsigned contract doesn't match currently selected -> update selected id
⋮----
# not in signed step -> return found contract
⋮----
current_utility_usage = await find_current_utility_usage(
⋮----
contract = await create_contract_from_quote(
⋮----
industries = ", ".join([f"{i.name} ({i.id})" for i in company.industries])
⋮----
industries = "------"
text_registration_number = company.registration_number
⋮----
# no industry name for sole trader
⋮----
text_registration_number = company.charity_number
⋮----
# no registration number for sole trader
⋮----
text_registration_number = "------"
⋮----
contact = company.primary_contact
usage_info = await find_current_utility_usage(
⋮----
# TODO (Stephen): Need to show a
# 'sorry something went wrong' screen to user if this ever happened
⋮----
current_provider: Provider = await get_provider_by_id(
⋮----
# If this happens then we're missing a provider
⋮----
# Stephen: check if pdf_url is not already populated and contract not is_signed
⋮----
# System to cancel "drop-off-quote-signup" job if user selects a quote
⋮----
pdf_key = create_pdf_for_contract(
⋮----
contract = await update_contract_pdf_key(
⋮----
# new contract update selected id with new pdf key
⋮----
"""Sign the specified contract."""
⋮----
contract: Contract = await get_contract(
⋮----
# TODO (James) this does not support contracts that are signed for more/less than 1 year
#           instead, we should add some `active(datetime) -> bool` method to the contract model
⋮----
usage = await find_current_utility_usage(
provider = await get_provider_by_id(contract.quote.provider_id, session)
⋮----
# If this happens then we are missing a provider
⋮----
# (Jude): the comments length was likely too long
⋮----
# we need to sign the LOA again, because it now runs for the length of the contract
loa_pdfKey = create_signed_loa(
⋮----
# TODO (James) support multiple contacts
⋮----
# TODO (James) tidy up unsigned contracts
loa_URL = generate_presigned_s3_url(
contract_URL = generate_presigned_s3_url(
# TODO (James) remove these logs once we're confident the emails are working
⋮----
usage,  # mpan should not be sensored
⋮----
# Cancel the "Sign Contract" reminder email, as the contract has been signed
````

## File: python/app/api/providers/dtos.py
````python
class ProviderDto(OrmBase)
⋮----
"""Provider DTO for API responses."""
id: UUID4
name: str
logo_file_name: str
is_displayed_on_current_supplier_list: bool
udcore_id: str
````

## File: python/app/api/providers/views.py
````python
providers = Blueprint("providers", url_prefix="/providers")
⋮----
@route(providers, "GET", "/", with_db_session=True)
@session_decorators.with_company()
@send_internal_email_on_quote_failure_exceptions
async def get_providers(request: Request, company: Company, session: AsyncSession)
⋮----
"""Get all providers who supply the given utility types."""
⋮----
ut = []
⋮----
utility_type = int(utility_type)
⋮----
query = {"utilities_provided": {"$all": ut}}
all_providers = await get_all_providers(session=session)
⋮----
"""Get a provider by id."""
provider = await get_provider_by_id(provider_id, session=session)
````

## File: python/app/api/public/dtos/request.py
````python
class CompanyBankingDetailsDto(OrmBase)
⋮----
"""This is the company banking details DTO"""
bank_name: str
account_holder_name: str
account_number: str
sort_code: str
contract_id: Optional[str]
⋮----
@validator("account_number")
@classmethod
    def validate_account_number(cls, account_number)
⋮----
"""Validate that the account number is a UK account number"""
⋮----
@validator("sort_code")
@classmethod
    def validate_sort_code(cls, sort_code)
⋮----
"""Validate that the sort code is a UK sort code"""
````

## File: python/app/api/public/dtos/response.py
````python
class CompanyBankingDetailsResponseDto(OrmBase)
⋮----
"""This is the company banking details DTO"""
bank_name: str
account_holder_name: str
account_number: str
sort_code: str
⋮----
@validator("account_number")
    def validate_account_number(cls, account_number)
⋮----
"""Validate that the account number is a UK account number"""
⋮----
@validator("sort_code")
    def validate_sort_code(cls, sort_code)
⋮----
"""Validate that the sort code is a UK sort code"""
````

## File: python/app/api/public/services.py
````python
"""
    Create a new CompanyBankingDetails for the given company id
    TODO (James) This should be moved to the Company model (DDD)
                e.g. company.create_bank_account_details(company_banking_dto)
    :param company_banking_dto: The DTO containing the banking details
    :param company_id: The id of the company to create the banking details for
    :param session: The database session
    :return: The newly created CompanyBankingDetails
    """
company = await get_company_by_id(company_id, session=session)
# verified_at should only be set if the bank account is verified
verified_at = datetime.now() if is_verified else None
bank_account_detail = CompanyBankingDetails(
````

## File: python/app/api/public/views.py
````python
banking_details = Blueprint("banking", url_prefix="/banking")
public = Blueprint.group(banking_details, url_prefix="/public")
⋮----
@route(banking_details, "POST", "/", with_db_session=True)
@session_decorators.with_company()
@send_internal_email_on_quote_failure_exceptions
async def create_banking(request: Request, company: Company, session: AsyncSession)
⋮----
"""Create banking details"""
company_id = str(company.id)
⋮----
company_banking_details_dto: CompanyBankingDetailsDto = (
⋮----
company_banking_details = await create_bank_account_details(
````

## File: python/app/api/quotes/dtos/request.py
````python
class QuoteDto(OrmBase)
⋮----
"""
    A generic quote
    This will have more functionality added to it in the future, as we add more quote types
    """
id: UUID4
"""
    The utility type of this quote
    1 = Electricity
    """
utility_type: UtilityType
⋮----
@validator("end_date", check_fields=False)
@classmethod
    def convert_to_iso_format(cls, value)
⋮----
"""Convert the end date to an ISO (string) format in the response"""
⋮----
@classmethod
    def from_orm(cls, model)
⋮----
# if the model has an electricity utility type, defer to ElectricityQuoteDto
⋮----
class ElectricityQuoteDto(QuoteDto)
⋮----
"""
    An electricity quote
    """
"""
    This quote list is for electricity
    """
utility_type: UtilityType = UtilityType.ELECTRICITY
provider_id: UUID4
annual_price: float
day_unit_rate: float
evening_unit_rate: Optional[float]
night_unit_rate: Optional[float]
weekend_unit_rate: Optional[float]
off_peak_unit_rate: Optional[float]
standing_charge: float
capacity_charge_kva: Optional[str]
price_guaranteed: float
contract_type: str
duration: int
# TODO (James) should this go in the base class?
end_date: datetime
is_comparison_provider: bool
⋮----
# if the model is not an ElectricityQuote, defer to QuoteDto
⋮----
# TODO: once we start supporting more quote types, we may need to split this file up
# TODO: when adding another utility review fields which are the same that can be extracted into base model
class GasQuoteDto(QuoteDto)
⋮----
"""A gas quote"""
utility_type: UtilityType = UtilityType.GAS
⋮----
unit_rate: float
````

## File: python/app/api/quotes/dtos/response.py
````python
class QuoteListResponseDto(OrmBase, TimeStampMixin)
⋮----
"""
    The unique identifier of this quote list
    """
id: UUID4
"""
    The quotes in this quote list
    """
quotes: List[QuoteDto]
"""
    The utility type of this quote list
    """
utility_type: UtilityType
⋮----
@classmethod
    def from_orm(cls, model)
⋮----
# if the model has an electricity utility type, defer to ElectricityQuoteListDto
⋮----
class ElectricityQuoteListResponseDto(QuoteListResponseDto)
⋮----
"""An electricity quote list"""
"""
    This quote list is for electricity
    """
utility_type: UtilityType = UtilityType.ELECTRICITY
⋮----
quotes: List[ElectricityQuoteDto]
"""
    The total annual usage of electricity in kWh which was used to generate this quote list
    TODO (James) this should be banded
    """
electricity_total_annual_usage: Optional[float]
"""The proportional splits of usage in each tariff"""
electricity_tariff_usage_splits: Optional[UsageTariffs]
"""The actual usage in kWh of each tariff split"""
electricity_tariff_usage_values: Optional[UsageTariffs]
⋮----
# if the model is not an ElectricityQuoteList, defer to QuoteListDto
⋮----
class GasQuoteListDto(QuoteListResponseDto)
⋮----
"""An gas quote list"""
utility_type: UtilityType = UtilityType.GAS
⋮----
quotes: List[GasQuoteDto]
"""
    The total annual usage of gas which was used to generate this quote list
    """
gas_total_annual_usage: Optional[float]
````

## File: python/app/api/quotes/common.py
````python
"""Evaluates the ud_core_id and smart_meter_agreement to determine if the provider is quotable. If so, returns the provider otherwise returns None"""
provider: Union[Provider, None] = next(
⋮----
"""Generate quote by utility type"""
# We store the term in months, so if the API returns it in years we convert it to months
⋮----
term = int(rate["Term"]) * 12
⋮----
term = int(rate["Term"])
⋮----
is_comparison_provider=False,  # Set to false in electricity
````

## File: python/app/api/quotes/electricity_services.py
````python
"""Create electricity quote list from ud quotes with current supplier marked with is_comparison_provider."""
quotes: List[Quote] = []
# Dictionary to track unique ud_core_ids that are ignored along with a reason for exclusion
ignored_info = {}
# DO NOT move this into the for loop
all_providers = await get_all_providers(session=session)
⋮----
ud_core_id = rate["Supplier"]
provider = await check_udcore_provider_quotability(
⋮----
# Provider not found or not quotable.
⋮----
# We have an issue where the quote for a supplier will not be generated if the below issue is triggered.
# udcore_id: "Utilita" pulls back the first result with electra_link_id: "0005"
# Which does not have a supplier_template_keys.py value associated with it.
# FIX we should remove bogus entries in the providers.ts that do not have a supplier_template_key mapping for it.
# Or add the supplier_template_keys mapping for it back (quoted out by James many moons ago)
⋮----
message = (
⋮----
# Ensure the provider provides electricity.
⋮----
message = f"Provider '{provider.name}' ('{provider.electra_link_id}') does not provide ELECTRICITY"
⋮----
# Generate the electricity quote if all checks pass.
electricity_quote = _generate_quote_by_utility(
⋮----
# Log the detailed information for ignored rates.
⋮----
# Temp solution to always add the current supplier as a comparison supplier.
junk_sample_duration = 1
junk_sample_date = datetime.now() + relativedelta(years=1)
⋮----
contract_type="",  # Contract type is not known.
⋮----
quote_list = ElectricityQuoteList(
````

## File: python/app/api/quotes/gas_services.py
````python
"""Create gas quote list from ud quotes with current supplier marked with is_comparison_provider."""
quotes: List[Quote] = []
# Dictionary to track unique ud_core_ids of rates that are ignored along with a reason for exclusion
ignored_info = {}
# DO NOT move this into the for loop
all_providers = await get_all_providers(session=session)
⋮----
ud_core_id = rate["Supplier"]
provider = await check_udcore_provider_quotability(
⋮----
# Provider not found or not quotable.
⋮----
message = f"Missing GAS template key for provider '{provider.name}' ('{provider.electra_link_id}')"
⋮----
message = (
⋮----
# Ensure the provider provides gas.
⋮----
message = f"Provider '{provider.name}' ('{provider.electra_link_id}') does not provide GAS"
⋮----
# Generate the gas quote if all checks pass.
gas_quote = _generate_quote_by_utility(
⋮----
# Log the detailed information for ignored rates.
⋮----
# Temp solution to always add the current supplier as comparison supplier.
junk_sample_duration = 1
junk_sample_date = datetime.now() + relativedelta(years=1)
⋮----
quote_list = GasQuoteList(
````

## File: python/app/api/quotes/services.py
````python
"""Get the quote list for the specified utility type"""
⋮----
"""Get a quote by its quote id"""
⋮----
"""Create quote list from ud quotes with current supplier marked with is_comparison_provider"""
# Incoming ud_quotes = ud_quotes["GetElectricRatesResult"]["Rates"]
# or ud_quotes["GetGasRatesResult"]["Rates"]
⋮----
UTILITY_TYPE_MAPPING = {
quote_list = await UTILITY_TYPE_MAPPING[utility_type](
⋮----
"""Return a quote list for a company if one exists otherwise create a new one."""
usage = await find_current_utility_usage(
⋮----
provider: Provider = await get_provider_by_id(
⋮----
# If this happens then we're missing a provider
⋮----
# check if we have any saved quotes from the company
existing_quote_list = await find_available_quote_list_by_utility_type(
⋮----
# return the existing quote list if it is not expired
⋮----
# archive the existing quote list if expired then generate new quotes
⋮----
"""Return a quote list for a company if one"""
⋮----
"""Generate new quotes for a given company and utility."""
## Unprocessed
ud_quotes = await get_utility_prices(
⋮----
)  # generate quotes using UDCore API
⋮----
# TODO (Stephen): if we don't have any ud_quotes coming back we need to ensure we show an error to user
⋮----
latest_agreement_set: CompanyAgreementSet = company.latest_agreement_set
quote_list = await create_quote_list_from_ud_quotes(
⋮----
# If we no no actionable quotes then shift to +14 days and try again
⋮----
# Early return same as above
⋮----
# this is required so that quote_list.id and quote_list.quotes[i].id are populated
⋮----
async def get_company_credit_score(company: Company, session: AsyncSession) -> int
⋮----
"""Get the credit score for a company"""
⋮----
company: LtdCompany = company
# Check if user has previously submitted usage page and retrieve existing credit score
existing_score = await get_credit_score_by_company_id(
⋮----
# otherwise we need to do a new credit score lookup
⋮----
# get credit score from experian
credit_score = invoke_get_credit_score_lambda(
⋮----
credit_score = DEFAULT_CREDIT_SCORE
⋮----
# TODO (Stephen): This should be move to the service layer
score = company.create_credit_score(credit_score=credit_score)
````

## File: python/app/api/quotes/views.py
````python
quotes = Blueprint("quotes", url_prefix="/quotes")
⋮----
@route(quotes, "GET", "/", with_db_session=True)
@session_decorators.with_company()
@send_internal_email_on_quote_failure_exceptions
async def get_quotes(request: Request, company: Company, session: AsyncSession)
⋮----
"""Get existing quote lists for the given utility types, for the current company."""
company_id = str(company.id)
⋮----
utilities = await validate_utilities_params(request.args.getlist("utilities"))
⋮----
quote_lists = []
⋮----
quote_list = await find_available_quote_list_by_utility_type(
⋮----
# flake8: noqa: C901
async def create_quotes(request: Request, company: Company, session: AsyncSession)
⋮----
"""Create quotes for the given utility types, for the current company."""
# this is enforced within with_company decorator, but good to do it here too
⋮----
# Only one utility is passed in the request arg but this validation checks and returns a list
utilities = await _validate_request_utilities(request)
credit_score = await get_company_credit_score(company, session=session)
quote_list: List[Dict[str, Any]] = []
⋮----
# TODO (Bidur): We only process one utility and although this loop is redundant, to prevent unwanted errors we'll leave it for now
⋮----
existing_quote_list = await find_available_quote_list_by_utility_type(
⋮----
new_quote_list = await get_or_create_quotes_with_usage(
⋮----
"""Used to schedule the 'choose quote' reminder for Utilities."""
# Stephen contract.pdf_url is not set if user hasn't selected that quote
contract_data = await get_contract_by_utility(
⋮----
# System to schedule "drop-off-quote-signup" If user is shown quotes and did not signup
contact = company.primary_contact
⋮----
industries = ", ".join(
⋮----
industries = "-"
⋮----
text_registration_number = await _get_company_text_registration_number(
usage = await find_current_utility_usage(
⋮----
provider = await get_provider_by_id(
⋮----
# If this happens then we're missing a provider
⋮----
company: LtdCompany = company
⋮----
company: CharityCompany = company
⋮----
company: SoleTraderCompany = company
⋮----
async def _validate_request_utilities(request: Request)
````

## File: python/app/api/reports/mapper.py
````python
""" Mapper module for the reports API. """
⋮----
QUOTE_OBJECT_MAP = {
def _get_utility_type_value(utility_type: UtilityType)
⋮----
"""
    Returns a stringified, CSV friendly representation of the utility type.
    Args:
        utility_type (UtilityType): The utility type to convert.
    Returns:
        str: The stringified utility type (e.g. "electricity" or "gas").
    """
⋮----
def _get_company_mpr_by_utility(company: Company, utility_type: UtilityType)
⋮----
"""Gathers the companie's meter point reference (MPAN/MPRN) by the selected utility type.
    These values come from company.primary_site.mpan and company.primary_site.mprn
    """
⋮----
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
⋮----
"""Constructs a quote object by the selected utility type.
    Uses the QUOTE_OBJECT_MAP to gather the correct quote object.
    """
result = {}
⋮----
logger.info(f"Getting attribute for key: {key}")  # logging the key
⋮----
# value is a function, so we can call it to get the default value
⋮----
"""
    Constructs a quote rag object from the given parameters.
    This is used to construct the quote rag object for the reports API.
    TODO: (Olly) - Break down the number of parameters in this function.
    """
company_mpr = _get_company_mpr_by_utility(company, utility_type)
quote_state = await get_quote_rag_state(company, utility_type, session)
⋮----
"""
    Returns a list of acquisitions that do not have a company associated with them.
    This is done by filtering out acquisitions that have a company associated with them,
    and acquisitions with an email that has already been used.
    In production, acquisitions with a Watt email are also filtered out.
    Args:
        acquisitions_for_campaign (list[CompanyAcquisitionInfo]): A list of acquisitions for a campaign.
        companies_with_acquisition_info (list[str]): A list of company ids that have an acquisition associated with them.
        unique_emails (set[str]): A set of emails that have already been used.
    Returns:
        list[CompanyAcquisitionInfo]: A list of acquisitions that do not have a company associated with them.
    """
acquisitions_without_companies: list[CompanyAcquisitionInfo] = []
⋮----
"""Get the quote RAG state for a company
    Args:
        company (Company): Company to get state of
        utility_type (UtilityType): Utility type to know which utility the report is for
        session (AsyncSession): database session
    Returns:
        QuoteRagStates(Enum): Enum of what state it is in
    """
rejoin_information = await get_rejoin_information_for_rag_report(
⋮----
"""
    Maps a list of companies to a list of QuoteRag models.
    Args:
        current_supplier (dict[int, str]): A dictionary mapping company IDs to the current supplier name.
        companies (List[Company]): A list of Company models.
    Returns:
        List[QuoteRag]: A list of QuoteRag models mapped from the Company models.
    """
"""
    TODO (James) the parameters to this function are unclear, specifically the three `str` types on the `companies_data` tuple.
    We should aim to create a named class to represent the `tuple[...]` so it's `companies_data: list[RAGCompanyData]`
    """
companies_with_acquisition_info: list[str] = []
quote_rags: list[QuoteRag] = []
⋮----
contract: Optional[Contract] = getattr(company, "contracts")
quote: Optional[Union[ElectricityQuote, GasQuote]] = (
⋮----
# Skip watt.co.uk emails in prod
⋮----
primary_contact: CompanyContact = company.primary_contact
banking_info: CompanyBankingDetails = (
latest_agreement_set: CompanyAgreementSet = company.latest_agreement_set
soletrader_personal_details: SoleTraderPersonalDetails = getattr(
⋮----
company_aquisition_info = (
⋮----
# TODO (James) fix the lint warnings in the function below by creating the lambda as a named function
# and using functools.partial to compose it with the company/primary_contact
# https://stackoverflow.com/a/********/1916362
⋮----
# Get the company acquisition info if it exists for the campaign_id and company
⋮----
# Usage was found for the company. Put it in the CSV.
# TODO (Bidur): This logic of looping over usages.items() skips gas usage page as the data is not yet submitted. This is known bug and will be looked at when we refactor the code.
quote_rag = await _construct_quote_rag_object(
⋮----
# If there is no usage data for the company we still want to show them in the CSV.
⋮----
usage=None,  # Usage is none here as we don't have any usage data for this company
⋮----
unique_emails = {quote_rag.target_email for quote_rag in quote_rags}
acquisitions_without_companies_list = _get_acquisitions_without_company(
⋮----
quote_rag = QuoteRag(
⋮----
# TODO: (Olly) : Discuss, How can we currently get this value for both gas and electricity?
# (James) it currently isn't possible as without a `Company` object, we don't know what they selected on the homepage.
# consider putting "N/A" here instead to be more accurate.
⋮----
def _are_watt_emails(*emails)
````

## File: python/app/api/reports/services.py
````python
""" This module contains the services for the reports module """
⋮----
"""Get the current supplier for a company
    Args:
        company_id (int): Company id to get supplier for
        session (AsyncSession): database session
    Returns:
        str: Name of the supplier
    """
company_usage = await find_current_utility_usage(
⋮----
# Might not work, reference to utility type
⋮----
supplier = await get_provider_by_id(company_usage.provider_id, session)
⋮----
"""Get the new supplier for a company
    Args:
        company (Company): Company to get supplier for
        session (AsyncSession): database session
    Returns:
        str: Name of the supplier
    """
contract: Contract = company.contracts
⋮----
quote: ElectricityQuote = contract.quote
⋮----
supplier = await get_provider_by_id(quote.provider_id, session)
⋮----
async def _get_utility_usages(company: Company, session: AsyncSession)
⋮----
"""
    Get the utility usages for a company.
    Calls the find_current_utility_usage function for each utility type
    Args:
        company (Company): Company to get utility usages for
        session (AsyncSession): database session
    Returns:
        dict: { UtilityType: usage }
    """
# returns find_current_utility_usage for each utility type { UtilityType: usage }
# if doesnt exist, skip it
utility_usages: dict[Union[ElectricityUsage, GasUsage]] = {}
⋮----
usage = await find_current_utility_usage(company.id, utility_type, session)
⋮----
"""Generate a CSV of companies and their RAG state
    Args:
        campaign_id (Optional[str]): _description_
        session (AsyncSession): _description_
    Returns:
        str: CSV of companies and their RAG state
    """
⋮----
# As the address and other data structures have changed in the gas release, to prevent application failure we are setting a hard date filter
gas_release_date = datetime(2024, 1, 1)
hard_company_created_or_or_after_date_filter = (
hard_company_acquisition_created_on_or_after_date_filter = (
filters = (
companies = await get_all_companies_by_filter(session, filters)
acquisitions_for_campaign = (
mapped_data = await map_company_to_quote_rags(
````

## File: python/app/api/reports/types.py
````python
ELECTRICITY_QUOTE_JSON_MAP = {
GAS_QUOTE_JSON_MAP = {
⋮----
"day_unit_rate": str,  # Set to this so that we can use the same template for both gas and electricity, (wont add any empty fields. )
````

## File: python/app/api/reports/views.py
````python
""" Reports views """
⋮----
reports = Blueprint("reports", url_prefix="/reports")
⋮----
"""Generate a CSV of companies and their RAG state
    Args:
        request (Request): Http request
        session (AsyncSession): database session
    Returns:
        HTTPResponse: CSV file
    """
api_key: Union[str, None] = request.args.get("api_key")
campaign_id: Union[str, None] = request.args.get("campaign_id")
utilities: Union[int, None] = request.args.get("utilities")
⋮----
marketing_campaign_api_key = load_secret(
⋮----
company_rag_csv = await generate_company_rag_csv(
````

## File: python/app/api/security/dtos/request.py
````python
class AuthorizationRejoinRequestDto(BaseModel)
⋮----
"""
    Authorization rejoin request model
    This is the request to rejoin the quote flow for a company.
    The client should send this request to the server, and the server
    will verify the token, set up a session, and return an AuthorizationRejoinResponseDto
    """
"""The token to rejoin with"""
token: str
class AuthorizationRejoinRequestDetailsDto(BaseModel)
⋮----
"""
    Authorization rejoin request details model
    This is base64 encoded inside AuthorizationRejoinRequestDto.token
    The reason we base64 encode it is just to make it a bit nicer to look at,
    it doesn't add any security. The token itself is still encrypted.
    It has to contain the company so that we can look up the company first
    and then check the token against the company.
    """
"""The company ID"""
company_id: str
"""The rejoin token"""
⋮----
"""Contract ID (contract signing page)"""
contract_id: Optional[str]
class AuthorizationRejoinResponseDto(BaseModel)
⋮----
"""
    Authorization rejoin response model
    This is the response to the AuthorizationRejoinRequestDto.
    If there is no error, then the client should redirect to the page
    specified in the response.
    """
"""The error message, if any"""
error: Union[str, None]
"""The page to redirect to, if successful"""
page: Union[str, None]
````

## File: python/app/api/security/dtos/response.py
````python
# (Jude): in the future, we may want to create a response.py for response DTOs (if needed).
# For now, since we aren't returning DTOs in security, we don't need a response.py
````

## File: python/app/api/security/services.py
````python
UTILITY_TYPE_URLS = {
class CompanyRejoinInformation
⋮----
"""Class to store information about a company rejoining the flow"""
page: str
"""The page to redirect to after rejoining."""
selected_utilities: List[int]
"""The utilities that the company has selected to quote for."""
⋮----
"""
    Get the rejoin page for a company, targeting a specific contract.
    This is used when a user is rejoining a drop-off email to a contract signing page.
    Args:
        company (Company): The company to get the rejoin information for.
        contract (Contract): The contract to get the rejoin information for.
        report (bool, optional): Whether or not this is being run for a report. Defaults to False.
    """
# TODO (James) this will need to be updated to handle multi-site.
site = company.primary_site
# defensive programming, contracts can't be created against sites without managing the utility
⋮----
# if the contract is signed, then there is nothing to show the user
⋮----
# this function is called programmatically to determine company state for the RAG report
⋮----
"""Return the page to redirect to after rejoining a session."""
⋮----
# We cannot pass site.utilities_managed to selected_utilities because it can include signed utiltity and frontend would initiate the flow which would eventually fail due to existing quotes and contracts
# Instead we will filter out utilities that have signed contracts and only return the available utilities
available_utilities = []
⋮----
signed_contracts = await get_contract(
⋮----
# Handles the user rejoining to a specific contract, i.e. from a drop-off link to the contract signing page
⋮----
contract = await get_contract(
⋮----
rejoin_page_for_contract = _get_rejoin_page_for_contract(
⋮----
# otherwise we need to look through the outstanding utilities and find the correct landing page
⋮----
# skip utilities that the site doesn't have
⋮----
# load any contracts for the company for this utility
# TODO (James) this will need to be updated to handle multi-site.
⋮----
# if there is a signed contract for this utility, then skip it (nothing for the user to do)
⋮----
# otherwise see if we can get a rejoin page for this contract
⋮----
current_usage = await find_current_utility_usage(
⋮----
# This expects users would have submitted a previous usage.
active_quote_list = await get_quotes_with_usage(
⋮----
# if we reach here, then the user needs to go to the Usage page for their utility
⋮----
# if we reach here, then there are no outstanding utilities, so we can just return the home page for now
⋮----
# contract is complete
⋮----
# Current logic of generating rag report loops through usages.items() to _construct_quote_rag_object
# due to this we have a know bug (accepted for gas release and to be rectified in refactor)
# where when a user is on Usage page the rag report cannot tell which utility type it is as the usage data is not yet submitted by the user
# below we are temporarily injecting "ENTERING USAGE" quote state
⋮----
"""Invalidate a `CompanyEmailAuthToken` by deleting it from the database.
    Args:
        token (CompanyEmailAuthToken): The token to invalidate
        session (AsyncSession): The database session
    """
⋮----
"""
    The "token" on the request is actually base64 encoded, so we need to decode it
    It contains the company ID and the secret token, so we can look up the company
    and check the token against the company (with bcrypt.checkpw)
    The reason we base64 encode it is just to make it a bit nicer to look at,
    it doesn't add any security. The token itself is still encrypted.
    """
⋮----
# decode the base64 token
decoded_token = decode_base64_string(dto.token)
# parse the decoded token (json) into a dto
request_details: AuthorizationRejoinRequestDetailsDto = (
company_id = request_details.company_id
rejoin_token = request_details.token
# TODO (James) this isn't sent by the frontend, whats going on here?
contract_id = None
⋮----
contract_id = request_details.contract_id
⋮----
"""
    Look up a company by ID, and check the rejoin token against the company's
    email auth token.
    If the token is valid, return the company, otherwise return None.
    This will also invalidate the token.
    """
company = await get_company_by_id(company_id, session=session)
````

## File: python/app/api/security/views.py
````python
# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger("httpx")
# logger.setLevel(logging.DEBUG)
authorization = Blueprint("authorization", url_prefix="/authorization")
⋮----
@route(authorization, "GET", "/", check_csrf=False)
async def authorize_browser(request: Request)
⋮----
"""
    Create a new session for the browser if it doesn't have one, or refresh the
    existing session if it does.
    This endpoint is called by the browser when it loads the app, and is used to
    ensure that the browser has a session cookie set. This session cookie is then
    used to identify the browser when it makes requests to the API.
    This uses `check_csrf=False` because we don't want to check the CSRF token
    for this endpoint, because it's called before the CSRF token is set.
    """
⋮----
# TODO (James) handle refresh / recreate session
⋮----
"""
    Rejoin a session for an existing company, using a `CompanyEmailAuthToken`.
    This is used to rejoin a session from a drop-off email. This endpoint will
    set a session cookie for the browser, and contains a redirect URL to the
    page that the user should be taken to.
    """
⋮----
# TODO (James) we should invalidate the user's session because they tried to use a bad token
⋮----
company = await get_company_with_rejoin_token(
⋮----
# Get the page that the user should be redirected to after rejoining
rejoin_information = await get_rejoin_information_for_company(
# If the user is being redirected to home then they should be given a new session
needs_new_session = rejoin_information.page == "/"
⋮----
# Get the existing session for the company, if it exists
company_id = None if needs_new_session else company_id
# Create a new session for the company
⋮----
# set up the response - done here to preserve cookies on response
````

## File: python/app/api/usage/constants.py
````python
"""Usage Constants"""
eac_band_upper_bounds = [5000, 10000, 15000, 20000]
````

## File: python/app/api/usage/services.py
````python
"""Finds the users current contract usage from their session"""
⋮----
current_utility_usage: Union[
⋮----
async def update_mpan(company: Company, mpan: str, session: AsyncSession)
⋮----
"""
    Updates the MPRN for the company's primary site
    TODO (James) this will need changing for multi-site, recommend to use a CompanySite parameter instead.
    """
updated = await update_company_primary_site_mpan(company, mpan, session=session)
⋮----
async def update_mprn(company: Company, mprn: str, session: AsyncSession)
⋮----
updated = await update_company_primary_site_mprn(company, mprn, session=session)
⋮----
"""Get the usage info for a company from Electralinks"""
⋮----
total_eac = _get_total_annual_usage(annual_usage, half_hourly_meter_usage)
⋮----
"""Get the annual and half hourly usage for a company depending on the utility type."""
site = company.primary_site
⋮----
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
mpan = MPAN(site.mpan or dtoMpan) if site.mpan or dtoMpan else None
profile_class = getattr(mpan, "profile_class", None)
core = getattr(mpan, "core", None)
company_address = getattr(company, "address", None)
postcode = getattr(company_address, "postcode", None)
⋮----
annual_usage = await get_gas_meter_by_mprn(site.mprn) if site.mprn else None
half_hourly_meter_usage = None
⋮----
async def get_gas_usage_info_from_aperture(company: Company)
⋮----
"""Get the usage info for a company from Aperture"""
⋮----
total_eac = _get_total_annual_usage(annual_usage, None)
⋮----
"""Get the total annual usage from either the annual usage or half hourly get_gas_usage_and_contract_datameter usage"""
⋮----
total_annual_usage: Union[float, None] = None
⋮----
total_annual_usage = annual_usage.total_eac
⋮----
# If contract eac is None then calculate annual usage from monthly usage data
total_annual_usage = round(
⋮----
# TODO merge `get_electric_usage_and_contract_data` and `get_gas_usage_and_contract_data`?
⋮----
"""Get the electricity usage and contract data for a company"""
company_electricity_usage = await find_current_utility_usage(
# if this is not the first time this company has called this route. E.g submit then go back to the usage page or user trying to quote for the secondth+ time
# problem: if user is trying to quote again then we currently do not send the schedule_current_supplier_information_reminder email because we are unable to tell the difference between the user landing on the usage page from company information or from quote page after clicking back button
⋮----
response_contract = RegistrationFlowContractResponseDto(
provider = await get_provider_by_id(
⋮----
# If this happens then we're missing a provider
⋮----
response_usage = company_electricity_usage.usage
# Check if the meter is a half hourly meter and if so we need to get the half hourly meter usage from Electralinks as we currently do not store this in our db
mpan = MPAN(company.primary_site.mpan) if company.primary_site.mpan else None
⋮----
is_hh_meter = str(profile_class) == "00" if profile_class else False
⋮----
half_hourly_meter_usage = await get_mpan_hhcombined(
response_usage = (
⋮----
# if no usage data found in electrallinks then allow manual entry flow
⋮----
supplier_id = annual_usage.supplier_id
provider = await get_provider_by_electra_link_id(supplier_id, session=session)
⋮----
provider = await create_unsupported_provider(
⋮----
response_contract = {
⋮----
"""Get the gas usage and contract data for a company"""
company_gas_usage = await find_current_utility_usage(
# if this is not the first time this company has called this route. E.g submit then go back to the usage page
⋮----
response_contract = RegistrationFlowContractResponseDto.from_orm(
⋮----
response_usage = company_gas_usage.usage
⋮----
# if no usage data found in aperture then allow manual entry flow
⋮----
supplier_name = getattr(annual_usage, "supplier_name", None)
provider = (
⋮----
# only names we have from the unsupported provider is the supplier name which is the recco_id
⋮----
response_usage = []
⋮----
total_annual_usage_from_db = (
tpr_tariff_count = (
# Usage dto eac is None if NaN i.e banded usage therefore we need to get the total annual usage from the db
total_annual_usage = usage_dto.total_annual_usage or total_annual_usage_from_db
⋮----
# if total annual usage is still None then we need to assign the total annual usage from electralinks
total_annual_usage = total_annual_usage or total_eac
# update the tpr_tariff_count from electralinks
tpr_tariff_count = len(getattr(annual_usage, "tpr_data", []))
⋮----
# TODO: look at usage DTOs as this is a hotfix to remove default MPRN from DTO
usage_dict = usage_dto.dict()
⋮----
mpan = company.primary_site.mpan or usage_dto.mpan
# TODO: need check everytime you repost on this route a new electricity object is created
utility_usage = await create_utility_usage(
⋮----
# if total annual usage is still None then we need to get the total annual usage from aperture
⋮----
total_annual_usage = total_eac
⋮----
# TODO: look at usage DTOs as this is a hotfix to remove default MPAN from DTO
⋮----
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
mprn = company.primary_site.mprn or usage_dto.mprn
````

## File: python/app/api/usage/utils.py
````python
def get_eac_band(eac: float)
⋮----
"""
    Get the EAC band for the given EAC as a tuple of (lower_bound, upper_bound)
    If the upper bound is None, then the EAC is greater than the last upper bound
    e.g. (0, 5000) or (5000, None)
    param eac: The EAC to get the band for
    return: The EAC band as a tuple of (lower_bound, upper_bound)
    """
lower_bound = 0
⋮----
lower_bound = upper_bound
⋮----
def get_eac_band_str(eac: Union[float, None])
⋮----
"""
    Get the EAC band for the given EAC as a string
    if less than 1 its junk so return None, and have the user enter it
    e.g. 1-5000 or 5000+
    param eac: The EAC to get the band for
    return: The EAC band as a string
    """
# Allow user to provide eac if it is zero
⋮----
def get_rounded_year_month_usage(monthly_usage: list[MonthlyUsage])
⋮----
"""
    Round the months usage to the nearest hundred to avoid exposing exactly how much energy is being used
    """
⋮----
def round_to_nearest_hundred(value: float)
⋮----
"""Round the value to the nearest hundred"""
````

## File: python/app/api/usage/views.py
````python
usage = Blueprint("usage", url_prefix="/usage")
GET_USAGE_AND_CONTRACT_DATA = {
POST_USAGE = {
⋮----
"""Get the usage and contract data (to refactor) *by utility type* for the current company."""
# with_company decorator will handle this, but it doesn't hurt to be safe
⋮----
# TODO: (Olly) - consider to refactor the below, utility_type in this case is be a single value, not a list.
utility_type = await validate_utilities_params(request.args.getlist("utility_type"))
⋮----
# not_quotable_reason = company.primary_site.get_not_quotable_reason(
#     utility_type=utility_type[0]
# )
# if not_quotable_reason:
#     logger.info("Company is not quotable")
#     if utility_type[0] == UtilityType.ELECTRICITY:
#         raise QuotationFailureEmailErrors.ERROR_MPAN_NOT_FOUND.exception()
#     if utility_type[0] == UtilityType.GAS:
#         raise QuotationFailureEmailErrors.ERROR_MPRN_NOT_FOUND.exception()
⋮----
# ensure that any QuoteFailureException is re-raised
⋮----
"""Create the contract and usage data"""
⋮----
provider = await get_provider_by_name(
⋮----
utility_usage = await POST_USAGE[dto.utility_type](company, dto, session)
# Only cancelling the drop-off if there is not usage does not make sense
# usage_data = annual_usage or half_hourly_meter_usage
# if not usage_data:
#     # System to cancel "drop-off-current-supplier-information" job if data is not Electralink and and hasn't been submitted yet
# Stephen: We need to know if the user has already submitted data to avoid scheduling the reminder
previously_submitted_contract = await find_current_utility_usage(
⋮----
contact = company.primary_contact
⋮----
# TODO Stephen: test this email is being trigger and has the right content
⋮----
# TODO (Stephen): If user submits usage twice after producing a contract
# Then we can not delete their quotes due to lack of cascade to the contracts
# We likely need to archive the contract table entries with a property `is_archived: true`
quote_list = await find_available_quote_list_by_utility_type(
# If there is an existing quote list for the client, clear it when the usage is updated, to prevent risk of stale data.
⋮----
res = RegistrationFlowContractResponseDto(
# TODO the ORM/DTO should handle this
````

## File: python/app/api/blueprint.py
````python
api_blueprint = Blueprint.group(
````

## File: python/app/common/aws/secrets_retrieval.py
````python
def get_secret(secret_name) -> dict[str, str]
⋮----
"""Get a secret from AWS Secrets Manager"""
session = boto3.session.Session()
client = session.client(
⋮----
# Call the Secrets Manager API to retrieve the secret value
get_secret_value_response = client.get_secret_value(SecretId=secret_name)
⋮----
# Secrets Manager API call failed, raise an error
⋮----
# Secrets Manager API call succeeded, parse the secret value
⋮----
secret = get_secret_value_response["SecretString"]
⋮----
secret = get_secret_value_response["SecretBinary"]
````

## File: python/app/common/db/connection.py
````python
def connect_postgres_db(app: Sanic)
⋮----
"""
    Connect to the database and create a session factory
    These are then added to the app context so they can be used by other parts of the app
    :param app: Sanic app
    """
settings: DefaultSettings = app.ctx.settings
⋮----
portal_db_url = f"{settings.PORTAL_DB_USED}://{settings.PORTAL_DB_USER}:{settings.PORTAL_DB_PASSWORD}@{settings.PORTAL_DB_HOST}:{settings.PORTAL_DB_PORT}/{settings.PORTAL_DB_NAME}"
engine = create_async_engine(
⋮----
async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)
⋮----
async def disconnect_postgres_db(app: Sanic)
⋮----
"""Disconnect from the database"""
⋮----
portal_db = app.ctx.portal_db
````

## File: python/app/common/db/session.py
````python
def with_db_session(function: Callable) -> Callable
⋮----
"""
    Ensure that the function is called with a database session, if it is not then create one and pass it in.
    This fuction is a decorator and should be used as follows:
    ```
    @with_db_session
    async def my_function(session):
        ...
    ```
    You must also ensure that the function has a session parameter, this is where the session will be passed in.
    This decorator must be called before any other decorators which require a session.
    """
⋮----
@wraps(function)
    async def wrapper(*args, **kwargs)
⋮----
session = kwargs.get("session")
⋮----
async_session = sanic_app.ctx.portal_db_session
````

## File: python/app/common/local_mode_data/britishgas.py
````python
recent_end_date = (datetime.now() + relativedelta(years=-1, days=90)).strftime(
LOCAL_MODE_POSTCODE_SEARCH_EH6_5DY = JSONTemplateRenderer.render_template(
LOCAL_MODE_UDPRN_POSTCODE_SEARCH_EH6_5DY_906338794 = (
````

## File: python/app/common/local_mode_data/electralink.py
````python
"""Electralinks local data"""
⋮----
# TODO (DC): AL1 1AG is the default postcode the details below relate to;
#  find a way to express it in constants' names etc
LOCAL_MODE_MPAN = "1015680495647"
recent_end_date = (datetime.now() + relativedelta(years=-1, days=90)).strftime(
LOCAL_MODE_MPAN_ADDITIONAL_DETAILS = JSONTemplateRenderer.render_template(
LOCAL_MODE_MPAN_EAC = JSONTemplateRenderer.render_template(
LOCAL_MODE_MPAN_HH_COMBINED = JSONTemplateRenderer.render_template(
LOCAL_MODE_MPAN_FULL_TPR = JSONTemplateRenderer.render_template(
````

## File: python/app/common/local_mode_data/experian_aperture.py
````python
"""ExperianAperture local data."""
⋮----
LOCAL_MODE_MPRN_LOOKUP_METER_ADDITIONAL_DETAILS_8901264506 = (
LOCAL_MODE_MPRN_KEY_POSTCODE_SEARCH_KW17_2AB = JSONTemplateRenderer.render_template(
LOCAL_MODE_MPRN_KEY_POSTCODE_SEARCH_OX11_8RJ = JSONTemplateRenderer.render_template(
# `mprn_key` to MPRN details for given postcode
LOCAL_MODE_MPRN_DETAILS_AL1_1AG = {
⋮----
# AL1 1AG - 1 Pullman Close, ST. ALBANS, Hertfordshire
⋮----
# AL1 1AG - 2 Pullman Close, St. Albans, Hertfordshire
⋮----
# AL1 1AG - 3 Pullman Close, St. Albans, Hertfordshire
⋮----
# AL1 1AG - 4 Pullman Close, St. Albans, Hertfordshire
⋮----
# AL1 1AG - 5 Pullman Close, ST. ALBANS, Hertfordshire
⋮----
# AL1 1AG - 6 Pullman Close, ST. ALBANS, Hertfordshire
````

## File: python/app/common/local_mode_data/lambdas.py
````python
send_quote_failure_email = {}
send_contract_completion_email = {}
send_email_verification_code = {}
send_rejoin_existing_fuzzy_matched_company_email = {}
notify_scheduler_start = {}
notify_scheduler_stop = {}
````

## File: python/app/common/local_mode_data/pdf_as_bytearray.py
````python
def load_local_pdf_bytearray(filename: str)
⋮----
"""Load a local PDF file as a bytearray.
    Args:
        filename (str): The name of the PDF file to load.
    Returns:
        A bytearray object containing the contents of the PDF file.
    """
path = Path(__file__).parent / filename
````

## File: python/app/common/local_mode_data/udcore.py
````python
electricprices = {
gasprices = {
````

## File: python/app/common/postcodes_uk/main/exceptions.py
````python
class InvalidArea(Exception)
⋮----
def __init__(self, msg="Invalid postcode area", *args, **kwargs)
class InvalidDistrict(Exception)
⋮----
def __init__(self, msg="Invalid postcode district", *args, **kwargs)
class InvalidSector(Exception)
⋮----
def __init__(self, msg="Invalid postcode sector", *args, **kwargs)
class InvalidUnit(Exception)
⋮----
def __init__(self, msg="Invalid postcode unit", *args, **kwargs)
````

## File: python/app/common/postcodes_uk/main/formatter.py
````python
class Postcode
⋮----
def __init__(self, area: str, district: str, sector: int, unit: str) -> None
def __str__(self)
⋮----
string = self.outward_code + " " + self.inward_code
⋮----
def __eq__(self, other)
⋮----
@staticmethod
    def from_string(postcode: str)
⋮----
"""
        Create a Postcode object from a string
        :param postcode: A string with the postcode
        :return: A postcode object
        """
match = re.search(
area = match.group("area")
district = match.group("district")
sector = int(match.group("sector"))
unit = match.group("unit")
````

## File: python/app/common/postcodes_uk/main/validator.py
````python
def valid_postcode(postcode: str) -> bool
⋮----
"""
    Validate a british postcode
    For more information, please read : https://en.wikipedia.org/wiki/Postcodes_in_the_United_Kingdom#Formatting
    :param postcode: A string with the UK postcode
    :return: A boolean result with whether the postcode is valid or not
    """
⋮----
def valid_postcode_area(area: str)
⋮----
"""
    Validate a british postcode area
    :param area: A string with the UK postcode area
    :return: A boolean result with whether the postcode area is valid or not
    """
⋮----
def valid_postcode_district(district: str)
⋮----
"""
    Validate a british postcode district
    :param district: A string with the UK postcode district
    :return: A boolean result with whether the postcode district is valid or not
    """
⋮----
def valid_postcode_sector(sector: int)
⋮----
"""
    Validate a british postcode sector
    :param sector: A string with the UK postcode sector
    :return: A boolean result with whether the postcode sector is valid or not
    """
⋮----
def valid_postcode_unit(unit: str)
⋮----
"""
    Validate a british postcode unit
    :param unit: A string with the UK postcode unit
    :return: A boolean result with whether the postcode unit is valid or not
    """
````

## File: python/app/common/secrets/secrets.py
````python
""" Secrets for the application """
⋮----
class SecretNames(Enum)
⋮----
"""Enum for secret names
    Args:
        Enum (Enum): The names of the secrets store in AWS Secrets Manager
    """
EA_API_SECRET_NAME = settings.EA_API_SECRET_NAME
EL_API_SECRET_NAME = settings.EL_API_SECRET_NAME
MARKETING_CAMPAIGN_SECRET_NAME = settings.MARKETING_CAMPAIGN_SECRET_NAME
def load_secret(secret_name: SecretNames, key_name: str) -> str
⋮----
"""Gets a single secret from the dot environment file or AWS Secrets Manager
    Args:
        secret_name (str): _description_
        key_name (str): _description_
    Returns:
        _type_: _description_
    """
⋮----
def load_secrets(secret_name: SecretNames, key_names: list[str]) -> dict[str, str]
⋮----
"""
    Load a secret from the dot environment file or
    AWS Secrets Manager and check if the value is not an empty string
    Args:
        secret_name (str): Name of the secret
        key_names (list[str]): List of key names to check
    Returns:
        dict: Dictionary of key names and values
    """
full_secret_name = f"{settings.ENVIRONMENT}-{secret_name.value}"
⋮----
# Load the secret from the dot environment file
secrets = {key_name: os.environ.get(key_name, "") for key_name in key_names}
⋮----
# Load the secret from AWS Secrets Manager
secrets = get_secret(full_secret_name)
⋮----
# Check if each key name and value to see if the value is not an empty string
⋮----
value = secrets.get(key_name, "")
⋮----
class Secrets
⋮----
"""Secrets for the application"""
# ExperianAperture
EA_API_KEY: str
# ElectraLink
EL_API_KEY: str
EL_API_PASSWORD: str
MARKETING_CAMPAIGN_API_KEY: str
def __init__(self)
⋮----
"""Load the secrets"""
ea_secret = load_secrets(
el_secret = load_secrets(
def set_secret(secret: dict[str, str])
⋮----
"""Set the secret
            Args:
                secret (str): The secret
            """
# for each key in secret set the value of the key to the value of the key in the secret
⋮----
app_secrets = Secrets()
````

## File: python/app/common/security/csrf/check.py
````python
"""
CSRF checking code
This module contains the code that is used to check the CSRF token in a request
to ensure that the request is coming from a trusted source.
This was inspired by
https://github.com/PacktPublishing/Python-Web-Development-with-Sanic/blob/79f5bcebea865c62ae66f4d215d5c24a3edaad1a/Chapter07/csrf/server.py
"""
⋮----
def csrf_protected(func)
⋮----
"""
    Decorator to protect a route from CSRF attacks.
    This decorator should be used on routes that are not GET requests, and
    should be used in conjunction with the `inject_csrf_token` middleware.
    This decorator will check the CSRF token in the request headers and cookies
    to ensure that the request is coming from a trusted source.
    The check is performed by comparing the token in the `x-xsrf-token` header with the
    token in the `csrf_token` cookie. This approach is called the "double submit cookie"
    method.
    If the CSRF token is valid, then the company ID will be set on the request context
    """
def decorator(f)
⋮----
@wraps(f)
        async def decorated_function(request: Request, *args, **kwargs)
⋮----
# check the CSRF token on the request
token = _get_valid_csrf_token(request)
⋮----
# get the session contents from the Redis cache
session_cache_contents = await request.app.ctx.session_cache.hmget(
# fail if the session cache is not found
⋮----
# set the company ID on the request context for use in the route
⋮----
# call the wrapped route
response = f(request, *args, **kwargs)
⋮----
response = await response
⋮----
def _get_valid_csrf_token(request: Request) -> str
⋮----
"""
    Check the CSRF token in the request headers and cookies to ensure that the request
    is coming from a trusted source.
    The check is performed by comparing the token in the `x-xsrf-token` header with the
    token in the `csrf_token` cookie. This approach is called the "double submit cookie"
    method.
    https://stackoverflow.com/a/65871101/1916362 explains the approach in more detail.
    :param request: The request to check
    :raises CSRFFailure: If the CSRF token is invalid
    :return: True if the CSRF token is valid
    """
# Get the CSRF token from the request headers
csrf_token_header = request.headers.get("x-xsrf-token", "")
⋮----
# Get the CSRF and reference tokens from the request cookies
csrf_token_cookie = request.cookies.get("csrf_token", "")
ref_token = request.cookies.get("ref_token", "")
secret = settings.CSRF.REF_SECRET
padding = settings.CSRF.REF_PADDING
⋮----
# perform the actual verification of the CSRF token
⋮----
"""
    Compare the CSRF token in the request headers with the CSRF token in the request
    cookies. If they are not equal, then the request is not coming from a trusted
    source, and the request should be rejected.
    """
⋮----
# if we get here then all is well
⋮----
def _verify_csrf(secret: bytes, padding: int, ref: str, token: str) -> None
⋮----
"""
    Verify the CSRF token to ensure that it is valid.
    :param secret: The secret used to encrypt the CSRF token
    :param padding: The number of bytes to remove from the start of the CSRF token
    :param ref: The reference token
    :param token: The CSRF token
    :raises InvalidToken: If the CSRF token is invalid
    """
⋮----
cipher = Fernet(secret)
raw = b64decode(token.encode("utf-8"))
pretoken = raw[padding:]
encoded_ref = cipher.decrypt(pretoken)
````

## File: python/app/common/security/csrf/exceptions.py
````python
class CSRFFailure(Forbidden)
⋮----
"""
    Exception to raise when CSRF validation fails.
    """
message = "CSRF Failure. Missing or invalid CSRF token."
quiet = False
````

## File: python/app/common/security/csrf/inject.py
````python
"""
CSRF generation
This module contains the code that is used to generate a CSRF token and inject it into
the response cookies.
This was inspired by
https://github.com/PacktPublishing/Python-Web-Development-with-Sanic/blob/79f5bcebea865c62ae66f4d215d5c24a3edaad1a/Chapter07/csrf/server.py
"""
⋮----
"""
    Inject CSRF token into response cookies, if not already present.
    This function is intended to be used as a middleware for Sanic (@app.on_response).
    :param request: The request object.
    :param response: The response object.
    :param needs_new_session: Whether to generate a new session cookie.
    :return: A Tuple containing:
        - The response object.
        - The CSRF token, if it was generated, otherwise None.
    """
# if in debug mode, don't set secure cookies
secure_cookies = not bool(request.app.ctx.settings.debug_mode)
# TODO (James) create a DTO for the response shape
⋮----
# TODO (James) check if the CSRF token in cookie is valid
# consider if we want to allow access here without the header
⋮----
response = json({"message": "session-set", "csrf_token": str(token)})
# TODO (James) create a function to set the cookie
⋮----
# TODO (James) also return session token so we can set it in caches etc
⋮----
def _generate_csrf(secret: bytes, ref_length: int, padding: int) -> Tuple[str, str]
⋮----
"""
    Generate a CSRF token.
    :param secret: The secret key to use for encryption.
    :param ref_length: The length of the reference token (in bytes).
    :param padding: The amount of padding to add to the reference token (in bytes).
    :return: A Tuple containing the reference token (in hex) and the CSRF token (in base64).
    """
cipher = Fernet(secret)
ref = os.urandom(ref_length)
pad = os.urandom(padding)
pretoken = cipher.encrypt(ref)
````

## File: python/app/common/security/decorators.py
````python
"""Decorators for security."""
⋮----
def with_company(required=True)
⋮----
"""
    Decorator to get the company from the session and pass it to the function.
    If a `company` is already on the kwargs, it will be passed through, and
    no action will be taken by this decorator.
    This decorator must be placed on a function which takes a `request` as the first argument.
    If the function accepts a `session` argument, the session will be passed to it.
    :param required: (Optional, default=True) If True, the decorator will raise an exception if there is no company in the session.
    """
is_required = required
def decorator(function: Callable) -> Callable
⋮----
@wraps(function)
@with_db_session
        async def wrapper(request: Request, *args, session=None, **kwargs)
⋮----
# if company is already on kwargs, pass it through
⋮----
company_id = request.ctx.company_id
⋮----
company_id = None
⋮----
company = await get_company_by_id(company_id, session=session)
⋮----
# if the function accepts a session argument, pass our session to it
⋮----
# this allows the decorator to be used with or without arguments
# e.g. @with_company or @with_company(required=False)
````

## File: python/app/common/security/session.py
````python
async def _set_session_cache_data(session_id: str, data: Dict[str, str])
⋮----
"""
    Sets the session cache data for the given session ID.
    """
⋮----
# TODO (James) magic number here for the expiry time
⋮----
def get_session_id_for_request(request: Request)
⋮----
"""
    Get the session id from the request's cookies.
    """
⋮----
async def clear_session_for_request(request: Request)
⋮----
"""
    Clears the session for the request and removes it from the Redis cache.
    """
# TODO (James) we should probably add the session to a revoked session cache
⋮----
async def create_session(session_id: str, company_id: Optional[str] = None)
⋮----
"""
    Creates a session for the given ID and sets it in the Redis cache.
    Optionally, a company ID can be passed in to associate the session with a company.
    """
# TODO (James) we need to be careful here - we need to make sure that the session id is unique
#               and that it is not already in the cache, otherwise we risk overwriting an existing session
#               and exposing the data to the wrong user
#
#               As we store the IP, we can check if the IP is the same as the one in the cookie but
#               we need to do this for every request, which is not ideal
cache_values = {}
````

## File: python/app/common/utils/accepts_kwarg.py
````python
def accepts_kwarg(func: Callable, kwarg: str)
⋮----
"""Check if a function accepts a keyword argument."""
sig = inspect.signature(func)
````

## File: python/app/common/utils/call_endpoint.py
````python
"""
Utility functions for the application
"""
⋮----
"""
    Call any URL endpoint with retry mechanism.
    :param url: Endpoint URL to call
    :param method: HTTP method; one of 'GET', 'POST', 'PUT', 'DELETE' (case insensitive)
    :param payload: Request payload to send
    :param retries: Number of retries to attempt in case of HTTP error
    :param timeout: Request timeout in seconds
    :param jitter_min: Minimum jitter value for exponential backoff, defaults to 0.1 seconds
    :param jitter_max: Maximum jitter value for exponential backoff, defaults to 0.5 seconds
    :return: Response JSON
    """
# Map HTTP methods to httpx client methods
http_method_func_map = {
# Validates that the method is a valid HTTP method
method = method.lower()
⋮----
http_method = http_method_func_map[method]
⋮----
# Calls the corresponding httpx function based on the method
response = await http_method(
⋮----
# Include json payload if present for POST and PUT requests
⋮----
# Calculate delay with jitter
delay = min(jitter_max, jitter_min * 2**retry) * (
````

## File: python/app/common/utils/case_styles.py
````python
"""Case styles utility class to convert to/fro camelCase/snake_case."""
class CaseStyles
⋮----
@staticmethod
    def to_camel_case(text: str) -> str
⋮----
"""Convert from snake_case to camelCase.
        Args:
            text (str): Text to convert.
        Returns:
            str: Camel case text.
        """
tokens = text.split("_")
⋮----
@staticmethod
    def to_snake_case(text: str) -> str
⋮----
"""Convert from camelCase to snake_case.
        Args:
            text (str): Text to convert.
        Returns:
            str: Snake case text.
        """
````

## File: python/app/common/utils/company_number.py
````python
""" Company number utils """
⋮----
# TODO (Stephen): Replace with a get method on Company model
def get_company_number(company_info: Company)
⋮----
"""
    Get the company number for the company
    :param company_info: The company to get the number for
    :return: The company number or N/A if no company number
    """
company_number = {
````

## File: python/app/common/utils/csv_generator.py
````python
"""
CSVGenerator class for generating CSV files from Pydantic model instances.
"""
⋮----
class CSVGeneratorError(Exception)
⋮----
"""An error that occurs when generating a CSV file."""
class CSVGeneratorNoDataError(Exception)
⋮----
"""An error that occurs when generating a CSV file with no data."""
class CSVGenerator(BaseModel)
⋮----
"""
    A class that generates CSV files from Pydantic model instances.
    """
quoting: int = Field(
data: List[BaseModel] = Field(
def to_dict(self, na_value: str = "N/A") -> List[dict]
⋮----
"""
        Return the data as a list of dictionaries with missing values replaced by 'N/A'.
        Args:
            na_value (str, optional): The value to replace missing values with. Defaults to "N/A".
        Returns:
            List[dict]: The data as a list of dictionaries with missing values replaced by 'N/A'.
        """
⋮----
def generate(self) -> str
⋮----
"""
        Generate the CSV file with the configured fields and data.
        Returns:
            str: The CSV content as a string.
        Raises:
            CSVGeneratorNoDataError: If any of the required parameters (fields, data) are missing.
        """
⋮----
model_fields = list(self.data)[0].__fields__
field_names = list(model_fields.keys())
⋮----
csv_writer = writer(output, quoting=self.quoting, quotechar='"')
````

## File: python/app/common/utils/decode_base64_string.py
````python
"""Decodes base64 encoded string"""
⋮----
def decode_base64_string(encoded_string: str) -> str
⋮----
"""
    Decodes a base64 encoded string.
    Args:
        encoded_string (str): A base64 encoded string.
    Returns:
        str: The decoded string.
    """
````

## File: python/app/common/utils/hide_meter_numbers.py
````python
def hide_meter_number(n: Union[str, int], k: int = 4) -> str
⋮----
"""Hide the meter number, only show last `k` digits."""
⋮----
n = str(n)
i = len(n) - k
⋮----
def hide_mpan_middle(mpan: str)
⋮----
"""Hide the middle of the mpan"""
````

## File: python/app/common/utils/human_readable.py
````python
def human_readable(text: str)
⋮----
"""
    Converts a camel case string to a human-readable format with proper capitalization and spaces.
    Args:
        text (str): A camel case string to be converted to human-readable format.
    Returns:
        str: A human-readable string with spaces and proper capitalization.
    Examples:
        >>> human_readable("SmartChoiceEstablishedB")
        'Smart Choice Established B'
        >>> human_readable("Default")
        'Default'
        >>> human_readable("Choice")
        'Choice'
        >>> human_readable("Protect")
        'Protect'
    """
````

## File: python/app/common/utils/is_valid_email_address.py
````python
"""Check if email is valid"""
⋮----
def is_email_valid(email: str) -> bool
⋮----
"""Check if email is valid
    Args:
        email (str): email to check
    Returns:
        bool: True if email is valid, False otherwise
    """
````

## File: python/app/common/utils/json_template_renderer.py
````python
"""
This utility class
for rendering JSON templates using Jinja2.
"""
⋮----
K = TypeVar("K")
V = TypeVar("V")
class JSONTemplateRenderer
⋮----
"""
    A utility class for rendering JSON templates using Jinja2.
    """
⋮----
@staticmethod
    def render_template(template_path: str, context: Dict[K, V] = None) -> Dict[K, V]
⋮----
"""
        Renders a JSON template with the given context.
        Args:
            template_path: The path to the JSON template file.
            context: A dictionary containing the values to replace the placeholders in the template.
        Returns:
            A dictionary containing the rendered JSON data.
        """
⋮----
context = {}
⋮----
template_str = file.read()
template = Template(template_str)
rendered_json = template.render(context)
````

## File: python/app/common/utils/prevent_auto_linking.py
````python
"""Prevent auto-linking of URLs by inserting a zero-width space after each dot"""
def prevent_auto_linking(text: str) -> str
⋮----
"""Prevents auto-linking of URLs by inserting a zero-width space after each dot
    Args:
        text (str): Text that may contain URLs
    Returns:
        str: Text with zero-width spaces inserted after dots
    """
````

## File: python/app/common/utils/random_string.py
````python
def generate_random_string(length)
⋮----
"""Generate a random string of fixed length"""
````

## File: python/app/common/utils/test_human_readable.py
````python
""" Test human_readable function."""
⋮----
def test_human_readable(input_string, expected_output)
⋮----
"""Test human_readable function."""
````

## File: python/app/common/utils/url_builder.py
````python
"""
    Generic URL builder.
    Args:
        base_url (str): Base URL with scheme and domain info (eg 'http://api.google')
        path_params (List(str)): Ordered list of path parameter strings (eg ['search', 'user'])
        query_params (Dict): Query parameter mapping (eg {'name': 'joe', 'role': 'admin'})
    Returns:
        str: URL string (eg 'http://api.google/search/user?name=joe&role=admin')
    """
parsed_url = urlsplit(base_url)
parsed_query_params = None
⋮----
parsed_query_params = urlencode(query_params, doseq=True)
parsed_path_params = ""
⋮----
parsed_path_params = "/".join(path_params)
````

## File: python/app/common/constants.py
````python
"""
General constant values for the application.
Note that these aren't settings but instead they are to replace "magic values" around the codebase.
"""
CURRENT_AGREEMENT_VERSION = "1.0.0"
# pylint: disable=C0301
UK_POSTCODE_REGEX = r"(?P<postcode_area>[A-Za-z]{1,2})(?P<district>(?:[0-9]{1,2})|(?:[0-9][A-Za-z]))(?P<sector>[0-9])(?P<postcode>[A-Za-z]{2})"
# r'^(((\+44\s?\d{4}|\(?0\d{4}\)?)\s?\d{3}\s?\d{3})|((\+44\s?\d{3}|\(?0\d{3}\)?)\s?\d{3}\s?\d{4})|((\+44\s?\d{2}|\(?0\d{2}\)?)\s?\d{4}\s?\d{4}))(\s?\#(\d{4}|\d{3}))?$'
UK_PHONE_REGEX = r"^(((\+44\s?\d{4}|\(?0\d{4}\)?)\s?\d{3}\s?\d{3})|((\+44\s?\d{3}|\(?0\d{3}\)?)\s?\d{3}\s?\d{4})|((\+44\s?\d{2}|\(?0\d{2}\)?)\s?\d{4}\s?\d{4}))(\s?#(\d{4}|\d{3}))?$"
UK_COMPANY_REGISTRATION_NUMBER_REGEX = re.compile(
DAYS_IN_YEAR = 365
"""The number of days in a year"""
SOLE_TRADER_ADDRESS_HISTORY_YEARS_REQUIRED = 5
"""The number of years of address history required for sole traders"""
SOLE_TRADER_ADDRESS_HISTORY_MAX_GAP_BETWEEN_ADDRESSES_DAYS = 31
"""The maximum number of days between addresses for sole traders"""
# TODO (Stephen): This is not a fixed value for all suppliers.
RENEWAL_WINDOW_DAYS = 240
LOWER_BOUND_FOR_CREDIT_SCORE = 30
DEFAULT_CREDIT_SCORE = 50
VALID_CHARITY_NUMBER_REGEX = r"\d{6,7}"
VALID_CHARITY_NUMBER_SC_REGEX = r"SC\d{6}"
VALID_CHARITY_NUMBER_WITHDASHZERO_REGEX = r"\d{6}-0"
VALID_CHARITY_NUMBER_COMBINED_REGEX = f"^(?:{VALID_CHARITY_NUMBER_REGEX}|{VALID_CHARITY_NUMBER_SC_REGEX}|{VALID_CHARITY_NUMBER_WITHDASHZERO_REGEX})$"
````

## File: python/app/common/is_in_local_mode.py
````python
def _is_not_authenticated_with_aws()
⋮----
"""Force local mode if no AWS credentials are available"""
⋮----
def is_in_local_mode(settings, aws_check: bool = False)
⋮----
"""Check if the environment is in local mode."""
⋮----
# Forces local_mode if not authenticated with AWS
# avoids boto3.client calls failing when local mode is off
````

## File: python/app/common/local_mode.py
````python
GET_EXPERIAN_CREDIT_SCORE = 50
MPAN_ADDITIONAL_DETAILS = electralink.LOCAL_MODE_MPAN_ADDITIONAL_DETAILS
MPAN_EAC = electralink.LOCAL_MODE_MPAN_EAC
MPAN_HH_COMBINED = electralink.LOCAL_MODE_MPAN_HH_COMBINED
MPRN_LOOKUP_METER_ADDITIONAL_DETAILS_8901264506 = (
PREMISES_SEARCH_BRITISH_GAS_EH6_5DY = britishgas.LOCAL_MODE_POSTCODE_SEARCH_EH6_5DY
SERVICES_SEARCH_BRITISH_GAS_EH6_5DY_906338794 = (
UDCORE_ELECTRIC_PRICES = udcore.electricprices
UDCORE_GAS_PRICES = udcore.gasprices
LAMBDA_CREATE_PDF_FOR_CONTRACT = "fake-local-mode-pdf.pdf"
LAMBDA_SIGN_PDF_FOR_CONTRACT = "fake-local-mode-signed-pdf.pdf"
INVOKE_SEND_QUOTE_FAILURE_EMAIL = lambdas.send_quote_failure_email
INVOKE_SEND_CONTRACT_COMPLETION_EMAIL = lambdas.send_contract_completion_email
LAMBDA_INVOKE_SEND_EMAIL_VERIFICATION_CODE = lambdas.send_email_verification_code
LAMBDA_INVOKE_SEND_REJOIN_EXISTING_FUZZY_MATCHED_COMPANY_EMAIL = (
LAMBDA_INVOKE_SCHEDULER_START = lambdas.notify_scheduler_start
LAMBDA_INVOKE_SCHEDULER_STOP = lambdas.notify_scheduler_stop
LAMBDA_CREATE_SIGNED_LOA = "fake-local-mode-loa-pdf.pdf"
````

## File: python/app/common/mpan.py
````python
# TODO (James) remove this flag in future, when the feature should be enabled
SHOULD_PROHIBIT_MTCS = True
def is_mpan_valid(mpan: str) -> bool
⋮----
"""
    Check if the mpan is valid
    - check the overall structure
    - ensure the checksum is correct
    - ensure the profile class is not prohibited
    - ensure the mtc is not prohibited for the profile class
    TODO (James) this function should probably return the MPAN object for
                use by the caller, rather than just a boolean
    """
⋮----
parsed_mpan = MPAN(mpan)
⋮----
profile_class = parsed_mpan.profile_class.identifier
meter_time_switch_code = parsed_mpan.mtc.identifier
⋮----
prohibited_mtcs = _prohibited_mtcs_by_profile_class[profile_class]
⋮----
"""
A list of MTCS that are prohibited for each profile class
This is generally due to them having a "related meter", for which the Watt
team must call the customer and provide the quote manually.
"""
_prohibited_mtcs_by_profile_class = JSONTemplateRenderer.render_template(
````

## File: python/app/common/mprn.py
````python
def is_mprn_valid(mprn: str)
⋮----
"""
    Checks if an MPRN is valid: it has to pass the validation for the last 2 digits
    (checksum).
    The final 2 digits in the MPRN are the check digits, and validate the preceding
    digits using a modulus 11 test. See
    https://en.everybodywiki.com/Meter_Point_Reference_Number for details.
        Args:
            mprn (str): MPRN value.
        Returns:
            bool: True if valid. False if invalid.
    """
````

## File: python/app/domains/dropoff/payloads.py
````python
class DropoffEmailPayload(BaseModel)
⋮----
"""This DTO represents the payload for the dropoff email."""
company_name: str
registration_number: str
main_address: str
main_postcode: str
meter_type: str
meter_number: str
business_type: str
industries: str
current_supplier_name: str
total_annual_usage: str
supplier_name: str
utility_type_name: str
continue_path: str
class Config
⋮----
"""Pydantic configuration."""
# (Jude): this is a hack to get around the fact that the server is expecting camelCase
alias_generator = CaseStyles.to_camel_case
allow_population_by_field_name = True
⋮----
@override
    def dict(self, *args, **kwargs)
⋮----
"""Override the dict method to use camelCase."""
````

## File: python/app/domains/dropoff/scheduler.py
````python
"""Schedule a reminder to update the supplier information."""
rejoin_path = await _get_company_rejoin_path(str(company.id))
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
mpan = str(company.primary_site.mpan) if company.primary_site.mpan else None
mprn = str(company.primary_site.mprn) if company.primary_site.mprn else None
⋮----
# TODO (DC): these could be stored in `UtilityType`
meter_name = "MPAN"
meter_number = mpan
⋮----
meter_name = "MRPN"
meter_number = mprn
utility_type_name = utility_type.name_capitalized()
⋮----
"""Schedule a reminder to sign the contract."""
rejoin_path = await _get_company_rejoin_path(str(company.id), contract)
⋮----
mpan = company.primary_site.mpan
mprn = company.primary_site.mprn
⋮----
meter_type = "MPAN"
meter_number = str(mpan)
⋮----
meter_type = "MPRN"
meter_number = str(mprn)
payload = DropoffEmailPayload(
⋮----
"""Schedule a reminder to choose a quote."""
⋮----
# TODO (DC): meter type could easily be attached to the UtilityType enums,
#  what do you think about that my dear reader?
⋮----
def _get_rejoin_path(company_id: str, token: str, contract_id: Optional[str]) -> str
⋮----
"""Get the rejoin path for a company, given the company ID and the secret token."""
values = {"company_id": company_id, "token": token}
⋮----
# Encode the values as a base64 string so they are nicer to look at in the URL
encoded = base64.urlsafe_b64encode(json.dumps(values).encode()).decode()
⋮----
"""Create a rejoin token for a company and return the full rejoin path."""
company: Company = await get_company_by_id(company_id=company_id, session=session)
⋮----
token = str(company.rejoin_token)
⋮----
token = await company.create_auth_token(company, session)
contract_id = str(contract.id) if contract else None
````

## File: python/app/integrations/crm/services.py
````python
"""
This module provides functionality to retrieve services for an address from our own addresses API
Functions:
    get_services_by_address_id(postcode: str, address_id: str) -> dict | None:
        Fetches address details for a given postcode and address ID from the local API.
        It returns a dictionary containing address details if found, otherwise None.
"""
⋮----
class Address(TypedDict, total=True)
⋮----
"""Model for the CRM Addresses API response.
    Args:
        TypedDict (_type_): _description_
        total (bool, optional): _description_. Defaults to False.
    """
id: str
postcode: str
county: Optional[str]
postalTown: Optional[str]
country: Optional[str]
address: Optional[str]
addressLine1: Optional[str]
addressLine2: Optional[str]
houseName: Optional[str]
houseNumber: Optional[str]
flatNumber: Optional[str]
bgServiceArea: Optional[bool]
createdAt: Optional[str]
updatedAt: Optional[str]
mpans: list[str]
mprns: list[str]
async def get_address_by_address_id(address_id: str)
⋮----
"""
    Gets the addresses with MPRN keys for a given address id.
    Args:
        address_id (str): The specific address ID to filter the results.
    Returns:
        Address | None: A dictionary with address details if found, otherwise None.
    The function queries a local API endpoint, filters the results based on the
    provided address ID, and returns the relevant address details.
    """
# Construct the URL for the API request
url = f"{settings.CRM_APP_URL}/api/addresses/{address_id}"
⋮----
address: dict[Address] = await call_endpoint(
⋮----
async def get_companies_house(company_number: str)
⋮----
"""
    Gets companies house details for a given company number.
    """
⋮----
url = f"{settings.CRM_APP_URL}/api/companies-house/{company_number}"
⋮----
address: Address = await call_endpoint(
````

## File: python/app/integrations/electralink/_types.py
````python
class TPRDataItem(BaseModel)
⋮----
"""Represents the TPR data for a single MPAN"""
tpr_eac: float
eac_tpr: str
eac_efd: str
"""
    The EFD (effective date) of the TPR
    TODO (James) should we parse this as a date?
    """
class MPANAnnualUsageData(BaseModel)
⋮----
"""Represents the annual usage data for a single MPAN"""
mpan: str
"""The bottom 13 digits of the MPAN"""
metering_point_address_line_1: str
"""The first line of the address of the MPAN"""
metering_point_address_line_2: str
"""The second line of the address of the MPAN"""
metering_point_address_line_3: str
"""The third line of the address of the MPAN"""
metering_point_address_line_4: str
"""The fourth line of the address of the MPAN"""
metering_point_address_line_5: str
"""The fifth line of the address of the MPAN"""
metering_point_address_line_6: str
"""The sixth line of the address of the MPAN"""
metering_point_address_line_7: str
"""The seventh line of the address of the MPAN"""
metering_point_address_line_8: str
"""The eighth line of the address of the MPAN"""
metering_point_address_line_9: str
"""The ninth line of the address of the MPAN"""
post_code: str
"""
    The postcode of the MPAN
    TODO (James) we should remove this and link it to a CompanySite instead
    """
supplier_id: str
"""
    The supplier ID
    This links to the electralink_supplier_id field
    """
supplier_name: str
"""
    The supplier name
    This is shown on the frontend
    """
supplier_efd: str
"""
    The EFD (effective date) of the supplier
    TODO (James) should we parse this as a date?
    """
total_eac: Optional[float] = None
"""The total EAC for the MPAN"""
tpr_data: list[TPRDataItem]
"""The TPR (tariff) data for the MPAN"""
````

## File: python/app/integrations/electralink/api_utils.py
````python
"""Helpers for making API calls."""
⋮----
"""Make API calls to ElectraLink and return the response object."""
# (Jude): this assumes a single key and value only
el_url = build_url(
# label is like so: {endpoint}_postcode_{post_code}
label = f"{endpoint}_postcode_{postcode.replace(' ', '')}_mpan_{value}"
⋮----
label = f"{endpoint}_postcode_{postcode.replace(' ', '')}"
⋮----
response = await client.get(
````

## File: python/app/integrations/electralink/get_mpan_hhcombined.py
````python
def _get_monthly_usage_from_usage_data(data) -> list[MonthlyUsage]
⋮----
"""Sum the annual usage per month"""
monthly_usage_column_name = "monthly_usage"
year_month_column_name = "year_month"
data_frame = pd.DataFrame.from_dict(data, orient="index", dtype="float")
data_frame = data_frame.filter(like="_HHC")
⋮----
data_frame = data_frame.reset_index()
⋮----
data_frame = (
⋮----
async def get_mpan_hhcombined(profile_class: str, mpan: str, postcode: str)
⋮----
"""Gets the half hourly combined usage data for a given mpan"""
⋮----
# Half hourly meters are profile class 00
⋮----
# TODO validate that this is a HH MPAN (half-hourly) profile class 00
# https://en.wikipedia.org/wiki/Meter_Point_Administration_Number#Profile_Class_(PC)
response = await make_electralink_api_call(
⋮----
data = response.json()
````

## File: python/app/integrations/electralink/models.py
````python
class YearMonthFormatError(Exception)
⋮----
"""Custom error that is raised when the year_month doesn't have the right format"""
def __init__(self, value: str, message: str) -> None
class MonthlyUsage(BaseModel)
⋮----
"""Represents the monthly usage"""
year_month: str
monthly_usage: float
⋮----
@validator("year_month")
    def validate_year_month(cls, value: str)
⋮----
"""Check that the year_month string has the correct format"""
⋮----
YEAR_MONTH_REGEX = r"^[0-9]{4}-[0-9]{2}"
````

## File: python/app/integrations/electralink/mpan_additional_details.py
````python
"""Constructs the full MPAN from its components"""
mpan_top = (
⋮----
async def get_mpan_additional_details(input_mpan_bottom: str, postcode: str)
⋮----
"""get_mpan_additional_details is a wrapper around the Electralink API call
    Args:
        input_mpan_bottom (str): bottom half of the mpan
        postcode (str): postcode of the mpan
        settings (DefaultSettings): settings object
    Returns:
        dict: mpan, energisation_status, full_address, post_code
    """
⋮----
res = LOCAL_MODE_MPAN_ADDITIONAL_DETAILS
⋮----
res = await _get_additional_details(input_mpan_bottom, postcode)
mpan_bottom = res["mpan"]
line_loss_factor_class_id = res["line_loss_factor_class_id"]
profile_class = res["additional_detail"][0]["profile_class"]
mtc_id = res["additional_detail"][0]["mtc_id"]
# Electralinks has a lot of missing MTC_IDs so we need to call Experian Aperture
# to get the MTC_ID.
⋮----
response = await make_experian_aperture_api_call(
⋮----
raw = response.json()
electricity_meter = raw["result"]["addresses_formatted"][0]["address"][
mtc_id = electricity_meter["meter_timeswitch_class"]
# The line loss factor from Electralink is not always correct
# so we need to get it from Experian Aperture but we currently don't
# have logic for when is incorrect.
# line_loss_factor = electricity_meter["line_loss_factor"]
mpan = _construct_full_mpan(
# Construct address
address_strings = [
full_address = ", ".join([x for x in address_strings if x])
post_code = res["post_code"]
# TODO (Stephen): This should be strongly typed against a Pydantic model
⋮----
# MPANs can only be constructed if the profile_class is available
# which is only true if enegistration_status is "E", both "" and "D" are not suitable
⋮----
async def _get_additional_details(mpan_bottom: str, postcode: str) -> str
⋮----
response = await make_electralink_api_call(
⋮----
# request failed
````

## File: python/app/integrations/electralink/mpan.py
````python
async def get_mpan_eac(settings: DefaultSettings, mpan: str, postcode: str)
⋮----
response = await make_electralink_api_call(
````

## File: python/app/integrations/electralink/usage.py
````python
async def get_usage_info_for_mpan(profile_class: str, mpan_bottom: str, postcode: str)
⋮----
# Only do this if profile_class is 00 otherwise wasting money
⋮----
async def _get_eac_full_tpr(mpan_bottom: str, postcode: str)
⋮----
response = await make_electralink_api_call(
⋮----
parsed = response.json()
tpr_data = [
# If the total_eac is not present, then we need to calculate it from the tpr_data
# TODO (James) should this be the other way around? i.e. prefer to sum the tpr_data if it's present
total_eac = (
````

## File: python/app/integrations/experian_aperture/_types.py
````python
class MPRNAnnualUsageData(BaseModel)
⋮----
"""Represents the annual usage data for a single MPRN"""
# TODO: (Olly): Please note: These are not the current values within Aperture.
mprn: Optional[str] = None
total_eac: Optional[float] = None
supplier_name: str
"""
    The supplier name
    This is shown on the frontend
    """
supplier_efd: str
"""
    The EFD (effective date) of the supplier
    TODO (James) should we parse this as a date?
    """
post_code: str
"""
    The postcode of the MPRN
    TODO (James) we should remove this and link it to a CompanySite instead
    """
````

## File: python/app/integrations/experian_aperture/api_utils.py
````python
"""Helpers for making API calls."""
⋮----
class ExperianApertureApiPayloads
⋮----
lookup_v2_electric = {
lookup_v2_gas = {
⋮----
@classmethod
    def get_payload_lookup_v2_gas(cls, mprn: str) -> dict
⋮----
payload = cls.lookup_v2_gas["json"].copy()
⋮----
@classmethod
    def get_payload_lookup_v2_electric(cls, mpan: str) -> dict
⋮----
payload = cls.lookup_v2_electric["json"].copy()
⋮----
# TODO (DC): add tests
⋮----
"""Make API calls to Experian Aperture and return the response object."""
⋮----
payload = ExperianApertureApiPayloads.get_payload_lookup_v2_gas(mprn)
⋮----
payload = ExperianApertureApiPayloads.get_payload_lookup_v2_electric(mpan)
ea_url = build_url(settings.EA_URL, path_params=["address", endpoint, version])
⋮----
# label is like so: {endpoint}_mprn_{mprn}
⋮----
label = f"address/{endpoint}_mprn_{mprn}"
⋮----
label = f"address/{endpoint}_mpan_{mpan}"
⋮----
response = await client.post(
````

## File: python/app/integrations/experian_aperture/lookup.py
````python
@with_db_session
async def get_supplier_id_by_recco_id(recco_id: str, session: AsyncSession = None)
⋮----
"""Gets the supplier_id by given recco_id (Aperture supplier_name)"""
⋮----
async def get_gas_meter_by_mprn(mprn: str)
⋮----
"""Gets gas meter details by MPRN."""
⋮----
mock_meter_details = local_mode.MPRN_LOOKUP_METER_ADDITIONAL_DETAILS_8901264506
mock_meter = mock_meter_details["result"]["addresses_formatted"][0]["address"][
⋮----
supplier_efd="2019-01-01",  # Not applicable for gas
⋮----
response = await make_experian_aperture_api_call(
⋮----
raw = response.json()
addresses_formatted = raw["result"]["addresses_formatted"]
⋮----
# We return None to allow manual entry flow
⋮----
gas_meters = addresses_formatted[0]["address"]["gas_meters"]
⋮----
gas_meter = gas_meters[0]
⋮----
supplier_efd="2019-01-01",  # Not applicable for gas
⋮----
error = (
````

## File: python/app/integrations/lambdas/contract_pdf_generator/contract_pdf_generator.py
````python
"""
    Creates a signed LOA for the given contract
    Invokes the create pdf for contract lambda, but uses loa_template as templateKey.
    Args:
        contract (Contract) : The contract to create the LOA for
        company_info (Company) : The company info
        contact_info (CompanyContact) : The contact info
        settings (DefaultSettings) : Settings
    Raises:
        GenerateSignedLOAException
    Returns:
        str: The key of the signed LOA
    """
payload = create_loa_pdf_payload(
invoke_response = invoke_lambda(
⋮----
# TODO (Olly): hook this into emailer.
⋮----
# TODO (Stephen): We need to make this return an error when the contract is missing.
⋮----
"""Invokes the create pdf for contract lambda.
    Args:
        contract (Contract): Contract to create the PDF for
        quoted_provider (Provider): Provider that was quoted
        current_provider_name (str): Name of the current provider
        company (Company): Company info
        industries (str): company industry
        contact_info (CompanyContact): Company contact info
        site_info (CompanySite): Company site info
        usage (ElectricityUsage): Electricity usage info
        settings (DefaultSettings): Settings
    Raises:
        GeneratePDFContractException: If the lambda fails to invoke
    Returns:
        str: The key of the PDF
    """
payload = create_pdf_payload(
⋮----
"""Invokes the create pdf for contract lambda.
    Args:
        contract (Contract): The contract to sign
        company (Company): The company info
        provider (Provider): The provider info
        ip_address (str): The IP address of the user
        settings (DefaultSettings): Settings
    Raises:
        SignPDFContractException: If the lambda fails to invoke
    Returns:
        str: The key of the signed PDF
    """
payload = create_sign_pdf_payload(
⋮----
TEMPLATE_LIST_BY_UTILITY = {
def get_contract_template_key(provider: Provider, utility: UtilityType)
⋮----
"""Gets the template key for the given provider.
    Args:
        provider (Provider): The provider to get the template key for
        utility (UtilityType): The utility type
    Raises:
        QuotationFailureEmailErrors.NO_PDF_KEY_FOR_PROVIDER.exception:
            If no template key is found
    Returns:
        str: The template key
    """
electra_link_id = provider.electra_link_id
selected_template_list = TEMPLATE_LIST_BY_UTILITY[utility]
````

## File: python/app/integrations/lambdas/contract_pdf_generator/supplier_template_keys.py
````python
"""
This file contains a mapping of supplier codes to the template keys used in the contract PDF generator.
The supplier code relates to `electra_link_id` on the `Provider` model.
"""
ELECTRIC_TEMPLATE_KEY_BY_SUPPLIER = {
⋮----
# XXXXX5 is a placeholder value as we dont know the id for it yet
⋮----
# === not sure which (if any) of the ones above are correct
# === the ones below are confirmed (coming back from UDCore)
⋮----
# === the ones below come back from UDCore and are not matched
⋮----
# "IRID" ("PfP Energy Supplies Ltd")
# "DRAX" ("DRAX") # Stephen: awaiting contract from Nicola disabled until we have the contract
# "SING" ("United Gas & Power (SING)")
# "0005" ("Utilita")
⋮----
GAS_TEMPLATE_KEY_BY_SUPPLIER = {
````

## File: python/app/integrations/lambdas/credit_score/get_credit_score.py
````python
"""Invokes the get credit score lambda."""
# don't call experian if we are in local mode
⋮----
def create_payload(business_ref: str, company_type: str)
response = invoke_lambda(
# response is None if the lambda throws an error
````

## File: python/app/integrations/lambdas/messaging/email_contract_completion.py
````python
class InvokeSendQuoteCompletionEmailException(Exception)
⋮----
"""Occurs when the invoke send quote completion email fails to get the data needed for the email"""
⋮----
"""Send email internal contract completion"""
⋮----
current_provider_name: str = "NOT AVAILABLE"
new_provider_name: Union[str, None] = None
⋮----
# TODO (James) remove these logs once we're confident the emails are working
⋮----
provider = await get_provider_by_id(
⋮----
# TODO (James) remove these logs once we're confident the emails are working
⋮----
current_provider_name = provider.name
⋮----
new_provider = await get_provider_by_id(
⋮----
new_provider_name = "[NOT AVAILABLE]"
⋮----
new_provider_name = new_provider.name
⋮----
credit_score = await get_credit_score_by_company_id(
company_type = getattr(
# Display the company type name instead of the enum value capitalized
company_type_name = (
banking_payload = create_banking_payload(company, contract)
# TODO: (maks) function with too many params
# Maks: this also looks like repeated code however i don't know
# what the external email will currently look like so it will
# most likely change
internal_data_payload = create_contract_completion_payload_internal(
⋮----
utility_usage,  # mpan should not be sensored
⋮----
external_data_payload = create_contract_completion_payload_external(
⋮----
# send email to watt team
⋮----
# send email to customer
⋮----
def invoke_email_lambda(payload, settings: DefaultSettings)
````

## File: python/app/integrations/lambdas/messaging/email_quote_failure.py
````python
NOT_AVAILABLE = "N/A"
class InvokeSendQuoteFailureEmailException(Exception)
⋮----
"""
    Occurs when the invoke send quote failure email
    fails to get the data needed for the email
    """
⋮----
# TODO (DC) this is not the GasUsage type, but some ad hoc NamedTuple at times
⋮----
# TODO (DC) this is not the ElectricityUsage type, but some ad hoc NamedTuple at times
⋮----
"""Send email internal quote failure email"""
⋮----
# electricity
electricity_data = {}
elec_current_provider = NOT_AVAILABLE
electricity_provider_id = getattr(electricity_usage, "provider_id", None)
⋮----
# (Bidur) - this needs reviewing not entirely sure of the logic
# find provider
⋮----
elec_provider = await get_provider_by_electra_link_id(
⋮----
elec_provider = await get_provider_by_id(
⋮----
elec_current_provider = elec_provider.name
⋮----
non_watt_provider_name = getattr(
⋮----
primary_site: CompanySite = getattr(company, "primary_site", None)
primary_site_mpan = getattr(primary_site, "mpan", None)
electricity_usage_mpan = getattr(electricity_usage, "mpan", None)
# TODO (Stephen): multi site changes needed here
⋮----
# gas
gas_data = {}
gas_current_provider = NOT_AVAILABLE
gas_provider_id = getattr(gas_usage, "provider_id", None)
⋮----
gas_provider = await get_provider_by_electra_link_id(
⋮----
gas_provider = await get_provider_by_id(str(gas_provider_id), session)
⋮----
gas_current_provider = gas_provider.name
⋮----
primary_site_mprn = getattr(primary_site, "mprn", None)
gas_usage_mprn = getattr(gas_usage, "mprn", None)
⋮----
# company data
⋮----
company_type = getattr(
# Display the company type name instead of the enum value capitalized
company_type_name = NOT_AVAILABLE
⋮----
# TODO (DC) this should be a method of BusinessType, why is it here
company_type_name = (
⋮----
# TODO (DC) move out all of this so can be refactored (sometime)
def create_payload()
⋮----
contact: contracts = company.primary_contact
latest_agreement_set: CompanyAgreementSet = company.latest_agreement_set
main_address_object: EntityAddress = getattr(
business_address = getattr(main_address_object, "address", None)
business_postcode = getattr(main_address_object, "postcode", None)
⋮----
primary_site_address_object: EntityAddress = getattr(
site_address = getattr(primary_site_address_object, "address", None)
site_postcode = getattr(
utilities_managed = getattr(primary_site, "utilities_managed", None)
utilities_managed_names = []
# We need the code below as the data type of utilities_managed can be different
⋮----
# If utility is an instance of UtilityType enum, access the name attribute
⋮----
# If the utility is an integer, get the name from the UtilityType enum
⋮----
# TODO this should go into electricity_data too
⋮----
# TODO (DC) fix this
⋮----
# TODO this should go into gas_data too
⋮----
response = invoke_lambda(
⋮----
# TODO (Stephen): Refactor to generic "send-email" lambda function
````

## File: python/app/integrations/lambdas/messaging/email_rejoin_existing_fuzzy_matched_company.py
````python
"""Send email to rejoin an existing company session which has not yet signed the contract but the user only fuzzy matched the details"""
rejoin_path = await _get_company_rejoin_path(str(company.id))
def create_payload()
response = invoke_lambda(
⋮----
def _get_rejoin_path(company_id: str, token: str, contract_id: Optional[str]) -> str
⋮----
"""Get the rejoin path for a company, given the company ID and the secret token."""
values = {"company_id": company_id, "token": token}
⋮----
# Encode the values as a base64 string so they are nicer to look at in the URL
encoded = base64.urlsafe_b64encode(json.dumps(values).encode()).decode()
⋮----
"""Create a rejoin token for a company and return the full rejoin path."""
company: Company = await get_company_by_id(company_id=company_id, session=session)
⋮----
token = company.rejoin_token
⋮----
token = await company.create_auth_token(company, session)
contract_id = str(contract.id) if contract else None
````

## File: python/app/integrations/lambdas/messaging/email_verification.py
````python
"""Send email verification code to the user"""
def create_payload()
response = invoke_lambda(
````

## File: python/app/integrations/lambdas/messaging/sole_trader.py
````python
"""
Utility functions for displaying sole trader details in emails
"""
⋮----
def _format_soletrader_address(address: SoleTraderPersonalAddress)
⋮----
"""Serialise a SoleTraderPersonalAddress to a single-line string using the new EntityAddress relationship"""
entity_address = address.address_object
move_in_date_formatted = (
move_out_date_formatted = (
⋮----
# Collect the address components into a list
address_lines = [
# Filter out any None values
address_lines = list(filter(None, address_lines))
⋮----
full_address = (
⋮----
"No Address Available"  # Fallback message if address details are empty
⋮----
full_address = " ".join(
⋮----
)  # Join the non-empty address components
postcode = entity_address.postcode if entity_address.postcode else "N/A"
⋮----
full_address = "No Address Available"
postcode = "N/A"
⋮----
def format_soletrader_addresses(company: Company)
⋮----
"""Loop through all the addresses and serialise them"""
⋮----
def format_soletrader_dob(company: Company)
⋮----
"""Serialise the date of birth of a sole trader"""
````

## File: python/app/integrations/lambdas/notify_scheduler/notify_scheduler.py
````python
""" Lambda functions for scheduling and descheduling reminder emails """
⋮----
# TODO (Stephen): make this async
⋮----
"""Schedules a reminder email to be sent"""
def create_payload()
⋮----
response = invoke_lambda(
⋮----
# response is None if the lambda throws an error
⋮----
"""Cancels a schedule reminder email so it is no longer sent"""
````

## File: python/app/integrations/lambdas/payloads/email.py
````python
def create_banking_payload(company: Company, contract: Contract) -> dict[str, Any]
⋮----
"""Create a payload for banking details
    Args:
        company (Company): The company to get banking details from
    Returns:
        dict: The payload
    """
⋮----
# Find the banking details by contract_id
banking: CompanyBankingDetails = [
bank = banking[0] if banking else None
⋮----
# TODO (DC): single-use code, should go back to where it belongs
⋮----
# TODO (DC): usage should not be `None` at this point, see in caller
⋮----
"""Generate supplier info"""
⋮----
# TODO (James) remove these logs once we're confident the emails are working
⋮----
# TODO (DC): this could probably be a member of `UtilityType`
⋮----
meter_name = "MPAN"
meter_number = utility_usage.mpan or "NOT AVAILABLE"
⋮----
meter_name = "MPRN"
meter_number = utility_usage.mprn or "NOT AVAILABLE"
supplier_info = {
⋮----
# TODO (DC): this should not be `None`, right? it's a contract completed for an
#  actual utility
⋮----
"""Create a payload for the internal contract completion email"""
# TODO (James) remove these logs once we're confident the emails are working
⋮----
contact = company.primary_contact
contract = company.contracts
quote = contract.quote
latest_agreement_set = company.latest_agreement_set
utility_type_name = utility_usage.utility_type.name_capitalized()
business_address = getattr(company.main_address_object, "address", None)
business_postcode = getattr(company.main_address_object, "postcode", None)
# build payload
payload = {
⋮----
# TODO (DC): del? not in use
````

## File: python/app/integrations/lambdas/payloads/pdf.py
````python
"""This module contains the payload for the pdf lambda"""
⋮----
# TODO: extract into file as its reused
london_timezone = pytz.timezone("Europe/London")
def _format_date(date: datetime) -> str
⋮----
"""Format a date to be used in the pdf"""
⋮----
def _get_current_datetime() -> str
⋮----
"""Return date in current timezone"""
⋮----
"""Create the payload for the sign pdf lambda"""
# Find the banking details by contract_id
banking: CompanyBankingDetails = [
bank = banking[0] if banking else None
⋮----
# adding it here as it is used for both contract template
# and audit page, dont want either to depend on each other
# so it's less tightly coupled
⋮----
# Stephen: using created_at and updated_at for now but we need to make the auditing better
⋮----
"""Create the payload for the pdf lambda"""
def get_business_type_display_name(business_type: int) -> str
⋮----
"""Get the business type display name
        Returns:
            str: Formatted business name
        """
names = [
⋮----
company_address: EntityAddress = getattr(company, "address", None)
business_address: EntityAddress = getattr(company, "main_address_object", None)
⋮----
usage_split_amount = usage.usage_amount_by_tariff
⋮----
"""Create the payload for the LOA PDF"""
company_info_address: EntityAddress = getattr(company_info, "address", None)
⋮----
def _get_company_mprn_or_mpan(company: Company, contract: Contract)
⋮----
"""
    Gets the company's MPRN or MPAN
    """
⋮----
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
mprn = company.primary_site.mprn
⋮----
mpan = company.primary_site.mpan
⋮----
def _format_quote_fields(quote: Quote)
⋮----
"""
    Formats the Quote fields into the correct format for the PDF
    """
⋮----
def _format_mprn_fields(mprn: str)
⋮----
"""
    Formats the MPRN fields into the correct format for the PDF
    """
⋮----
def _format_mpan_fields(mpan: MPAN)
⋮----
"""
    Formats the MPAN fields into the correct format for the PDF
    """
⋮----
# we need to do this manually because the library doesnt quite handle things correctly
mpan_unique_1 = mpan.core[2:6]
mpan_unique_2 = mpan.core[6:10]
mpan_checksum = mpan.core[10:13]
⋮----
def _format_contact_fields(contact: CompanyContact)
⋮----
"""
    Formats the contact fields into the correct format for the PDF
    """
⋮----
def _format_sole_trader_details(details: SoleTraderPersonalDetails)
⋮----
"""
    Formats the details of the Sole Trader into the correct format for the lambda
    """
soletrader_personal_address = _most_recent_address(details.addresses)
personal_address: EntityAddress = getattr(
⋮----
"""
    Returns the most recent address from a list of addresses
    """
⋮----
def _compose_full_address_line_1(company_address: EntityAddress) -> str
⋮----
"""Composes address line 1 using available address components.
    Args:
        company_address: EntityAddress object containing address details
    Returns:
        str: Formatted address line 1 with components joined by commas where available
    """
components = []
````

## File: python/app/integrations/lambdas/common.py
````python
"""
Responsible for invoking our S3 lambda functions.
"""
⋮----
def invoke_lambda(payload, function_name: str, settings: DefaultSettings)
⋮----
"""Invoke a lambda function with a payload."""
⋮----
directory = f"dumps/lambdas/{function_name}"
⋮----
current_timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S:%f")
⋮----
client = boto3.client(
⋮----
payload_as_string = json.dumps(payload)
payload_as_bytes = bytes(payload_as_string, encoding="utf8")
# todo error handling
response = client.invoke(
response_contents = response["Payload"].read()
⋮----
reponse_json = json.loads(response_contents)
````

## File: python/app/integrations/s3/dtos.py
````python
class UnsupportedProviderDto(OrmBase)
⋮----
"""Unsupported provider data transfer object"""
provider_name: str
electra_link_id: str
created_at: str
def __init__(self, provider: Provider, created_at_timestamp: str)
````

## File: python/app/integrations/s3/exceptions.py
````python
class S3ClientError(Exception)
⋮----
"""s3 client error"""
````

## File: python/app/integrations/s3/s3.py
````python
"""Generate s3 client"""
⋮----
class S3Client
⋮----
client: boto3.client
def __init__(self)
def write_json_object(self, dict, bucket_name: str, path: str)
⋮----
"""Write json object to s3 bucket
        Args:
            dict (any): Python dictionary of values to convert to json and write to s3
            bucket_name (str): Bucket name
            path (str): Bucket path / file name
        Raises:
            Exception: _description_
        """
⋮----
json_data = JSONEncoder().encode(dict)
````

## File: python/app/integrations/s3/save_data.py
````python
"""Save objects to an S3 bucket"""
⋮----
class ExternalAPIType(Enum)
⋮----
"""API that we are accessing"""
UDCORE: str = "UDCORE_ELECTRICPRICES"
ELECTRALINK: str = "ELECTRALINK"
EXPERIAN_APERTURE: str = "EXPERIAN_APERTURE"
BRITISH_GAS: str = "BRITISH_GAS"
# TODO (DC): this will bloat, could be generated?
"""Errors"""
ERROR_UDCORE: str = "ERROR_UDCORE"
ERROR_ELECTRALINK: str = "ERROR_ELECTRALINK"
ERROR_EXPERIAN_APERTURE: str = "ERROR_EXPERIAN_APERTURE"
ERROR_BRITISH_GAS: str = "ERROR_BRITISH_GAS"
⋮----
"""Log API call data to an S3"""
date = datetime.now()
timestamp = get_timestamp()
# (Jude): e.g. file_name = "UDCORE_ELECTRICPRICES/2023/3/2023-04-04 14:48:29-label.json"
file_name = (
directory = f"dumps/s3/{file_name}"
⋮----
current_timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S:%f")
⋮----
# pretty print the json
⋮----
# return early if we are in local mode
⋮----
s3 = S3Client()
⋮----
def save_unsupported_provider(provider: Provider)
⋮----
"""Save unsupported provider object to s3 bucket
    Args:
        provider (Provider): Unsupported provider to save
    """
⋮----
body = UnsupportedProviderDto(provider=provider, created_at_timestamp=timestamp)
bucket_name = f"{settings.ENVIRONMENT}-{settings.UNSUPPORTED_PROVIDERS_BUCKET_NAME}"
directory = f"dumps/s3/{provider.electra_link_id}.json"
⋮----
# no need to raise an error here as we don't want to stop the flow
````

## File: python/app/integrations/s3/util.py
````python
def get_timestamp()
⋮----
date = datetime.now()
````

## File: python/app/integrations/udcore/data/electric_quote_definitions.py
````python
electric_quote_definitions = JSONTemplateRenderer.render_template(
````

## File: python/app/integrations/udcore/data/gas_quote_definitions.py
````python
gas_quote_definitions = JSONTemplateRenderer.render_template("gas_quote_definitions")
````

## File: python/app/integrations/udcore/electricprices/http.py
````python
"""
Integration with UDCore for electricity prices
"""
⋮----
async def call_electricprices_endpoint(payload)
⋮----
"""
    Call the UDCore /electricprices endpoint
    :param payload: Request payload to send
    :return: Response JSON
    """
⋮----
url = f"{settings.UDCORE_API_URL}/Service.svc/web/electricprices"
electric_supply: dict = payload["quoteDetails"]["ElectricSupply"]
label = electric_supply["MPANTop"] + "_" + electric_supply["MPANBottom"]
⋮----
result = await call_endpoint(url, "POST", payload)
````

## File: python/app/integrations/udcore/gasprices/http.py
````python
"""
Integration with UDCore for Gas prices
"""
⋮----
async def call_gas_prices_endpoint(payload)
⋮----
"""
    Call the UDCore /gasprices endpoint
    :param payload: Request payload to send
    :return: Response JSON
    """
⋮----
url = f"{settings.UDCORE_API_URL}/Service.svc/web/gasprices"
gas_supply: dict = payload["quoteDetails"]["GasSupply"]
label = gas_supply["MPR"]
⋮----
result = await call_endpoint(url, "POST", payload)
````

## File: python/app/integrations/udcore/types.py
````python
class Error(BaseModel)
⋮----
error_detail: str
message: str
class PaymentMethod(BaseModel)
⋮----
amount: str
display_name: str
frequency: str
type_: str
class Rate(BaseModel)
⋮----
annual_price: str
annual_price_inclusive: str
base_annual_price: str
capacity_charge: str
cheque_surcharge: Optional[str]
commission: str
contract_end_date: str
day_unitrate: str
direct_debit_discount: str
excess_capacity_charge: str
extra_info: str
fits: Optional[str]
fixed_fee: Optional[str]
fixed_fee_sc_uplift: Optional[str]
fixed_fee_uplift: Optional[str]
flat_direct_debit_discount: int
footer_message: Optional[str]
half_hourly_number_of_rates: Optional[str]
id_: int
include_mop_charges: Optional[str]
is_renewable: bool
night_unitrate: str
no_quote_reason: Optional[str]
outside_pricing_window: bool
payment_method: PaymentMethod
period: Optional[str]
plan_type: Optional[str]
pricebook: Optional[str]
pricebook_version: str
quote_rate_reference: str
raw_annual_price: float
raw_annual_price_inclusive: float
raw_base_annual_price: float
raw_commission: float
reactive_rate: str
ref: str
renewal: bool
sc: Optional[str]
standing_charge: str
standing_charge_type: str
standing_charge_uplift: str
stod1: Optional[str]
stod2: Optional[str]
stod3: Optional[str]
stod4: Optional[str]
stod5: Optional[str]
stod6: Optional[str]
supplier: str
supplier_data: Optional[str]
tariff_code: str
term: str
transportation_charge: Optional[str]
uplift: str
wend_unitrate: str
class GetGasRatesResult(BaseModel)
⋮----
email_reference: Optional[str]
error: Error
footer_notes: str
half_hourly: bool
max_demand: bool
multi_site_results: Optional[str]
rates: List[Rate]
class GetElectricityRatesResult(BaseModel)
````

## File: python/app/integrations/udcore/utility_prices.py
````python
"""Electric prices integration with UDCore"""
⋮----
"""Save the data to a file for debugging purposes"""
⋮----
directory = "dumps/udcore"
⋮----
LOCAL_MODE_MAPPING = {
⋮----
"""
    Create quotes for a given utility via UDCore
    :param utility: the utility to create quotes for
    :param latest_usage: the latest usage for the utility
    :param company: the company information
    :param company_site: the site information
    :param settings: the settings
    :param credit_score the credit score
    :return: a list of quotes
    """
# Don't call UDCore if we are in local mode
⋮----
# TODO (James) this will need changing to support multi-site. Recommend take `CompanySite` as a parameter instead
mpan: MPAN = company.primary_site.mpan
mprn = company.primary_site.mprn
postcode = company.primary_site.postcode
postcode = Postcode.from_string(postcode.upper())
def create_electric_supply_payload()
⋮----
mpan_top = mpan.top_line
mpan_bottom = mpan.core
usages_by_tariff = usage.usage_amount_by_tariff
# KVA is needed for HH metres which have a profile class of "00". Otherwise it is 0.
# KVA can range from 70 - 150. Watt always assume 100 to generate the quote. If the quote is rejected
# then we need to contact the customer and ask them to provide the KVA.
kva_usage = 100 if mpan.profile_class.identifier == "00" else 0
# We could possibly use the "TPR" linked usages to get actual usages for each tariff
# rather than making assumptions about the split.
# Profile Class 00 is a HH meter, which has a smart meter.
smart_meter = "true" if mpan.profile_class.identifier == "00" else "false"
⋮----
# POWWR does not send anything else up to UDCore, other than the day usage.
⋮----
# POWWR sends the start date of the contract, not the end date.
⋮----
"MeasurementClass": "",  # Empty in POWWR
"SmartMeter": smart_meter,  # Can be "true" or "false" in POWWR
⋮----
def create_gas_supply_payload()
⋮----
total_annual_usage = usage.total_annual_usage
# TODO Stephen: There are not smart gas meters? Confused by Olly's comment below
smart_meter = "false"  # TODO: Update with smart meter status
⋮----
# Uplift is 1.0p
def create_payload(uplift=1.0)
⋮----
supply_payload = create_electric_supply_payload()
quote_definitions = electric_quote_definitions
⋮----
supply_payload = create_gas_supply_payload()
quote_definitions = gas_quote_definitions
⋮----
"BusinessType": "LTD",  # Can always be LTD as we don't generate final quote with UDCore
⋮----
# Id is not needed for the request.
⋮----
# mpan_top = str(company.mpan)[0:8]
# mpan_bottom = str(company.mpan)[-13:]
# save_udcore_for_mpan(
#     prefix="request",
#     data=create_payload(),
#     mpan_top=mpan_top,
#     mpan_bottom=mpan_bottom,
#     settings=settings,
# )
# If this times out the users sees an error screen,
# if they go back and forward it can work on second attempt
⋮----
result = await call_electricprices_endpoint(create_payload())
⋮----
result = await call_gas_prices_endpoint(create_payload())
⋮----
#     prefix="response",
#     data=result,
````

## File: python/app/models/companies/address.py
````python
"""Address for company"""
⋮----
class EntityAddress(BuildableORMBase)
⋮----
"""Generic address object"""
__tablename__ = "entity_address"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
postcode = Column(String, nullable=False)
county = Column(String, nullable=True)
postal_town = Column(String, nullable=False)
country = Column(String, nullable=True)
address = Column(String, nullable=False)
address_line_1 = Column(String, nullable=True)
address_line_2 = Column(String, nullable=True)
house_name = Column(String, nullable=True)
house_number = Column(String, nullable=True)
flat_number = Column(String, nullable=True)
mpan = Column(MPANType, nullable=True)
mprn = Column(String, nullable=True)
````

## File: python/app/models/companies/agreements.py
````python
"""This is a site that a company has. This is where utilities are provided to."""
⋮----
class CompanyAgreementSet(BuildableORMBase)
⋮----
"""This is the agreements the company has provided.
    Args:
        BuildableORMBase (Base): The base class for all models
    """
__tablename__ = "company_agreement_set"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id", ondelete="CASCADE"))
version = Column("version", String, default="1.0.0")
authorized = Column("authorized", Boolean, default=False, nullable=False)
credit_check = Column("credit_check", Boolean, default=False, nullable=False)
letter_of_authority = Column(
terms_and_conditions = Column(
smart_meter_agreement = Column(
direct_debit_agreement = Column(
````

## File: python/app/models/companies/auth.py
````python
"""This represents an auth token for a company."""
⋮----
class CompanyEmailAuthToken(BuildableORMBase)
⋮----
"""
    This represents an auth token for a company.
    This token is used in emails to allow the company to resume the flow.
    """
__tablename__ = "company_email_auth_token"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id", ondelete="cascade"))
"""The token that is used to authenticate the company. This is encrypted."""
token = Column(Text, nullable=False, unique=True)
"""The time that the token expires"""
expires_at = Column(DateTime, nullable=False)
````

## File: python/app/models/companies/banking.py
````python
class CompanyBankingDetails(BuildableORMBase)
⋮----
"""This is the banking details for a company"""
__tablename__ = "company_banking_details"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id", ondelete="NO ACTION"), nullable=False)
"""The date and time at which the bank details were verified by Data8"""
verified_at = Column(DateTime, nullable=True)
bank_name = Column(String, nullable=False)
account_holder_name = Column(String, nullable=False)
account_number = Column(String, nullable=False)
sort_code = Column(String, nullable=False)
contract_id = Column(ForeignKey("contract.id"), nullable=True, default=None)
⋮----
@property
    def is_verified(self)
````

## File: python/app/models/companies/company_aquisition_info.py
````python
"""Company acquisition information model"""
⋮----
class CompanyAcquisitionInfo(BuildableORMBase)
⋮----
"""
    Market campaign information to identify each target user
    against a SendGrid market campaign email
    """
__tablename__ = "company_aquisition_info"
id = Column(
"""Identify user via hashed email present on the acquisition link"""
target_email = Column(EmailType, nullable=True, default=None)
"""Identify the market campaign present on the acquisition link"""
campaign_id = Column(String, nullable=True, default=None)
````

## File: python/app/models/companies/company.py
````python
"""
This file contains the Company model and its subclasses.
These models are used to represent the different types of companies that can
be registered on the platform.
These models must live in the same file as they are all subclasses of the
Company model, and SQLAlchemy requires that all subclasses are defined in
the same file as the parent class.
"""
⋮----
# Email auth token is valid for 3 days
# TODO (James) move this to config
_EMAIL_AUTH_TOKEN_EXPIRY = timedelta(days=3)
def _encrypt_company_auth_token(token: str) -> str
⋮----
"""Encrypt a company auth token using bcrypt"""
⋮----
class Company(BuildableORMBase)
⋮----
"""This is the abstract Company model"""
def __init__(self, business_type, **kwargs)
__tablename__ = "company"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
business_type = Column(ChoiceType(BusinessType, impl=Integer()))
_active_contracts: Union[Contract, None] = None
__mapper_args__ = {
name = Column(String)
main_address = Column(String, unique=True)  # Legacy field
mpan = Column(MPANType)  # Legacy field
main_address_id = Column(ForeignKey("entity_address.id"), nullable=True)
main_address_object: EntityAddress = relationship("EntityAddress", uselist=False)
main_postcode = Column(String)
contacts: list[CompanyContact] = relationship("CompanyContact", backref="company")
sites: CompanySite = relationship("CompanySite", backref="company")
agreement_sets: list[CompanyAgreementSet] = relationship(
banking: CompanyBankingDetails = relationship(
selected_contract_id = Column(
aquisition_info_id = Column(
contracts: list[Contract] = relationship(
"""
    The contract that has been selected by the company
    TODO (James) this should be renamed to `selected_contract` in future as the current name is misleading
    """
email_auth_token = relationship(
⋮----
@reconstructor
    def init_on_load(self)
⋮----
"""Initialise the object when it is loaded from the database"""
⋮----
@property
    def address(self) -> EntityAddress
⋮----
"""Return first address in company sites"""
⋮----
@property
    def active_contracts(self)
⋮----
"""Return the active contracts for this company"""
# If the active contracts have not been loaded, load them
⋮----
# Return the cached active contracts
⋮----
@property
    def primary_contact(self) -> CompanyContact
⋮----
"""
        Return the primary contact for this company
        This assumes that the contacts have either been loaded, or are available
        for lazy loading.
        """
⋮----
@property
    def primary_site(self) -> CompanySite
⋮----
"""
        Return the primary sites for this company
        This assumes that the sites have either been loaded, or are available
        for lazy loading.
        """
⋮----
@property
    def latest_agreement_set(self) -> CompanyAgreementSet
⋮----
"""
        Return the latest agreement set for this company
        """
⋮----
def create_contact(self, *_args: Any, **kwargs: Any) -> CompanyContact
⋮----
"""
        Create a CompanyContact for this company.
        """
contact = CompanyContact(**kwargs)
⋮----
def create_site(self, address: dict, *_args: Any, **kwargs: Any)
⋮----
"""
        Create a CompanySite for this company.
        """
address = EntityAddress(**address)
site = CompanySite(
⋮----
def create_agreement_set(self, *_args: Any, **kwargs: Any)
⋮----
"""
        Create a CompanyAgreementSet for this company.
        """
agreement_set = CompanyAgreementSet(**kwargs)
⋮----
def create_credit_score(self, *_args: Any, **kwargs: Any)
⋮----
"""
        Create a CreditScore for this company.
        """
⋮----
async def create_auth_token(self, company, session)
⋮----
"""
        Create a new CompanyEmailAuthToken for this company and
        return the token in plain text.
        """
⋮----
# TODO (James) do we need to manually delete any existing token here?
token = f"{str(uuid.uuid4())}-{secrets.token_hex(16)}"
⋮----
async def _create_company_email_auth_token(self, token: str, company_id, session)
⋮----
"""
        Create a CompanyEmailAuthToken for this company.
        :param token: The token to be stored - this function will encrypt it with sha256
        """
expires_at = datetime.now() + _EMAIL_AUTH_TOKEN_EXPIRY
encrypted_token = _encrypt_company_auth_token(token)
token = CompanyEmailAuthToken(
⋮----
class LtdCompany(Company)
⋮----
"""
    Represents a limited company.
    This is stored in the same table as the abstract Company model
    https://docs.sqlalchemy.org/en/14/orm/inheritance.html#single-table-inheritance
    As this model uses single-table inheritance, you **must not** add any
    columns with `nullable=False` to this model. See docs/sqlalchemy_tips.md for more info.
    """
# required so we don't lose __mapper_args__ from base class
__mapper_args__ = Company.__mapper_args__.copy() if Company.__mapper_args__ else {}
⋮----
"""
    TODO (James) some people say we shouldn't need this, but we seem to
    https://stackoverflow.com/a/34724424/1916362
    """
__table_args__ = {"extend_existing": True}
industries = relationship(
registration_number = Column(String, unique=True)
# TODO (Stephen): Handle other company types
⋮----
@validates("registration_number")
    def validate_registration_number(self, _, registration_number)
⋮----
"""Validate the registration number is a valid UK company registration number."""
⋮----
class CharityCompany(Company)
⋮----
"""
    Represents a charity. This is stored in the same table as the abstract Company model
    https://docs.sqlalchemy.org/en/14/orm/inheritance.html#single-table-inheritance
    As this model uses single-table inheritance, you **must not** add any
    columns with `nullable=False` to this model. See docs/sqlalchemy_tips.md for more info.
    """
⋮----
charity_number = Column(String, unique=True)
class SoleTraderCompany(Company)
⋮----
"""
    Represents a sole trader. This is stored in the same table as the abstract Company model
    https://docs.sqlalchemy.org/en/14/orm/inheritance.html#single-table-inheritance
    As this model uses single-table inheritance, you **must not** add any
    columns with `nullable=False` to this model. See docs/sqlalchemy_tips.md for more info.
    """
⋮----
personal_details = relationship(
⋮----
uselist=False,  # this is a one-to-one relationship
⋮----
"""The personal details for this sole trader, used for credit scoring"""
````

## File: python/app/models/companies/contact.py
````python
""""This is the contact for the company"""
⋮----
class CompanyContact(BuildableORMBase)
⋮----
"""This is the contact for the company"""
__tablename__ = "company_contact"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
forename = Column(String, nullable=False)
surname = Column(String, nullable=False)
email = Column(EmailType, nullable=False)
phone = Column(String, nullable=False)
position = Column(String)
company_id = Column(ForeignKey("company.id", ondelete="CASCADE"), nullable=False)
⋮----
@validator("phone")
@classmethod
    def validate_uk_phonenumber(cls, phone)
⋮----
"""Validate that the phone number is a UK number"""
parsed = phonenumbers.parse(phone, "GB")
⋮----
@validates("email")
    def validate_email(self, _key, email) -> str
⋮----
"""Validate that the email is in the correct format."""
⋮----
def matches(self, *_args: Any, **kwargs: Any) -> bool
⋮----
"""Returns True if the CompanyContact matches the keyword arguments passed in"""
email = kwargs.get("email", None)
phone = kwargs.get("phone", None)
````

## File: python/app/models/companies/credit_score.py
````python
"""
This file contains the models for company credit.
"""
class CreditScore(BuildableORMBase)
⋮----
__tablename__ = "credit_score"
company_id = Column(ForeignKey("company.id", ondelete="CASCADE"), primary_key=True)
credit_score = Column(Integer())
````

## File: python/app/models/companies/industry.py
````python
""" This is the industry that a company belongs to """
⋮----
class CompanyIndustry(BuildableORMBase)
⋮----
"""This is the industry that a company belongs to"""
__tablename__ = "company_industry"
company_id = Column(ForeignKey("company.id"), primary_key=True)
industry_id = Column(ForeignKey("industry.id"), primary_key=True)
````

## File: python/app/models/companies/README.md
````markdown
# companies

This folder (`app.models.companies`) contains the code for the companies module of the application.

## Getting started

To get started with the companies module, simply import the relevant models into your code, as shown below:

```py
from app.models.companies import Company, CompanySite
```

## Models
The following models are defined in this folder:

- `Company`: This abstract model is the base class for all companies in the application.
- `LtdCompany`: This model represents a limited liability company and inherits from the Company model.
- `CharityCompany`: This model represents a charity company and inherits from the Company model.
- `SoleTraderCompany`: This model represents a sole trader company and inherits from the Company model.

- `CompanySite`: This model represents a physical site associated with a company, i.e. a place to supply utilities
- `CompanyContact`: This model represents a contact associated with a company.
- `CompanyIndustry`: This model represents the industry that a company operates in.
- `CreditScore`: This model represents a company's credit score.
- `CompanyBankingDetails`: This model contains information about a company's banking details.
- `CompanyEmailAuthToken`: This model is responsible for handling email authentication tokens for companies (for rejoins)

These models are intended to be used together in order to provide a complete picture of a company in the application.

### Company model

The Company model is an abstract model that represents a company. It is one of the main models in the system.

#### Fields
- `business_type`: The type of business the company operates as (ltd, charity, sole trader), stored as a choice from the `BusinessType` list.
- `name`: The name of the company.
- `mpan`: The MPAN number of the company.
- `contacts`: A list of `CompanyContact` objects that represent the contacts associated with the company.
- `industries`: A list of industries that the company operates in, stored as Industry objects.
- `sites`: A list of `CompanySite` objects that represent the company's sites.
- `banking`: A list of `CompanyBankingDetails` objects that represent the company's banking information.
- `email_auth_token_id`: A foreign key to the `CompanyEmailAuthToken` model.
- `email_auth_token`: An instance of the `CompanyEmailAuthToken` model.

#### Properties
- `primary_contact`: A property that returns the first `CompanyContact` object in the contacts list.
- `primary_site`: A property that returns the first `CompanySite` object in the sites list.

#### Methods
- `create_contact`: Creates a new `CompanyContact` object and appends it to the contacts list.
- `create_site`: Creates a new `CompanySite` object and appends it to the sites list.
- `create_credit_score`: Creates a new `CreditScore` object associated with the company.
- `create_auth_token`: Creates a new `CompanyEmailAuthToken` object and returns the token in plain text.
````

## File: python/app/models/companies/site.py
````python
"""This is a site that a company has. This is where utilities are provided to."""
⋮----
class CompanySite(BuildableORMBase)
⋮----
"""This is a site that a company has. This is where utilities are provided to."""
__tablename__ = "company_site"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
address = Column(String)  # Legacy field
address_id = Column(ForeignKey("entity_address.id"), nullable=True)
address_object = relationship("EntityAddress", uselist=False)
postcode = Column(String)
# TODO (Stephen): This needs to be removed the mpan/mprn will live on the address
mpan = Column(MPANType)
mprn = Column(String)  # TODO Olly: Provide better typing for MPRN
company_id = Column(ForeignKey("company.id", ondelete="CASCADE"), nullable=False)
utilities_managed = Column(TextPickleType())
"""
    This is an array of the utility types that Watt manages for this site
    stored as a TextPickleType so that we can store the array in a single column
    """
⋮----
@validates("postcode")
    def validate_postcode(self, _, postcode: str)
⋮----
"""Validate that the postcode is in the correct format."""
⋮----
def matches(self, *_args: Any, **kwargs: Any) -> bool
⋮----
"""Returns True if the CompanySite matches the keyword arguments passed in"""
⋮----
def is_utility_managed(self, utility_type: UtilityType) -> bool
⋮----
"""Does Watt manage the given utility for this site?"""
⋮----
def get_not_quotable_reason(self, utility_type: UtilityType) -> Union[str, None]
⋮----
"""
        Can we provide a quote for this site, for the given utility type?
        Returns:
            None if we can provide a quote, otherwise a string explaining why we can't
        Note that this only checks the things that we need (e.g. mpan), there's
        still a chance that UDCore gives us no quotes for this site.
        """
# A constant that we can return if we can provide a quote
CAN_PROVIDE_QUOTE = None
````

## File: python/app/models/companies/sole_trader.py
````python
"""
This file contains extra models required for the SoleTraderCompany model.
The SoleTraderCompany model is stored in the company.py file,
because it is a subclass of the Company model
and therefore needs to be in the same file.
"""
⋮----
class SoleTraderPersonalDetails(BuildableORMBase)
⋮----
"""This table is used to store personal details for sole traders"""
__tablename__ = "soletrader_personal_details"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id"), nullable=False)
"""Links this personal details to a company (a SoleTraderCompany)"""
date_of_birth = Column(DateTime, nullable=False)
"""The date of birth of the sole trader"""
addresses = relationship("SoleTraderPersonalAddress")
"""The addresses that the sole trader has lived at"""
class SoleTraderPersonalAddress(BuildableORMBase)
⋮----
"""This table is used to store personal addresses for sole traders"""
__tablename__ = "soletrader_personal_address"
⋮----
soletrader_personal_details_id = Column(
"""Links this address to a sole trader"""
address = Column(String, nullable=True)  # Legacy field
postcode = Column(String, nullable=True)  # Legacy field
"""The postcode of the address"""
address_id = Column(ForeignKey("entity_address.id"), nullable=True)
address_object = relationship("EntityAddress", uselist=False)
"""The address text"""
move_in_date = Column(DateTime, nullable=False)
"""The date that the sole trader moved into the address"""
move_out_date = Column(DateTime, nullable=True)
"""
    The date that the sole trader moved out of the address
    This is nullable, because they may still live there.
    """
````

## File: python/app/models/electricity/tariffs.py
````python
class UsageTariffs(TypedDict)
⋮----
day: float
night: float
weekend: float
````

## File: python/app/models/electricity/usage.py
````python
def _get_usages_by_tpr_tariff_count(tpr_tariff_count: int) -> UsageTariffs
⋮----
"""
    Get the usage percentages for a given TPR tariff count
    TODO (James) there is some desire to have an "Evening and Weekend" tariff,
            as well as an "Evening, Weekend and Night" tariff. These are not supported
            by UDCore at the moment, so we just use day/night/weekend for now.
    # ^ Not true, UDCore supports all the tariffs, we just don't have many MPAN examples
    # that can be used to test them. Aaron provided some in the past we will need to dig or
    # ask him for them again.
    :param tpr_tariff_count: the number of TPR tariffs
    """
# 0 shouldn't happen, but if it does, we'll just assume it's a day tariff
# 1 is a day tariff
⋮----
# TODO (Stephen): I think there was TWO typs of 2 tariffs
# 2 tariffs is probably a day/night tariff
⋮----
# TODO (Stephen): there is definitely at least 2 types of 3 tariffs
# if there are 3 tariffs, it's probably a day/night/weekend tariff
⋮----
class ElectricityUsage(BuildableORMBase)
⋮----
"""Electricity usage for a company site"""
__tablename__ = "electricity_usage"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id"), nullable=False)
utility_type = Column(ChoiceType(UtilityType, impl=Integer()), nullable=False)
# Stephen: This is the new contract start date captured on the usage page
start_date = Column(DateTime, nullable=True)
total_annual_usage = Column(Integer, nullable=False)
# if associated provider is deleted this electricity usage object will be too
provider_id = Column(ForeignKey("provider.id", ondelete="cascade"), nullable=False)
non_watt_provider_name = Column(String, nullable=True, default=None)
post_code = Column(String)
mpan = Column(MPANType, nullable=True, default=None)
# fuck
usage = Column(TextPickleType())
day_unit_rate = Column(Integer)
smart_meter = Column(Boolean, nullable=False, default=False)
"""
    The number of TPR tariffs associated with this contract usage
    Used to determine how we split the EAC into day/night/weekend
    """
tpr_tariff_count = Column(Integer, nullable=False, default=0)
⋮----
@property
    def usage_split_by_tariff(self) -> UsageTariffs
⋮----
"""Get the usage split by tariff"""
⋮----
@property
    def usage_amount_by_tariff(self) -> UsageTariffs
⋮----
"""Get the usage amount by tariff"""
tariffs = _get_usages_by_tpr_tariff_count(self.tpr_tariff_count)
````

## File: python/app/models/gas/usage.py
````python
class GasUsage(BuildableORMBase)
⋮----
__tablename__ = "gas_usage"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id"), nullable=False)
utility_type = Column(ChoiceType(UtilityType, impl=Integer()), nullable=False)
# Stephen: This is the new contract start date captured on the usage page
start_date = Column(DateTime, nullable=True)
total_annual_usage = Column(Integer, nullable=False)
provider_id = Column(ForeignKey("provider.id"), nullable=False)
non_watt_provider_name = Column(String, nullable=True, default=None)
post_code = Column(String)
mprn = Column(String, nullable=True, default=None)
usage = Column(TextPickleType())
day_unit_rate = Column(Integer)
smart_meter = Column(Boolean, nullable=False, default=False)
````

## File: python/app/models/quotes/constants.py
````python
LOCAL_DEV_TIMEOUT = timedelta(seconds=100)
PROD_TIMEOUT = timedelta(hours=24)
````

## File: python/app/models/quotes/quote_list.py
````python
"""Quote list model"""
⋮----
class QuoteList(BuildableORMBase)
⋮----
"""A list of quotes for a company"""
__tablename__ = "quote_list"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
utility_type = Column(ChoiceType(UtilityType, impl=Integer()), nullable=False)
__mapper_args__ = (
⋮----
company_id = Column(ForeignKey("company.id"), nullable=False)
active = Column(Boolean, default=True, nullable=False)
# TODO (James) consider loading technique https://docs.sqlalchemy.org/en/14/orm/loading_relationships.html
quotes = relationship("Quote", backref="quote_list", cascade="all,delete")
def archive(self)
⋮----
@property
    def expired(self) -> bool
⋮----
"""Whether the quote list has expired."""
expiry = PROD_TIMEOUT
⋮----
expiry = LOCAL_DEV_TIMEOUT
⋮----
class ElectricityQuoteList(QuoteList)
⋮----
"""A list of electricity quotes"""
# required so we don't lose __mapper_args__ from base class
⋮----
"""
    TODO (James) some people say we shouldn't need this, but we seem to
    https://stackoverflow.com/a/34724424/1916362
    """
__table_args__ = {"extend_existing": True}
"""The total annual usage in kWh"""
electricity_total_annual_usage = Column(Float, nullable=True)
"""The percentage of usage in each tariff split"""
electricity_tariff_usage_splits = Column(TextPickleType(), nullable=True)
"""The actual usage in each tariff split"""
electricity_tariff_usage_values = Column(TextPickleType(), nullable=True)
⋮----
"""Initialise an electricity quote list."""
⋮----
@property
    def tariff_usage_splits(self) -> UsageTariffs
⋮----
"""The percentage of usage in each tariff split."""
⋮----
@property
    def tariff_usage_values(self) -> UsageTariffs
⋮----
"""The actual usage in each tariff split."""
⋮----
class GasQuoteList(QuoteList)
⋮----
"""A list of gas quotes"""
⋮----
gas_total_annual_usage = Column(Float, nullable=True)
def __init__(self, total_annual_usage: float, **kwargs)
⋮----
"""Initialise a gas quote list"""
⋮----
@property
    def total_annual_usage(self) -> float
⋮----
"""The total annual usage in kWh"""
````

## File: python/app/models/quotes/quote.py
````python
"""Quote model"""
⋮----
class Quote(BuildableORMBase)
⋮----
"""A quote for a utility"""
__tablename__ = "quote"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
utility_type = Column(ChoiceType(UtilityType, impl=Integer()), nullable=False)
__mapper_args__ = (
⋮----
quote_list_id = Column(ForeignKey("quote_list.id"))
active = Column(Boolean, default=True, nullable=False)
"""The provider of this quote"""
provider_id = Column(ForeignKey("provider.id"), nullable=False)
duration = Column(Integer, nullable=False)
end_date = Column(DateTime, nullable=False)
"""
    Represents whether this quote is a comparison quote or not, i.e.
    will it be used to show as the "current supplier" quote in the comparison grid.
    """
is_comparison_provider = Column(Boolean, nullable=False, default=False)
def archive(self)
class ElectricityQuote(Quote)
⋮----
"""
    An electricity quote
    As this model uses single-table inheritance, you **must not** add any
    columns with `nullable=False` to this model. See docs/sqlalchemy_tips.md for more info.
    """
# required so we don't lose __mapper_args__ from base class
__mapper_args__ = Quote.__mapper_args__.copy() if Quote.__mapper_args__ else {}
⋮----
"""
    TODO (James) some people say we shouldn't need this, but we seem to
    https://stackoverflow.com/a/34724424/1916362
    """
__table_args__ = {"extend_existing": True}
# TODO (Olly): In future update to use Integer to work with decimals ie:
# 30.50000 -> 3050000
# ! We must not do maths with these values until they are Integers !
day_unit_rate = Column(Float, nullable=True)
evening_unit_rate = Column(Float, nullable=True)
night_unit_rate = Column(Float, nullable=True)
weekend_unit_rate = Column(Float, nullable=True)
off_peak_unit_rate = Column(Float, nullable=True)
capacity_charge_kva = Column(String, nullable=True)
# TODO (James) consider whether the properties below should be moved to the base class
# as they are common to both electricity and gas quotes
annual_price = Column(Float, nullable=True)
standing_charge = Column(Float, nullable=True)
price_guaranteed = Column(Integer, nullable=True)
contract_type = Column(String, nullable=True)
class GasQuote(Quote)
⋮----
"""
    A gas quote
    As this model uses single-table inheritance, you **must not** add any
    columns with `nullable=False` to this model. See docs/sqlalchemy_tips.md for more info.
    """
⋮----
unit_rate = Column(Float, nullable=True)
````

## File: python/app/models/reports/quote_rag.py
````python
"""Company Quote RAG report model"""
⋮----
class QuoteRag(BaseModel)
⋮----
"""Quote RED AMBER GREEN model data that is exported to CSV
    Args:
        BaseModel (BaseModel): Pydantic base model
    """
# General details
state: str
campaign_id: Optional[str] = Field(None)
utility_type: str = Field(None)
current_supplier: Optional[str] = Field(None)
new_supplier: Optional[str] = Field(None)
credit_score: Optional[str] = Field(None)
target_email: Optional[str] = Field(None)
utilities_available: int = Field(None)
# Contact details
contact_email: Optional[str] = Field(None)
contact_phone_number: Optional[str] = Field(None)
contact_name: Optional[str] = Field(None)
# Company details
mpr: Optional[str] = Field(None)
"""
    The meter point reference number (MPR) is a unique number that identifies a meter.
    For electricity meters, this is an MPAN. For gas meters, this is an MPRN.
    """
company_name: Optional[str] = Field(None)
company_number: Optional[str] = Field(None)
business_type: Optional[str] = Field(None)
site_address: Optional[str] = Field(None)
site_postcode: Optional[str] = Field(None)
company_id: Optional[str] = Field(None)
"""
    TODO (James) we need to refactor the 'electricity' fields to be prefixed with 'electricity_' to make this
    suitable for when we introduce gas.
    """
# Quote details
contract_duration: Optional[str] = Field(None)
contract_end_date: Optional[str] = Field(None)
is_comparison_provider: Optional[str] = Field(None)
contract_type: Optional[str] = Field(None)
day_unit_rate: Optional[str] = Field(None)
night_unit_rate: Optional[str] = Field(None)
weekend_unit_rate: Optional[str] = Field(None)
evening_unit_rate: Optional[str] = Field(None)
off_peak_unit_rate: Optional[str] = Field(None)
annual_price: Optional[str] = Field(None)
standing_charge: Optional[str] = Field(None)
capacity_charge: Optional[str] = Field(None)
price_guarantee: Optional[str] = Field(None)
# Usage details
annual_estimated_usage: Optional[str] = Field(None)
# Contract details
new_contract_start_date: Optional[str] = Field(None)
new_contract_end_date: Optional[str] = Field(None)
contract_comments: Optional[str] = Field(None)
contract_signed: Optional[str] = Field(None)
contract_signature: Optional[str] = Field(None)
contract_utility_type: Optional[str] = Field(None)
# Bank details
bank_name: Optional[str] = Field(None)
account_holder_name: Optional[str] = Field(None)
account_number: Optional[str] = Field(None)
account_sort_code: Optional[str] = Field(None)
is_verified: Optional[str] = Field(None)
verified_at: Optional[str] = Field(None)
# Agreements
version: Optional[str] = Field(None)
authorized: Optional[str] = Field(None)
credit_check: Optional[str] = Field(None)
letter_of_authority: Optional[str] = Field(None)
terms_and_conditions: Optional[str] = Field(None)
smart_meter_agreement: Optional[str] = Field(None)
# Sole trader details
sole_trader_dob: Optional[str] = Field(None)
sole_trader_address: Optional[str] = Field(None)
class QuoteRagStates(Enum)
⋮----
"""
    Enumeration of possible Quote RAG states and the corresponding pages they map to.
    """
LANDING = ("LANDED ON COMPANY", None)
USAGE = ("ENTERING USAGE", [r"^\/electricity$", r"^\/gas$"])
QUOTE = ("SELECTING QUOTE", [r"^\/electricity/quote$", r"^\/gas/quote$"])
SIGNING = ("SIGNING CONTRACT", [r"^\/electricity/contract", r"^\/gas/contract"])
COMPLETE = ("CONTRACT COMPLETE", [r"^\/$"])
UNDEFINED = ("N/A", None)
def __init__(self, state_name: str, pages: list[str])
⋮----
"""
        Initializes a new QuoteRagStates enum member with the
        specified state name and page.
        Args:
            state_name (str): The name of the state.
            page (list[str]): The list of pages that map to this state.
        """
⋮----
@classmethod
    def from_page(cls, page: str) -> "QuoteRagStates"
⋮----
"""
        Returns the QuoteRagStates enum member that corresponds to the specified page.
        Args:
            page (str): The page to look up.
        Returns:
            QuoteRagStates: The enum member that corresponds to the page,
            or UNDEFINED if no match is found.
        """
````

## File: python/app/models/utils/pickle.py
````python
# TODO maybe this size is too small?
SIZE = 256
class TextPickleType(TypeDecorator)
⋮----
"""
    Serialise a dict (or other JSONable thing) to a string and vice versa
    """
impl = String(length=SIZE)
def process_bind_param(self, value, _)
⋮----
"""This is called when the value is set on the model."""
⋮----
def process_result_value(self, value, _)
⋮----
"""This is called when the value is retrieved from the database."""
⋮----
value = json.loads(value)
⋮----
def python_type(self)
⋮----
"""This is called when the value is used as a bind parameter."""
⋮----
def process_literal_param(self, value, _)
````

## File: python/app/models/common.py
````python
"""Common SQLAlchemy models."""
⋮----
# Set the timezone to Britain
britain_timezone = pytz.timezone("Europe/London")
⋮----
@declarative_mixin
class TimeMixinPostgres
⋮----
"""A mixin for SQLAlchemy (Postgres) models that adds created_at and updated_at fields."""
⋮----
@declared_attr
@classmethod
    def __tablename__(cls)
created_at = Column(
updated_at = Column(
# This makes the model load default values (e.g. created_at) eagerly
__mapper_args__ = {"eager_defaults": True}
Base = declarative_base(cls=TimeMixinPostgres)
class MPANType(TypeDecorator)
⋮----
"""
    A SQLAlchemy type for MPANs.
    This is used to convert MPANs to and from the database.
    """
impl = String
⋮----
@property
    def python_type(self)
def process_bind_param(self, value: MPAN, _)
process_literal_param = process_bind_param
def process_result_value(self, value: str, _)
⋮----
mpan = MPAN(value)
````

## File: python/app/models/contracts.py
````python
"""
This file contains the models for contracts.
They are bound to the Postgres database using SQLAlchemy
"""
⋮----
class Contract(BuildableORMBase)
⋮----
"""This is a contract between a company and a quote.
    Args:
        Base (Base): The base model that all models inherit from
    """
__tablename__ = "contract"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
company_id = Column(ForeignKey("company.id"), nullable=False)
# This is unique to enforce that a Quote can only have one Contract
quote_id = Column(
quote = relationship("Quote")
is_signed = Column(Boolean, nullable=False, default=False)
signature = Column(String, nullable=True)
utility_type = Column(ChoiceType(UtilityType, impl=Integer()), nullable=False)
status = Column(
comments = Column(String, nullable=True)
pdf_url = Column(String, nullable=True)  # TODO refactor to pdf_key
start_date = Column(DateTime, nullable=False)
end_date = Column(DateTime, nullable=False)
````

## File: python/app/models/email.py
````python
"""This module contains the models for email verification."""
⋮----
class CompanyEmailVerification(BuildableORMBase)
⋮----
"""
    This model is used to store the validation code for a company email address.
    It doesn't have a relation to `Company`,
    because the email verification happens before the
    `Company` is created.
    """
__tablename__ = "company_email_verification"
email = Column(EmailType, primary_key=True, unique=True)
code = Column(String, nullable=False)
__table_args__ = (
⋮----
@validates("email")
    def validate_email(self, _key, email) -> str
⋮----
"""Validate that the email is in the correct format."""
⋮----
@validates("code")
    def validate_code(self, _key, code) -> str
⋮----
"""Validate that the code is exactly 6 characters long."""
````

## File: python/app/models/enums.py
````python
"""This module contains all the enums used in the application"""
⋮----
class EmailType(str, Enum)
⋮----
"""Type of reminder"""
# Current supplier page not auto populated user left flow without submitting information
CURRENT_SUPPLIER_INFORMATION = ("CURRENT_SUPPLIER_INFORMATION",)
# User has seen quotes but didn't select one
QUOTE_SIGNUP = ("QUOTE_SIGNUP",)
# User selected a quote but hasn't provided bank details and clicked "Sign Contract"
SIGN_CONTRACT = ("SIGN_CONTRACT",)
# User has requested verification code
SEND_VERIFICATION = ("SEND_VERIFICATION",)
# User has re-requested verification code
RESEND_VERIFICATION = ("RESEND_VERIFICATION",)
# User encountered an error during quote flow send internal email for mannual review
QUOTE_FAILURE = ("QUOTE_FAILURE",)
# User has requested to rejoin an existing flow by entering fuzzy matched company details
SEND_REJOIN_EXISTING_FUZZY_MATCHED_COMPANY = (
class BusinessType(int, Enum)
⋮----
"""This class contains constants for the business type."""
LTD = 1
CHARITY = 2
SOLE_TRADER = 3
⋮----
@classmethod
    def list(cls)
⋮----
"""Return a list of all the business constants"""
⋮----
def name_capitalized(self)
⋮----
"""Return the name of the business type capitalized"""
⋮----
class UtilityType(int, Enum)
⋮----
"""Utility Constants"""
ELECTRICITY = 1
GAS = 2
WATER = 3
TELECOM = 4
INTERNET = 5
⋮----
"""List of utility constants"""
⋮----
"""Return the name of the utility type capitalized"""
⋮----
class ContractStatus(int, Enum)
⋮----
"""Status Constants"""
PROCESSING = 1
REJECTED = 2
ACCEPTED = 3
LIVE = 4
RENEWAL = 5
⋮----
"""Get a list of values of the enum"""
````

## File: python/app/models/industry.py
````python
"""Indusry model"""
⋮----
class Industry(BuildableORMBase)
⋮----
"""This is a lookup table for industries"""
__tablename__ = "industry"
id = Column(Text, primary_key=True, unique=True)
name = Column(String)
companies = relationship(
````

## File: python/app/models/orm_builder.py
````python
""" The SQLAlchemy ORM Model Builder class. Initialize the Builder with """
⋮----
T = TypeVar("T")
class SQLAlchemyField(Protocol)
⋮----
"""Protocol to represent SQLAlchemy fields
    Args:
        Protocol (Protocol): Protocol class
    """
type: T
class ORMModelBuilder
⋮----
"""
    The SQLAlchemy ORM Model Builder class. Initialize the Builder with
    a custom ORM Model to get a Builder object for the particular model.
    You can set a field in the model in any of 2 ways below.
    1. builder.customField = customValue
    2. builder = builder.customField(customValue)
    The 2nd way above allows to chain the method calls.
    Examples:
        ```python
        contract = (
            Contract.builder()
            .id(uuid.uuid4())
            .company_id(1)
            .quote_id(2)
            .is_signed(True)
            .signature("test_signature")
            .utility_type(UtilityType.ELECTRICITY)
            .status(ContractStatus.PROCESSING)
            .comments("Test comments")
            .pdf_url("https://example.com/pdf")
            .start_date("2023-01-01 00:00:00")
            .end_date("2023-12-31 00:00:00")
            .build()
        )
        ```
    """
__slots__ = ("__model__", "__values__")
def __init__(self, model: Type[Base]) -> None
⋮----
"""
        Construct a Builder object for an SQLAlchemy ORM Model class
        Args:
            model: The ORM Model Class
        """
⋮----
def __getattr__(self, name: str) -> Callable[[T], "ORMModelBuilder"]
⋮----
field: SQLAlchemyField = getattr(self.__model__, name)
field_type = field.type.python_type
def method(value: field_type) -> "ORMModelBuilder"
⋮----
def __setattr__(self, key, value)
def set_field(self, value: object, name: str) -> "ORMModelBuilder"
⋮----
"""
        Set the ORM Model field attribute.
        The value of the attribute can be another instance
        of `ORMModelBuilder`.
        The `build` method is called to update the fields
        Args:
            value:
                The value of the field. Value can be another builder.
                The `build` method will be called
                if the value is a subclass of ORMModelBuilder
            name: The name of the field
        Returns:
            The Builder object
        """
# Check if the value is an instance of `ORMModelBuilder` and
# call `build()` method to update value
⋮----
def build(self) -> Type[Base]
⋮----
"""
        Builds the ORM Model. Calling this method returns
        a Model object with all the values
        passed to it.
        Returns:
            The Model object
        """
⋮----
class BuildableORMBase(Base)
⋮----
"""
    Adds capability to build an SQLAlchemy ORM
    model step by step instead of generating
    the entire object by passing the values to the constructor.
    Get the Model builder by calling the
    `Builder` method which in turn constructs
    an object of BuildableORMBase type.
    """
__abstract__ = True
⋮----
@classmethod
    def builder(cls)
⋮----
"""Get the Model builder
        Returns:
            ORMModelBuilder: The Model Builder
        """
````

## File: python/app/models/providers.py
````python
"""Provider model for current and quoted providers"""
⋮----
class Provider(BuildableORMBase)
⋮----
"""Provider model for current and quoted providers"""
__tablename__ = "provider"
id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
name = Column(String, nullable=False, unique=True)
# whether or not to serve to client
# should be `false` when a contract needs to be updated or if we don't quote for them
is_enabled = Column(Boolean, nullable=False, server_default=expression.true())
smart_meter_required = Column(
electra_link_id = Column(String, nullable=False)
# This is the supplier_name (recco_id) used to identify the provider against the Aperture database.
# We get the supplier_id from the recco_id (supplier_name).
recco_id = Column(String, nullable=True)
# TODO (Stephen): udcore_id is being populated with ud_quote['name'] which is not possibly unqiue
# TODO (Stephen): We think we have an issue non unique udcore_id describing quotes. see 'create_quote_list_from_ud_quotes'
# udcore_is only used to filter our static list of suppliers against the suppliers fetched with udcore api
# so it can be nullable as when we fetch our providers we just check if it has a udcore id
# We could have 3 quotes (1,2,3 years) from British gas for example all with the same name. 'Provider.find_one({'udcore_id': ud_quote["name"]})'
udcore_id = Column(String, nullable=True)
# This is an array of the utility types that the provider supports
# stored as a TextPickleType so that we can store the array in a single column
utilities_provided = Column(TextPickleType())
logo_file_name = Column(String, nullable=False)
# determines if the provider is displayed on the supplier grid
is_displayed_on_current_supplier_list = Column(
def provides_utility(self, utility_type: UtilityType) -> bool
⋮----
"""Does this provider provide the given utility?"""
````

## File: python/app/models/pydantic_builder.py
````python
"""Pydantic model builder."""
⋮----
T = TypeVar("T", bound=BaseModel)
class BuildablePydanticBase(BaseModel)
⋮----
"""A base class for pydantic models that can be built using a builder.
    Args:
        BaseModel (BaseModel): The base model
    Returns:
        BuildablePydanticBase: The buildable pydantic base
    """
⋮----
@classmethod
    def builder(cls)
⋮----
"""Create a builder for this model.
        Returns:
            PydanticModelBuilder: The builder
        """
⋮----
def __init_subclass__(cls, **kwargs)
class PydanticModelBuilder
⋮----
"""A builder for pydantic models.
    Args:
        T (TypeVar): The type of the model to build
    Returns:
        PydanticModelBuilder: The builder
    """
__slots__ = ("__model__", "__values__")
def __init__(self, model: Type[T]) -> None
⋮----
"""Initialize the builder.
        Args:
            model (Type[T]): The model to build
        """
⋮----
def __getattr__(self, name: str) -> Callable[[T], "PydanticModelBuilder"]
⋮----
"""Get an attribute.
        Args:
            name (str): The name of the attribute
        Returns:
            Callable[[T], "PydanticModelBuilder"]: The attribute
        """
def method(value: T) -> "PydanticModelBuilder"
⋮----
"""Set a field.
            Args:
                value (T): The value of the field
            Returns:
                PydanticModelBuilder: The builder
            """
⋮----
def __setattr__(self, key, value)
⋮----
"""Set an attribute.
        Args:
            key: The key of the attribute
            value: The value of the attribute
        Raises:
            KeyError: If the attribute is not a valid field
        Returns:
            None
        """
⋮----
def set_field(self, value: object, name: str) -> "PydanticModelBuilder"
⋮----
"""Set a field.
        Args:
            value (object): The value of the field
            name (str): The name of the field
        Raises:
            KeyError: If the field is not a valid field
        Returns:
            PydanticModelBuilder: The builder
        """
⋮----
def build(self) -> T
⋮----
"""Build the model.
        Returns:
            T: The model
        """
````

## File: python/app/repository/banking.py
````python
"""
    Gets the copany bank details for the specific contract.
    Users can provide multiple bank details, so we need to ensure we show the correct one
    """
result = await session.execute(
````

## File: python/app/repository/companies.py
````python
"""Companies repository"""
⋮----
options = (
⋮----
"""
    Get a Company by id
    :param company_id: The company id
    """
⋮----
result = await session.execute(
company: Company = result.scalars().first()
⋮----
"""Get all companies from database
    Args:
        session (AsyncSession): database session
        filters (list): list of filters to apply to query
    Returns:
        list[Company]: list of companies
    """
result = await session.execute(select(Company).where(*filters).options(*options))
⋮----
def get_generic_company_registration_number(company: Company) -> str
⋮----
"""
    Get the generic company registration number for companies (returning `None` for
    charities and sole traders).
    Args:
        company (Company): The company
    Returns:
        _type_: The registration number
    """
⋮----
"""Update the selected contract for a company
    Args:
        company (Company): The company
        contract (Contract): The contract
        session (AsyncSession): The database session
    Raises:
        ValueError: _description_
        Unauthorized: _description_
    Returns:
        _type_: _description_
    """
⋮----
"""Update the MPRN for a company's primary site
    Args:
        company (Company): The company
        mprn (str): The MPRN
        session (AsyncSession): The database session
    Returns:
        bool: True if successful, False otherwise
    TODO (James) this will need changing for multi-site, recommend to use a CompanySite parameter instead.
    """
⋮----
# await session.flush()
⋮----
"""Update the MPAN for a company's primary site
    Args:
        company (Company): The company
        mpan (str): The MPAN
        session (AsyncSession): The database session
    Returns:
        bool: True if successful, False otherwise
    TODO (James) this will need changing for multi-site, recommend to use a CompanySite parameter instead.
    """
⋮----
"""
    Get a LtdCompany by registation number
    """
⋮----
company: LtdCompany = result.scalars().first()
⋮----
"""
    Get a CharityCompany by charity number
    """
⋮----
company: CharityCompany = result.scalars().first()
⋮----
"""
    Get a SoleTraderCompany by name
    """
⋮----
company: SoleTraderCompany = result.scalars().first()
⋮----
"""
    Get an Industry by id
    """
result = await session.execute(select(Industry).where(Industry.id == industry_id))
industry: Industry = result.scalars().first()
⋮----
"""Create a new company email verification."""
company_email_verification = CompanyEmailVerification(email=email, code=code)
⋮----
class CompanyEmailNotVerifiedException(Exception)
⋮----
"""Company email not verified exception"""
⋮----
"""Update a company email verification."""
company_email_verification = await get_company_email_verification_by_email(
⋮----
"""
    Get a company email verification by email
    """
filters = [CompanyEmailVerification.email == email]
⋮----
result = await session.execute(select(CompanyEmailVerification).where(*filters))
⋮----
async def get_banking_details_by_company_id(company_id: str, session: AsyncSession)
⋮----
"""Get CompanyBankingDetails by company id"""
⋮----
"""
    Get CompanyCreditScore by company id
    :param company_id: the company ID to filter on
    """
result: CreditScore = (
⋮----
async def get_site_by_mpan(mpan: MPAN, session) -> CompanySite
⋮----
"""Returns the CompanySite object corresponding to the given MPAN."""
⋮----
result = await session.execute(select(CompanySite).where(CompanySite.mpan == mpan))
⋮----
async def get_site_by_mprn(mprn: str, session) -> CompanySite
⋮----
result = await session.execute(select(CompanySite).where(CompanySite.mprn == mprn))
⋮----
async def get_contact_by_properties_and(properties: dict, session) -> CompanyContact
⋮----
"""Returns the CompanyContact object corresponding to the given properties."""
result = await session.execute(select(CompanyContact).filter_by(**properties))
⋮----
async def get_contact_by_properties_or(properties: dict, session) -> CompanyContact
⋮----
filter_criteria = or_(
result = await session.execute(select(CompanyContact).filter(filter_criteria))
⋮----
"""
    Get CompanyAcquisitionInfo by id
    :param company_acquisition_info_id: The company acquisition info id
    :param session: The database session
    """
⋮----
"""
    Get CompanyAcquisitionInfo by target email hash
    :param target_email: The base64 encoded email hash
    :param session: The database session
    """
⋮----
"""Create a new company acquisition info.
    Args:
        target_email (str): The target email
        campaign_id (str): The campaign id
        session (AsyncSession): The database session
    """
company_acquisition_info = CompanyAcquisitionInfo(
⋮----
"""Get all CompanyAcquisitionInfo by filter
    Args:
        filters (list, optional): The filter. Defaults to None.
        session (AsyncSession): The database session
    Returns:
        list[CompanyAcquisitionInfo]: The list of CompanyAcquisitionInfo
    """
result = await session.execute(select(CompanyAcquisitionInfo).where(*filters))
````

## File: python/app/repository/constants.py
````python
"""Constants for repository """
MAX_COMMENTS_LENGTH = 256
````

## File: python/app/repository/contracts.py
````python
"""Contract repository """
⋮----
# Set the timezone to Britain
britain_timezone = pytz.timezone("Europe/London")
⋮----
"""
    Gets a contract by contract_id
    """
⋮----
filters = [Contract.id == contract_id]
result = await session.execute(
⋮----
"""
    Gets a contract by company_id
    as well as any other (optional) filters
    """
⋮----
other_filters = []
⋮----
filters = [Contract.company_id == company_id, *other_filters]
⋮----
# TODO (James) should we warn if this returns more than one?
⋮----
"""
    Gets all contracts by company_id
    as well as any other (optional) filters
    """
⋮----
return result.scalars().all()  # Return all contracts
⋮----
"""Create a new Contract"""
# (Jude): check if a contract with specified company_id exists
⋮----
found_contract: Contract = result.scalars().first()
⋮----
contract = Contract(
⋮----
# TODO: this is a temporary fix as in contract model the created_at field populates with wrong time (hour before) need to research why, this bug only arises with the created_at field and for some reason not the updated_at field
⋮----
# (Jude): add only if contract doesn't exist
⋮----
"""Update the pdf_key of a contract"""
contract = await get_contract(
⋮----
"""
    Sign a contract and add the signed pdf_key
    TODO (James) this probably belongs in services more than repository
    """
⋮----
# create a signed PDF from the contract
signed_pdf_key = sign_pdf_for_contract(
````

## File: python/app/repository/industry.py
````python
"""
    Gets an Industry by id
    """
result = await session.execute(select(Industry).where(Industry.id == industry_id))
````

## File: python/app/repository/provider.py
````python
"""A Provider that provides electricity to a customer."""
⋮----
"""
    Gets a Provider by id
    """
⋮----
result = await session.execute(
⋮----
async def get_all_providers(session: AsyncSession) -> Union[List[Provider], None]
⋮----
"""
    Gets all Providers with udcore_id
    """
⋮----
"""
    Gets a Provider by recco_id
    """
⋮----
"""
    Gets a Provider by name
    """
result = await session.execute(select(Provider).where(Provider.name == name))
⋮----
"""
    Gets a Provider by electra_link_id
    """
⋮----
"""Create provider not currently in providers.ts list
    Args:
        name (str): name of provider
        electra_link_id (str): electra link id of provider
        utilities_provided (list[UtilityType]): utilities that provider supports
        session (AsyncSession): database session
    Raises:
        Exception: _description_
    """
⋮----
new_provider = Provider(
````

## File: python/app/repository/quotes.py
````python
async def delete_quote_list(quote_list: QuoteList, session: AsyncSession)
⋮----
"""
    Deletes a QuoteList
    TODO (James) this should be a soft delete, i.e. QuoteList.active = False
    """
⋮----
"""
    Gets a QuoteList by company_id
    as well as any other (optional) filters
    """
⋮----
other_filters = []
⋮----
filters = [QuoteList.company_id == company_id, *other_filters]
result = await session.execute(
# TODO (James) should we warn if this returns more than one?
quote_list: QuoteList = result.scalars().first()
````

## File: python/app/repository/usage.py
````python
"""Electricity Usage Repository"""
⋮----
"""
    Create a contract usage
    :param data: the contract usage data
    :param session: the database session
    :return: the contract usage
    """
⋮----
utility_usage = UtilityDispatch.USAGE_MODEL[utility_type](**data)
⋮----
# await session.flush()
⋮----
"""
    Gets the most recent (by created_at date) contract usage for a company and utility type
    """
utility_model = UtilityDispatch.USAGE_MODEL[utility_type]
filters = [
result = await session.execute(
usage = result.scalars().first()
````

## File: python/app/repository/utility_dispatch.py
````python
""" Dispatch module for usage API. """
⋮----
class UtilityDispatch
⋮----
"""Dispatch map utility type"""
# TODO: (Olly) - potentially place this within the enum class itself.
USAGE_MODEL = {
QUOTES_LIST_KEY_MAP = {
````

## File: python/app/app.py
````python
"""
This file is responsible for setting up the Sanic application.
However, it isn't the main entry point for the application.
The main entry point is in server.py
"""
⋮----
sanic_app = Sanic(settings.app_name)
⋮----
# TODO (James) this breaks for new versions of Sanic
# if settings.debug_mode:
#     from sanic_openapi import openapi3_blueprint
#     sanic_app.blueprint(openapi3_blueprint)
#     sanic_app.config.API_SCHEMES = ["https"]
#     sanic_app.config.API_TITLE = "Watt Quote"
#     sanic_app.config.API_VERSION = "0.0.1"
#     sanic_app.config.API_DESCRIPTION = "Watt Quote API"
⋮----
@sanic_app.on_request
async def set_request_id(request)
⋮----
@sanic_app.listener("after_server_start")
async def setup_application(application, _)
⋮----
"""Setup the application"""
⋮----
@sanic_app.listener("after_server_stop")
async def shutdown_application(application, _)
⋮----
"""Shutdown the application"""
⋮----
@sanic_app.get("health")
def health(_)
⋮----
"""Health check endpoint"""
⋮----
app = sanic_app
````

## File: python/app/config.py
````python
"""
This is a settings file for the application.
It loads the settings from the environment variables and provides
a way to access them in the application.
The settings are loaded from the environment variables using the
pydantic library through the BaseSettings class.
"""
⋮----
#
class CSRFConfig(BaseModel)
⋮----
"""CSRF settings for the application"""
DOMAIN: str
REF_PADDING: int = 12
REF_LENGTH: int = 18
# TODO (Stephen) use Fernet.generate_key() to generate a new key but currently it changes on save
REF_SECRET: bytes = "DZsM9KOs6YAGluhGrEo9oWw4JKTjdiOot9Z4gZ0dGqg="
# TODO (James) rename this to Settings or AppSettings or something as it's not
#           just the default settings, but the actual settings for the app
class DefaultSettings(BaseSettings)
⋮----
"""Default settings for the application"""
CSRF: CSRFConfig
# application settings
app_name: str
debug_mode: bool
# portal database settings
# TODO (James) use Pydantic's PostgresDsn type
PORTAL_DB_USED: str
PORTAL_DB_USER: str
PORTAL_DB_PASSWORD: str
PORTAL_DB_HOST: str
PORTAL_DB_PORT: str
PORTAL_DB_NAME: str
# redis cache settings
# TODO (James) use Pydantic's RedisDsn type
IN_MEMORY_SERVER_URL: str
# TODO (James) rename this to SESSION_STORAGE
COOKIE_STORAGE: str
# TODO (James) rename this to REVOKED_SESSION_STORAGE
REVOKED_STORAGE: str
CODES: str
# British Gas settings
BGAS_URL: str
# Experian Aperture settings
EA_URL: str
EA_API_SECRET_NAME: str
# ElectraLink settings
EL_URL: str
EL_API_SECRET_NAME: str
# campaign secret
MARKETING_CAMPAIGN_SECRET_NAME: str
# 3rd party api settings
UDCORE_API_URL: str
UDCORE_API_KEY: str
# should the application make requests to third party services?
# e.g. lambdas, external HTTP endpoints
# this is useful for testing locally
LOCAL_MODE: bool = False
EXTERNAL_API_CALLS_BUCKET_NAME: str
UNSIGNED_CONTRACTS_BUCKET_NAME: str
SIGNED_CONTRACTS_BUCKET_NAME: str
SIGNED_LOA_BUCKET_NAME: str
UNSUPPORTED_PROVIDERS_BUCKET_NAME: str
QUOTE_FAILURE_EMAIL: str
# business quotes settings
QUOTES_AVAILABILITY = 30  # 30 days available quotes
CONTRACTS_AVAILABILITY = 30  # 30 days for signable contracts
QUOTE_FE_APP_URL: str
CRM_APP_URL: str
CRM_PRIVATE_API_KEY: str
PORTAL_FE_APP_URL: str
CONTRACT_COMPLETION_EMAIL: str
ENVIRONMENT: str
class Config
⋮----
"""Pydantic config class"""
case_sensitive = True
settings = DefaultSettings()
````

## File: python/app/constants.py
````python
"""Constants"""
class CookieNames
⋮----
"""Cookie names"""
SESSION_TOKEN = "session_token"
REF_TOKEN = "ref_token"
CSRF_TOKEN = "csrf_token"
````

## File: python/app/logging.py
````python
class RequestIdFilter(logging.Filter)
⋮----
def filter(self, record)
def getLogConfig(local_mode)
⋮----
log_colors = {
# Define the logging format and add color to it
log_format = "%(log_color)s [%(levelname)s] [%(request_id)s] %(message)s"
access_format = (
formatter = colorlog.ColoredFormatter(log_format, log_colors)
access_formatter = colorlog.ColoredFormatter(access_format, log_colors)
# Define the log handler
console_handler = logging.StreamHandler()
⋮----
access_handler = logging.StreamHandler()
````

## File: python/app/server.py
````python
"""
This is the entry point for the application. It is responsible for starting the
Sanic application and registering the blueprints.
"""
⋮----
# pylint: disable=unused-import
import app.logging  # isort:skip
# The blueprints are registered here because if they are registered in app.py
# then there are circular imports.
⋮----
@sanic_app.on_response
def set_response_headers(_: Request, response)
⋮----
"""Add security headers to the response."""
⋮----
@sanic_app.on_request
def check_request_headers(request: Request)
⋮----
"""Check the request headers and reject the request if they are invalid."""
⋮----
origin = request.headers.get("origin")
⋮----
# TODO (Stephen): appears to sometimes fail, so commenting out for now
# raise InvalidUsage("Invalid Origin")
def _get_worker_count()
⋮----
"""Get the number of workers to use for the server."""
# TODO (James) set worker count to 1 when running locally
⋮----
# cpu_cores = multiprocessing.cpu_count()
⋮----
# TODO (James) we can use the `fast` parameter instead of `workers`
````

## File: python/docs/db_migrations.md
````markdown
[<- Return to root README](/README.md)

[<- Return to Quote README](../README.md)

# Database migrations

Database migrations are a mechanism to upgrade our database schema (adding, modifying and removing tables/columns) in response to changes in our model structure.

A migration file is effectively a list of instructions to change a database. The migration files are then applied to the database in order.

When the models change, or a new model is added, a new migration file must be generated. When the changes to those models are deployed, the migration file must be applied to the database.

## Creating new migration file

If you have modified the models (in `app/models/`) you probably need to generate a new migration file.

There is a script to create the migration file for you. You can provide a migration name as a parameter.

You must run this command from inside the root folder.

```sh
make migration-create
> Add email to customer
```

## Apply migrations to local database

You can apply migrations to your local database (to bring your local DB up to date with the code) with the following command.

You must run this command from inside the root folder.

```sh
make migration-apply
```

## Rolling back migration on local database

To roll back a migration one step, you can run the below command:

You must run this command from inside the root folder.

```sh
make migration-rollback-one
```

## Applying migrations to RDS database

To apply migrations to an RDS instance we need to connect to the ECS task and run a command inside the task.

You will need the following prerequisites:

- AWS CLI configured with `aws configure` (region is `eu-west-2`)
- [SessionManagerPlugin](https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html) installed

To apply the migration use

```sh
make migration-apply-cloud
> Environment (dev, qat, prod): dev
```
````

## File: python/docs/local_mode.md
````markdown
[<- Return to root README](/README.md)

[<- Return to Quote README](../README.md)

# Local mode

By default, the application will run in "local mode". The CI pipeline also runs in local mode.

Cloud instances do not run in local mode.

An instance running in local mode will be prevented from:

- making calls to 3rd party services
- invoking the AWS services (lambda/s3)

(**todo**: (@jameskmonger) create some specific list of what local_mode affects)

Predefined mocks will be used in place of genuine responses for these integrations.

(**todo**: (@jameskmonger) where can the mocks be found?)

To disable local mode, you can change the `LOCAL_MODE` value in `docker-compose.yml` to `0` and run `sh ./local-helpers/rebuild.sh`. Or you can add `LOCAL_MODE=0` in the `.env.secret` file. If you are turning local mode off, you will need to set API keys for the 3rd party services. Speak to another developer to get those keys.
````

## File: python/docs/tests.md
````markdown
[<- Return to root README](/README.md)

[<- Return to Quote README](../README.md)

# Tests

## Unit tests

To run unit tests, you will need to have the Docker Daemon running (see [Requirements and Setup](/docs/requirements_and_setup.md))

You can run the Quote unit tests (powered by **Pytest**) with the following command:

```sh
make test-unit
```

## Integration tests

To run integration tests it has the same setup as above for unit tests.

Run integration tests with the following command:
```sh
make test-integration
```

### Important:

- You must be in the root directory to run `make test`
- You need to have the docker daemon running
- Maybe a good idea to widen terminal window to see output because of docker logs formatting
````

## File: python/README.md
````markdown
[<- Return to root README](../README.md)

# Quote service

This is the Quote backend.

It is a Python service using [Sanic](https://sanic.dev/en/).

The database is [Postgres](https://www.postgresql.org/) ([AWS RDS](https://aws.amazon.com/rds/) in production), which we access through [SQLAlchemy](https://www.sqlalchemy.org/). We use migrations powered by [Alembic](https://alembic.sqlalchemy.org/en/latest/).

User sessions are stored in [Redis](https://redis.io/)

## Documentation

- [Database Migrations](./docs/db_migrations.md)
- [Local Mode](./docs/local_mode.md)
- [Tests](./docs/tests.md)

### Linting

The Python code is linted by `black`, `isort` and `autoflake`. You can fix the linting issues by running the script `./scripts/fixup.sh`.

Fake change testing CI
````
