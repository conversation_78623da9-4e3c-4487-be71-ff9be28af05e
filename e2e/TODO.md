1. Complete the full quote / sign contract flow
   1. Electric
   2. Gas
   3. Electric and Gas
2. Complete company page with different addresses for different types of meters
   1. MPAN
      1. profile class, mtc, llf (top-line) and bottom-line (core)
      2. Should run successfully for (01, 02 - domestic) and (03, 04 - commercial) meters.
      3. Half hourly vs non half hourly
3. Re-entering a previously quoted site / company
   1. When same company/site but different contact information (check inbox for previous session) - Bidur
   2. If I quote companyA siteA then companyB siteA it should allow it but currently doesn't - <PERSON>id<PERSON> is fixing.
   3. If I quote companyA siteA then siteB it fails on siteB - Can't easily fix this. (Lots of hard coded references to company.sites[0] making it extremly risky to change) - Bidur makes it throw an error (not sending rejoin email) instead now.
4. Should work for `Limited`, `Sole`, and `Charity
5. Test both Business address is and is not same as site address.
6. `Smart meter` if they do not select you get less quotes.
7. Must select `Direct debit` or we throw an error.

# Usage page

1. Keep original found usage
2. Meters that have no usage information found (Electric & gas)
3. Manually overriding the found usage information. E.g. it returns 0-5000 and user edits to say 12,345 then refreshing the page should show a banded range of 10,000-15,000.
4. Email to customer it shows range, in app it should also show range. The PDF at the end shows exact value (Business accepted exposing this value as competitors using us to check their own custmer usage have to go through many pages).
5. Check with and without `I am out of contract`

# Quote page

1. Select quote and immediately sign contract
2. Select a quote then go back view others then eventually sign contract
3. Select a quote then go back view others then eventually sign contract the first one you viewed

# Contract page

1. Fully complete the form (including view the pdf)

# After signing

1. Try to quote electric again for the same site again after you've signed the contract (should fail) - Can't remember what it currently does...
2. Try to quote gas for the site same site after you've signed electric (this should work)

Stephen has data for meters
