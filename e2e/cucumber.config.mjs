import { getAvailableMemory } from "./utils/system-memory-info.js";

const workersPerGbOfMemory = getAvailableMemory();

// Determine parallel workers setting based on environment variable
const shouldRunParallel = process.env.RUN_PARALLEL === "true";
const parallelWorkers = shouldRunParallel ? workersPerGbOfMemory : 1;

export default {
	loader: ["ts-node/esm"],
	import: ["features/step_definitions/**/*.ts"],
	paths: ["features/**/*.feature"],
	format: ["summary", "progress-bar"],
	parallel: parallelWorkers,
	retry: 1,
	formatOptions: {
		snippetInterface: "async-await",
	},
};
