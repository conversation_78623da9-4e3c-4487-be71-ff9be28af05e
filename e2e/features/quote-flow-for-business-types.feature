Feature: Complete Quote Form for Different Business Types
  As a business customer
  I want to obtain a quote for my business type
  So that I can compare energy prices for my company and obtain a quote

  Sc<PERSON><PERSON>: Successfully complete the quote form for a limited company
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "13824863" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page

  Scenario: Successfully complete the quote form for a charity
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Charity" from the "business type" dropdown
    And I enter "Scottish Charity" in the "business name" field
    And I enter "SC011159" in the "charity number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "John" in the "contact forename" field
    And I enter "Smith" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page

Scenario: Successfully complete the quote form for a sole trader
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Sole Trader" from the "business type" dropdown
    And I enter "Plumbbob" in the "business name" field
    And I enter "BN1 6SE" in the "postcode" field
    And I select "245, Preston Road, Brighton, East Sussex, BN1 6SE" from the "address" dropdown
    And I use the "Move in date" datepicker to select "01/01/2000"
    And I click the "Add another address" button
    And I enter "OX4 3PB" in the "postcode" field
    And I select "2, Lytton Road, Oxford, Oxfordshire, OX4 3PB" from the "address" dropdown
    And I use the "Move in date" datepicker to select "01/01/2000"
    And I use the "Move out date" datepicker to select "01/10/2000"
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "John" in the "contact forename" field
    And I enter "Smith" in the "contact surname" field
    And I use the "date of birth" datepicker to select "01/01/1980"
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page
