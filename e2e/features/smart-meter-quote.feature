Feature: Smart Meter Quote Displays More Quotes
  As a business customer
  I want to obtain a gas quote and contract
  So that I can view energy pricing for my company

  Scenario: Verify smart meter shows more quotes
    Given I am on the "/" page
    When I click the "Gas" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "BN43 5DA" in the "site address postcode" field
    And I select "2a, High Street, Shorehan-by-dea, West Sussex, BN43 5DA" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "14256871" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/gas" page
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    And I count the quotes on the page
    When I click the browser's back button
    And I click the browser's back button
    When I enter "BN43 5DA" in the "site address postcode" field
    And I select "2a, High Street, Shorehan-by-dea, West Sussex, BN43 5DA" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "14256871" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "John" in the "contact forename" field
    And I enter "Smith" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/gas" page
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    And I should see more quotes on the page
