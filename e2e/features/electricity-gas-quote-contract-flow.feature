Feature: Complete Duel Fuel Quote Contract Flow
  As a business customer
  I want to obtain a dual fuel quote and sign a contract
  So that I can secure energy pricing for my company

  # Need to change the postcode when re-running the test
  Background:
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Gas" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "OX3 0AJ" in the "site address postcode" field
    And I select "2, Bowness Avenue, Headington, Oxford, Oxfordshire, OX3 0AJ" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "15833051" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page

  Scenario: Complete Electricity quote flow with future start date and, out of contract for gas
    When I use the "Start date of new contract" datepicker to select "01/05/2025"
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the "1 year" year option
    And I click the "SIGN ME UP" button
    Then I should be redirected to the "/electricity/contract" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Doe" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Doe" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
    And I click the "Sign Contract" button
    Then I should be redirected to the "/gas" page
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Doe" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Doe" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
    And I click the "Sign Contract" button
    Then I should be redirected to the "/success" page
    When I click the "electricity" contract download button
    And I click the "gas" contract download button
    And I click the "Get your quote" button in the "gas" section
    Then the new tab URL should redirect to "/"
    When I close the new tab
    #todo: add a step to check the new tab URL is "/"
    Then I should be redirected to the "/success" page
    When I click the "Give us a call" button in the "energy solutions" section
    Then the new tab URL should redirect to "/contact"
    When I close the new tab
    Then I should be redirected to the "/success" page
