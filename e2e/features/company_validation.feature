Feature: Company Page Validation
  As a business customer
  I want to validate the company page
  So that I can ensure the form can be submitted successfully

  Sc<PERSON><PERSON>: Check the mandatory fields show required message when left blank
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I click the "Business address is the same as the site address" checkbox
    And I click the "Submit" button
    Then I should see an error message "Required information." in the "site address postcode" field
    And  I should see an error message "Required information." in the "site address" field
    And I should see an error message "Required information." in the "site address postcode" field
    And I should see an error message "Required information." in the "business type" field
    And I should see an error message "Required information." in the "contact email" field
    And I should see an error message "Required information." in the "business phone number" field
    And I should see an error message "Required information." in the "contact forename" field
    And I should see an error message "Required information." in the "contact surname" field
    And I should see an error message "Required information." in the "position" field
    And I should see an error message "Required information." in the "business address postcode" field
    And I should see an error message "Required information." in the "business address" field
    And I should see an error message "Required information." for the "I confirm I am authorized" checkbox
    And I should see an error message "Required information." for the "I agree to a credit check" checkbox
    And I should see an error message "Required information." for the "I agree with signing the Letter of Authority" checkbox
    And I should see an error message "Required information." for the "I agree with the Terms and Conditions" checkbox


  Scenario: Validate postcode field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "not a postcode" in the "site address postcode" field
    Then I should see an error message "No addresses were found." in the "site address" field

  Scenario: Validate business registration number field with invalid input for limited company
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I click the "Submit" button
    Then I should see an error message "Required information." in the "business registration number" field
    When I enter "1234" in the "business registration number" field
    Then I should see an error message "Invalid registration number." in the "business registration number" field

  Scenario: Validate charity number field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Charity" from the "business type" dropdown
    And I click the "Submit" button
    Then I should see an error message "Required information." in the "business name" field
    And I should see an error message "Required information." in the "charity number" field
    When I enter "test" in the "business name" field
    And I enter "1234" in the "charity number" field
    And I click the "Submit" button
    Then I should see an error message "Charity number must be at least six digits." in the "charity number" field

  Scenario: Validate sole trader postcode field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Sole Trader" from the "business type" dropdown
    And I click the "Submit" button
    Then I should see an error message "Required information." in the "business name" field
    And I should see an error message "Required information." in the "postcode" field
    When I enter "not a postcode" in the "postcode" field
    Then I should see an error message "Invalid postcode" in the "postcode" field

  Scenario: Validate sole trader date of birth field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Sole Trader" from the "business type" dropdown
    Then I should see an error message "Enter date: 01/01/1900 - 12/31/2009" in the "date of birth" field

  Scenario: Validate email field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "invalidemail" in the "contact email" field
    Then I should see an error message "Invalid email address." in the "contact email" field

  Scenario: Validate business phone number field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "11" in the "business phone number" field
    And I click the "Submit" button
    Then I should see an error message "Invalid phone number." in the "business phone number" field

   Scenario: Validate business address postcode field with invalid input
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I click the "Business address is the same as the site address" checkbox
    And I enter "not a postcode" in the "business address postcode" field
    Then I should see an error message "No addresses were found." in the "business address" field
