Feature: Complete Gas Quote and Contract Flow
  As a business customer
  I want to obtain and sign a gas quote and contract
  So that I can secure energy pricing for my company

  Background:
    Given I am on the "/" page
    When I click the "Gas" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "BN43 5DA" in the "site address postcode" field
    And I select "2a, High Street, Shorehan-by-dea, West Sussex, BN43 5DA" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "14256871" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/gas" page


  Scenario: Complete quote flow when out of contract
    When I click the "I am out of contract" checkbox
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract/" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Smith" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Smith" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
    # TODO can't submit the form yet otherwise re-running it will fail.
    # And I click the "Sign Contract" button
    # Then I should be redirected to the "/thank-you" page

  Scenario: Complete quote flow with future start date
    When I use the "Start date of new contract" datepicker to select "01/04/2025"
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract/" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Doe" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Doe" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
# TODO can't submit the form yet otherwise re-running it will fail.
# And I click the "Sign Contract" button
# Then I should be redirected to the "/thank-you" page

  Scenario Outline: Go back and select a different quote then sign contract flow
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "<contract_years>" year option
    Then I should see "<year> Years fixed price" in the contract details
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract" page
    And I should see "<year> Years fixed price" in the contract details
    When I click the browser's back button
    Then I should be redirected to the "/gas/quote" page
    When I click the "<contract_years_2>" year option
    Then I should see "<year_2> Years fixed price" in the contract details
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract" page
    And I should see "<year_2> Years fixed price" in the contract details
    When I click the browser's back button
    Then I should be redirected to the "/gas/quote" page
    And I count the quotes on the page
    When I click the "FILTER BY SUPPLIER"
    And I deselect the top checkbox
    And I click the "<contract_years>" year option
    And I click the "<contract_years_2>" year option
    Then I should see less quotes on the page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract" page
    Examples:
      | contract_years     |year| contract_years_2  |year_2|
      | 2 years            |2   | 3 years           |3     |
