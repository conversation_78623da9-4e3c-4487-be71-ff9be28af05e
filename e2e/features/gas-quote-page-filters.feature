Feature: Gas Quote Page Selecting Filters
  As a business customer
  I want to obtain and sign a gas quote and contract
  So that I can secure energy pricing for my company

  Background:
    Given I am on the "/" page
    When I click the "Gas" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "BN43 5DA" in the "site address postcode" field
    And I select "2a, High Street, Shorehan-by-dea, West Sussex, BN43 5DA" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "14256871" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/gas" page

   Scenario Outline: Select different contract years and verify quotes for gas
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "<contract_years>" year option
    Then I should see "<year> Years fixed price" in the contract details
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract" page
    And I should see "<year> Years fixed price" in the contract details

    Examples:
      | contract_years     |year|
      | 1 year             |1   |
      | 2 years            |2   |
      | 3 years            |3   |
      | 4 years            |4   |
      | 5 years            |5   |

  Scenario: Deselect all suppliers and verify no quotes are found
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "FILTER BY SUPPLIER"
    And I uncheck all checkboxes in the "Filter by supplier" filter
    Then I should see "No Quotes Found" on the page
    And I should see "There are no quotes available for the selected filters." on the page

  Scenario: Deselect the top supplier and verify quotes update
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "FILTER BY SUPPLIER"
    And I count the quotes on the page
    And I deselect the top checkbox
    Then I should see less quotes on the page
