Feature: Complete Quote Form for Different Business Address
  As a business customer
  I want to obtain a quote for a different business address
  So that I can compare energy prices for my company and obtain a quote

  Sc<PERSON>rio: Complete quote form for a limited company with a different business address
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "SN7 7WD" in the "site address postcode" field
    And I select "12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "13824863" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    When I click the "Business address is the same as the site address" checkbox
    And I enter "GL1 4TU" in the "business address postcode" field
    And I select "46, Conduit Road, Gloucester, Gloucestershire, GL1 4TU" from the "business address" dropdown
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page
    When I click the "I am out of contract" checkbox
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/electricity/contract/" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Smith" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Smith" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
