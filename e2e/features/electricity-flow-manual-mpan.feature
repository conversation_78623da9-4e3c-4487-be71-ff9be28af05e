Feature: Electricity Quote and Contract Flow for Manual MPAN Input
  As a business customer
  I want to obtain an electricity quote for my manual MPAN
  So that I can view energy pricing for my company

  Background:
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "CF62 3AD" in the "site address postcode" field
    And I select "Llancarfan Village Hall, Llancarfan, Llancarfan, Barry, South Glamorgan, CF62 3AD" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "00340076" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page

  @TODO
  Scenario: Complete Electricity quote flow out of contract with manual MPAN entry
    When I click the "I am out of contract" checkbox
    # Enter the full MPAN number based on the standard format
    And I enter "00" in the MPAN "profile class" field
    And I enter "845" in the MPAN "meter time switch code" field
    And I enter "02D" in the MPAN "line loss factor" field
    And I enter "15" in the MPAN "distributor id" field
    And I enter "9103" in the MPAN "meter point id number" field
    And I enter "0695" in the MPAN "meter point id number part two" field
    And I enter "521" in the MPAN "check digit" field
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I select "Quote Option 1" from the "available quotes" dropdown
    And I click the "Next" button
    Then I should be redirected to the "/sign-contract" page
    When I click the "I agree to the terms and conditions" checkbox
    And I enter "John Smith" in the "signature" field

  @TODO
  Scenario: Complete Electricity quote flow with future start date and manual MPRN
    When I use the "Start date of new contract" datepicker to select "01/04/2025"
    And I enter "***********" in the "MPRN" field
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "British Gas" supplier logo
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract/" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Doe" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Doe" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
