Feature: Gas Quote and Contract Flow for Manual MPRN Input
  As a business customer
  I want to obtain a gas quote for my manual MPRN
  So that I can view energy quotes for my company

  Background:
    Given I am on the "/" page
    When I click the "Gas" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "CF62 3AD" in the "site address postcode" field
    And I select "Llancarfan Village Hall, Llancarfan, Llancarfan, Barry, South Glamorgan, CF62 3AD" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "11389136" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/gas" page

  @TODO
  Scenario: Gas quote flow when out of contract and entering a manual MPRN
    When I click the "I am out of contract" checkbox
    And I enter "***********" in the "MPRN" field
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    Then I should be redirected to the "/gas/contract/" page
    When I click the "I agree to the terms and conditions" checkbox
    And I enter "John Smith" in the "signature" field

  @TODO
  Scenario: Quote flow with future start date and entering a manual MPRN
    When I use the "Start date of new contract" datepicker to select "01/04/2025"
    And I enter "***********" in the "MPRN" field
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "British Gas" supplier logo
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/gas/quote" page
    When I click the "SIGN ME UP" button
    Then I should be redirected to the "/gas/contract/" page
    When I enter "Barclays" in the "Bank name" field
    And I enter "John Doe" in the "Name of the account holder" field
    And I enter "********" in the "Account number" field
    And I enter "108800" in the "Sort code" field
    And I enter "John Doe" in the "Your signature" field
    And I enter "Optional comments about meter readings" in the "Comments" field
    And I click the "I confirm that I am authorized to sign this contract" checkbox
    And I click the "I understand that from the signed price, Watt.co.uk will receive a commission" checkbox
    And I click the "Download Contract" button
