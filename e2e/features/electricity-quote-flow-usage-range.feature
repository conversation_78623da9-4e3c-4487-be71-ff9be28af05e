Feature: Electricity Quote flow showing Usage Range
  As a business customer
  I want to obtain an electricity quote and view the usage range
  So that I can view energy quotes for my company

  Background:
    Given I am on the "/" page
    When I click the "Electricity" checkbox
    And I click the "Get a quote" button
    Then I should be redirected to the "/company" page
    When I enter "B13 9LQ" in the "site address postcode" field
    And I select "A1, Wakefield Court, Hayfield Road, Birmingham, West Midlands,B13 9LQ" from the "site address" dropdown
    And I select "Limited Company" from the "business type" dropdown
    And I enter "10564531" in the "business registration number" field
    And I enter "<EMAIL>" in the "contact email" field
    And I click the "Send verification email" button
    And I enter "000000" in the "verification code" field
    And I click the "Verify" button
    And I enter "***********" in the "business phone number" field
    And I enter "<PERSON>" in the "contact forename" field
    And I enter "<PERSON>" in the "contact surname" field
    And I enter "Manager" in the "position" field
    And I click the "I confirm I am authorized" checkbox
    And I click the "I agree to a credit check" checkbox
    And I click the "I agree with signing the Letter of Authority" checkbox
    And I click the "I agree with the Terms and Conditions" checkbox
    And I click the "I agree to a Smart Meter installation" checkbox
    And I click the "I agree to pay via Direct Debit" checkbox
    And I click the "Submit" button
    Then I should be redirected to the "/electricity" page

  Scenario: Manually entering a usage (kWh) defaults to a usage range when navigating back
    When I click the "I am out of contract" checkbox
    And I enter "4500" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the browser's back button
    Then I should be redirected to the "/electricity" page
    And I should see "0-5000" within the range in the "Your estimated annual usage (kWh)" field
    And I enter "10000" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the browser's back button
    Then I should be redirected to the "/electricity" page
    And I should see "5000-10000" within the range in the "Your estimated annual usage (kWh)" field
    And I enter "10002" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the browser's back button
    Then I should be redirected to the "/electricity" page
    And I should see "10000-15000" within the range in the "Your estimated annual usage (kWh)" field
    And I enter "15002" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the browser's back button
    Then I should be redirected to the "/electricity" page
    And I should see "15000-20000" within the range in the "Your estimated annual usage (kWh)" field
    And I enter "20002" in the "Your estimated annual usage (kWh)" field
    And I click the "SEE QUOTE" button
    Then I should be redirected to the "/electricity/quote" page
    When I click the browser's back button
    Then I should be redirected to the "/electricity" page
    And I should see "20000+" within the range in the "Your estimated annual usage (kWh)" field
