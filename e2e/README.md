# End to End Tests

Test automation framework using Cucumber.js, <PERSON><PERSON>, and <PERSON><PERSON> for Gherkin Business Driven Development (BDD) tests with natural language step definitions.

---

## Quick Start

Assuming your machine is already set up with **Node.js** (version ≥20) and **pnpm** (version ≥9):

1. **Navigate to the `e2e` directory**:

   ```bash
   cd path/to/project/e2e
   ```

   Replace `path/to/project` with the actual path to your project.

2. **Install dependencies**:

   ```bash
   pnpm install
   ```

3. **Set the OpenAI API key**:
   - Create a file named `.env` in the `e2e` directory with the following content:

     ```
     OPENAI_API_KEY=your-key-here
     ```

   - Replace `your-key-here` with your actual OpenAI API key.

4. **Run tests**:

   ```bash
   pnpm test:cucumber
   ```

**Note**: For detailed setup instructions (e.g., installing Node.js and pnpm), see [Machine Setup](#machine-setup-for-windows).

---

## Table of Contents

- [End to End Tests](#end-to-end-tests)
  - [Quick Start](#quick-start)
  - [Table of Contents](#table-of-contents)
  - [Machine Setup (For Windows)](#machine-setup-for-windows)
  - [Usage](#usage)
    - [Write Tests](#write-tests)
    - [Execute](#execute)
  - [Notes](#notes)

---

## Machine Setup (For Windows)

If your machine doesn't have Node.js and pnpm installed, follow these steps to set up the environment:

1. **Install Node.js**
   - Download the Long-Term Support (LTS) version from [nodejs.org](https://nodejs.org/).
   - Run the installer with default settings.
   - Verify installation by opening Command Prompt and running:

     ```bash
     node -v
     npm -v
     ```

     You should see version numbers (e.g., `v20.x.x` for Node.js and `10.x.x` for npm).

2. **Install pnpm**
   - In Command Prompt, run:

     ```bash
     npm install -g pnpm
     ```

   - Verify installation:

     ```bash
     pnpm -v
     ```

     Expect a version like `9.x.x`.

3. **Navigate to the `e2e` directory**
   - Open Command Prompt and run:

     ```bash
     cd path/to/project/e2e
     ```

     Replace `path/to/project` with the actual path to your project.

4. **Install Project Dependencies**
   - Run:

     ```bash
     pnpm install
     ```

   - This command installs all dependencies and automatically installs Playwright browsers via the `postinstall` script.

5. **Set API Keys**
   - Create a file named `.env` in the `e2e` directory with:

     ```
     OPENAI_API_KEY=your-key-here
     ```

   - Replace `your-key-here` with your actual OpenAI API key.

---

## Usage

### Write Tests

- Add Gherkin `.feature` files in the `features` directory (relative to the `e2e` directory).
- Reuse existing steps in `features/step_definitions/common.ts`.
- Define new steps in the same file, using parameterized steps (e.g., `"I click {string}"`), maximizing reuse across features.

### Execute

- Run all tests:

  ```bash
  pnpm test:cucumber
  ```

- Run specific tests by tagging them with `@only` above the `Scenario:` line in the `.feature` file:

  ```bash
  pnpm test:cucumber --tags @only
  ```

---

## Notes

- **Framework Details**: Uses Cucumber.js for Gherkin syntax and Stagehand for AI-driven Playwright actions.
- **Selector Strategy**: Avoids brittle selectors by relying on natural language commands.
- **Examples**: Check the `features` directory for sample tests.
- **Troubleshooting**: If browsers fail to launch, ensure they are installed with:

  ```bash
  pnpm exec playwright install
  ```

- **Resources**:
  - [Node.js Documentation](https://nodejs.org/en/docs/)
  - [pnpm Documentation](https://pnpm.io/)
