{"name": "test-stagehand", "type": "module", "scripts": {"build": "tsc", "start": "tsx index.ts", "postinstall": "playwright install", "test:cucumber": "cucumber-js --config cucumber.config.mjs"}, "dependencies": {"@ai-sdk/google": "^1.1.16", "@ai-sdk/openai": "^1.1.13", "@browserbasehq/sdk": "2.3.0", "@browserbasehq/stagehand": "1.13.1", "@playwright/test": "^1.49.1", "ai": "^4.1.45", "boxen": "^8.0.1", "chalk": "^5.3.0", "dotenv": "^16.4.7", "openai": "^4.85.4", "zod": "^3.22.4"}, "devDependencies": {"@cucumber/cucumber": "^11.2.0", "@types/cucumber": "^7.0.3", "@types/node": "^22.13.4", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.0.0"}, "packageManager": "pnpm@9.15.1", "engines": {"node": ">=20", "npm": "please-use-pnpm", "pnpm": ">=9", "yarn": "please-use-pnpm"}, "pnpm": {"patchedDependencies": {"@browserbasehq/stagehand": "patches/@browserbasehq__stagehand.patch"}}}