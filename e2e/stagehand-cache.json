{"Check the 'Electricity' checkbox": {"description": "Checkbox for Electricity option", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/span[1]/input[1]"}, "Click the 'Get a quote' button": {"description": "GET A QUOTE button that initiates the quote process", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[3]/div[1]/div[1]/button[1]"}, "Type 'SN7 7WD' into the site address postcode field": {"description": "Textbox for Postcode for the site address", "method": "fill", "arguments": ["SN7 7WD"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click to open the site address dropdown": {"description": "Button element that opens the site address dropdown.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]"}, "Select the '12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD' option from the site address dropdown menu": {"description": "Dropdown option for site address '12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD'.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[13]"}, "Click to open the business type dropdown": {"description": "Business type dropdown button labeled 'Select a business type'", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[3]/div[1]/div[1]"}, "Select the 'Limited Company' option from the business type dropdown menu": {"description": "The 'Limited Company' option in the business type dropdown menu", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Type '13824863' into the business registration number field": {"description": "Textbox for Business registration number field", "method": "fill", "arguments": ["13824863"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type '<EMAIL>' into the contact email field": {"description": "Contact email textbox for entering the user email address", "method": "fill", "arguments": ["<EMAIL>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'Send verification email' button": {"description": "SEND VERIFICATION EMAIL button that sends a verification email when clicked.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[1]/div[2]/button[1]"}, "Type '000000' into the verification code field": {"description": "Textbox for entering the verification code", "method": "fill", "arguments": ["000000"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'Verify' button": {"description": "The 'Verify' button used to submit the verification code.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[2]/div[1]/div[2]/button[1]"}, "Type '***********' into the business phone number field": {"description": "Textbox for Business phone number where users can input their business phone number.", "method": "fill", "arguments": ["***********"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[3]/div[1]/input[1]"}, "Type 'John' into the contact forename field": {"description": "Contact Forename(s) textbox field where the user's first name is entered", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[4]/div[1]/input[1]"}, "Type 'Smith' into the contact surname field": {"description": "Textbox for Contact Surname used to capture the contact's surname.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[5]/div[1]/input[1]"}, "Type 'Manager' into the position field": {"description": "Textbox for 'Position within the company'", "method": "fill", "arguments": ["Manager"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[6]/div[1]/input[1]"}, "Check the 'Business address is the same as the site address' checkbox": {"description": "Checkbox for 'Business address is the same as the site address'", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[7]/label[1]/span[1]/input[1]"}, "Check the 'I confirm I am authorized' checkbox": {"description": "I confirm I am authorized to deal with this company's account checkbox", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[1]/label[1]/span[1]/input[1]"}, "Check the 'I agree to a credit check' checkbox": {"description": "Checkbox for agreeing to a credit check being carried out on the company.", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[2]/label[1]/span[1]/input[1]"}, "Check the 'I agree with signing the Letter of Authority' checkbox": {"description": "Checkbox to agree with signing the Letter of Authority, allowing Watt.co.uk to act on the user's behalf.", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[3]/label[1]/div[1]/div[1]/span[1]/input[1]"}, "Check the 'I agree with the Terms and Conditions' checkbox": {"description": "Checkbox for agreeing with the Terms and Conditions of Watt.co.uk", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[4]/label[1]/span[1]/input[1]"}, "Check the 'I agree to a Smart Meter installation' checkbox": {"description": "Checkbox for agreeing to a Smart Meter installation if not already present (optional).", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[5]/label[1]/span[1]/input[1]"}, "Check the 'I agree to pay via Direct Debit' checkbox": {"description": "The checkbox for 'I agree to pay via Direct Debit for this company. (optional)' used to confirm direct debit payment option.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[5]/div[6]/label[1]/span[1]/input[1]"}, "Click the 'Submit' button": {"description": "SUBMIT button that submits the form.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[2]/button[1]"}, "Type 'not a postcode' into the site address postcode field": {"description": "Textbox for the site address postcode field used to enter the postcode.", "method": "fill", "arguments": ["not a postcode"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type 'SN7 7WD' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address', where users can enter the postcode.", "method": "fill", "arguments": ["SN7 7WD"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click to open the 'site address' dropdown": {"description": "Button element that, when clicked, opens the site address dropdown associated with the 'Site address' label.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]"}, "Click the '12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD' option from the 'site address' dropdown menu": {"description": "Option '12, Chapman Crescent, Faringdon, Oxfordshire, SN7 7WD' in the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[13]"}, "Click to open the 'business type' dropdown": {"description": "<PERSON><PERSON> labeled 'Select a business type' that opens the business type dropdown.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[3]/div[1]/div[1]"}, "Click the 'Limited Company' option from the 'business type' dropdown menu": {"description": "Represents the 'Limited Company' option in the 'business type' dropdown.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Type '13824863' into the 'business registration number' field": {"description": "Textbox for entering the business registration number.", "method": "fill", "arguments": ["13824863"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type '<EMAIL>' into the 'contact email' field": {"description": "Textbox for Contact email used to input the contact email address", "method": "fill", "arguments": ["<EMAIL>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type '***********' into the 'business phone number' field": {"description": "Textbox for Business phone number field where the user inputs the business phone number.", "method": "fill", "arguments": ["***********"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[3]/div[1]/input[1]"}, "Type 'John' into the 'contact forename' field": {"description": "Textbox for entering Contact Forename(s).", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[4]/div[1]/input[1]"}, "Type 'Smith' into the 'contact surname' field": {"description": "Textbox for Contact Surname used to enter the customer's surname.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[5]/div[1]/input[1]"}, "Type 'Manager' into the 'position' field": {"description": "Textbox for 'Position within the company' field where the user's role is entered", "method": "fill", "arguments": ["Manager"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[6]/div[1]/input[1]"}, "Type '000000' into the 'verification code' field": {"description": "Textbox for entering the verification code", "method": "fill", "arguments": ["000000"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click to open the 'Start date of new contract' datepicker": {"description": "Textbox for choosing the start date of new contract; clicking it should open the datepicker.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/input[1]"}, "Click the to change the year": {"description": "Button to switch from calendar view to year view, allowing the user to change the year.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/button[1]"}, "Select year '2025'": {"description": "<PERSON><PERSON> labeled '2025' in the year selection view, representing the year 2025", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/button[1]"}, "Select month '04'": {"description": "<PERSON><PERSON> labeled 'Next month' which, when clicked, will change the current month from March (03) to April (04).", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/button[2]"}, "Check the 'I am out of contract' checkbox": {"description": "Checkbox labeled 'I am out of contract' used for indicating that the user is out of contract.", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[1]/label[1]/span[1]/input[1]"}, "Type '4500' into the 'Your estimated annual usage (kWh)' field": {"description": "Textbox for 'Your estimated annual usage (kWh)' where the user enters their estimated annual electricity usage.", "method": "fill", "arguments": ["4500"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Click the 'SEE QUOTE' button": {"description": "<PERSON><PERSON> labeled 'SEE QUOTE' used to display a quote when clicked.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[2]/button[1]"}, "Select day '01'": {"description": "Calendar gridcell representing day 1 (interpreted as '01') in the date picker.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/button[1]"}, "Click the 'OK' button": {"description": "OK button in the dialog used to confirm the action", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[2]/button[2]"}, "Click the 'Charity' option from the 'business type' dropdown menu": {"description": "Option 'Charity' in the 'business type' dropdown menu.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[3]"}, "Click the 'Sole Trader' option from the 'business type' dropdown menu": {"description": "The 'Sole Trader' option from the 'business type' dropdown menu.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[4]"}, "Type '1234' into the 'business registration number' field": {"description": "Textbox for Business registration number", "method": "fill", "arguments": ["1234"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type 'test' into the 'business name' field": {"description": "Textbox for Business name, where the user should type the business name input.", "method": "fill", "arguments": ["test"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type '1234' into the 'charity number' field": {"description": "Textbox for Charity number field", "method": "fill", "arguments": ["1234"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[5]/div[1]/input[1]"}, "Type 'not a postcode' into the 'postcode' field": {"description": "Textbox for 'Postcode for the site address' used to input the site's postcode.", "method": "fill", "arguments": ["not a postcode"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type 'Scottish Charity' into the 'business name' field": {"description": "Textbox for 'Business name' where the user can enter the name of the business.", "method": "fill", "arguments": ["Scottish Charity"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type 'SC011159' into the 'charity number' field": {"description": "Textbox for Charity number field where the user inputs the charity number.", "method": "fill", "arguments": ["SC011159"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[5]/div[1]/input[1]"}, "Type 'not a postcode' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'", "method": "fill", "arguments": ["not a postcode"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type 'invalidemail' into the 'contact email' field": {"description": "Contact email input field where the user's contact email should be entered", "method": "fill", "arguments": ["invalidemail"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type '11' into the 'business phone number' field": {"description": "Textbox for Business phone number", "method": "fill", "arguments": ["11"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[3]/div[1]/input[1]"}, "Type 'not a postcode' into the 'business address postcode' field": {"description": "Textbox for 'Postcode for the business address'. This field is used to input the postcode for the business address.", "method": "fill", "arguments": ["not a postcode"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[7]/div[1]/input[1]"}, "Type 'Plumbbob' into the 'business name' field": {"description": "Textbox for Business name where the user enters the business name.", "method": "fill", "arguments": ["<PERSON><PERSON><PERSON><PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type 'BN1 6SE' into the 'postcode' field": {"description": "Textbox labeled 'Postcode' in the Address History section.", "method": "fill", "arguments": ["BN1 6SE"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[6]/div[1]/div[1]/div[1]/input[1]"}, "Click to open the 'address' dropdown": {"description": "<PERSON>ton to open the 'address' dropdown in the Address History section.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[6]/div[1]/div[2]/div[1]/div[1]/div[1]"}, "Click the '245, Preston Road, Brighton, East Sussex, BN1 6SE' option from the 'address' dropdown menu": {"description": "Option '245, Preston Road, Brighton, East Sussex, BN1 6SE' in the address dropdown menu", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[53]"}, "Check the 'Gas' checkbox": {"description": "The checkbox associated with the 'Gas' option under 'I am interested in'.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[2]/span[1]/input[1]"}, "Click to open the 'Move in date' datepicker": {"description": "Textbox for 'Choose date' associated with the 'Move in date' label, which when clicked, opens the datepicker.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[6]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Select year '2000'": {"description": "Button for year 2000", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[101]/button[1]"}, "Type 'SY13 3DN' into the 'site address postcode' field": {"description": "Textbox for Postcode for the site address where the user needs to enter the postcode.", "method": "fill", "arguments": ["SY13 3DN"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '1, 1 Ash Tree Cottages, Hanmer, Hanmer, Whitchurch, Shropshire, SY13 3DN' option from the 'site address' dropdown menu": {"description": "Option for '1, Ash Tree Cottages, Hanmer, Hanmer, Whitchurch, Shropshire, SY13 3DN' in the site address dropdown menu", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Type '00340076' into the 'business registration number' field": {"description": "Textbox for Business registration number input field", "method": "fill", "arguments": ["00340076"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Click the '1, Ash Tree Cottages, Hanmer, Hanmer, Whitchurch, Shropshire, SY13 3DN' option from the 'site address' dropdown menu": {"description": "Option '1, Ash Tree Cottages, Hanmer, Hanmer, Whitchurch, Shropshire, SY13 3DN' from the 'site address' dropdown menu in the listbox", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Type 'CF62 3AD' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address' where the postcode is entered.", "method": "fill", "arguments": ["CF62 3AD"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'Llancarfan Village Hall, Llancarfan, Llancarfan, Barry, South Glamorgan, CF62 3AD' option from the 'site address' dropdown menu": {"description": "Option 'Lancarfan Village Hall, Llancarfan, Llancarfan, Barry, South Glamorgan, CF62 3AD' in the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[8]"}, "Type '3456' into the 'meter point id number' field": {"description": "Textbox labeled 'MPAN' which represents the meter point id number field where the meter point id is entered.", "method": "fill", "arguments": ["3456"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type '7890' into the 'meter point id number part two' field": {"description": "Textbox for the second segment of the MPAN (meter point id number) field, currently showing '56'. This is likely the 'meter point id number part two' field.", "method": "fill", "arguments": ["7890"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/input[1]"}, "Click the 'MPAN information icon' button": {"description": "MPAN information icon button located next to the MPAN text in the Contract and usage section", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[3]/div[1]/div[1]/button[1]"}, "Type '9103' into the 'meter point id number' field": {"description": "Textbox field labeled 'MPAN' for entering the meter point id number.", "method": "fill", "arguments": ["9103"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Type '0695' into the 'meter point id number part two' field": {"description": "Textbox for MPAN second part (meter point id number part two) where the meter point id is segmented into parts.", "method": "fill", "arguments": ["0695"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/input[1]"}, "Type '521' into the 'check digit' field": {"description": "Textbox for the MPAN check digit (currently displaying '5')", "method": "fill", "arguments": ["521"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[3]/div[1]/div[1]/input[1]"}, "Click the 'Your estimated annual usage (kWh)' field": {"description": "Textbox input field for 'Your estimated annual usage (kWh)'", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Enter '9103' into the 'meter point id number' field": {"description": "Textbox labeled 'MPAN', which likely corresponds to the 'meter point id number' field", "method": "fill", "arguments": ["9103"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Enter '521' into the 'check digit' field": {"description": "Textbox within the MPAN group likely representing the check digit field", "method": "fill", "arguments": ["521"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[3]/div[1]/div[1]/input[1]"}, "Type '56412362211' into the 'MPRN' field": {"description": "Textbox for the MPRN field where users can enter the MPRN value.", "method": "fill", "arguments": ["56412362211"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'White Chapel, Llancarfan, Llancarfan, Barry, South Glamorgan, CF62 3AD' option from the 'site address' dropdown menu": {"description": "Option 'White Chapel, Llancarfan, L<PERSON>carfan, Barry, South Glamorgan, CF62 3AD' from the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[7]"}, "Click to change the year": {"description": "Button to switch calendar view from month mode to year view, allowing the user to change the year.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/button[1]"}, "Type '05349232' into the 'business registration number' field": {"description": "Textbox for Business registration number field where the user enters the registration number.", "method": "fill", "arguments": ["05349232"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type 'CH5 3BS' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'", "method": "fill", "arguments": ["CH5 3BS"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '6, Holywell Road, Holywell, Flintshire, CH5 3BS' option from the 'site address' dropdown menu": {"description": "Option with text '6, Holywell Road, Ewloe, Deeside, Clwyd, CH5 3BS' from the site address dropdown list", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Type '14256871' into the 'business registration number' field": {"description": "Textbox for Business registration number where the user can enter the registration number.", "method": "fill", "arguments": ["14256871"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Click the '6, Holywell Road, Ewloe, Deeside, Clwyd, CH5 3BS' option from the 'site address' dropdown menu": {"description": "Option for '6, Holywell Road, Ewloe, Deeside, Clwyd, CH5 3BS' in the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Type 'BN43 5DA' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address' field, where the user can input the site's postcode.", "method": "fill", "arguments": ["BN43 5DA"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '2, High Street, Shorehan-by-dea, West Sussex, BN43 5DA' option from the 'site address' dropdown menu": {"description": "option: 2, High Street, Shoreham-by-sea, West Sussex, BN43 5DA", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[4]"}, "Click the '2a, High Street, Shorehan-by-dea, West Sussex, BN43 5DA' option from the 'site address' dropdown menu": {"description": "Option element for address '2a, High Street, Shoreham-by-sea, West Sussex, BN43 5DA' within the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Type 'TF6 6HE' into the 'site address postcode' field": {"description": "Textbox for Postcode for the site address where the user can input the postcode.", "method": "fill", "arguments": ["TF6 6HE"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '1, The Fields, Long Lane, Long Lane, Telford, Shropshire, TF6 6HE' option from the 'site address' dropdown menu": {"description": "The option element labeled '1, The Fields, Long Lane, Long Lane, Telford, Shropshire, TF6 6HE' inside the site address dropdown.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Type 'B13 9LQ' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address' where users enter the postcode for the site address", "method": "fill", "arguments": ["B13 9LQ"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'A1, Wakefield Court, Hayfield Road, Birmingham, West Midlands,B13 9LQ' option from the 'site address' dropdown menu": {"description": "Option 'A1, Wakefield Court, Hayfield Road, Birmingham, West Midlands, B13 9LQ' from the site address dropdown menu", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[3]"}, "Type '10564531' into the 'business registration number' field": {"description": "Textbox for business registration number", "method": "fill", "arguments": ["10564531"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Click to open the 'available quotes' dropdown": {"description": "<PERSON><PERSON> labeled 'All energy offers' likely serving as the available quotes dropdown toggle.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/div[1]/div[1]/button[1]"}, "Click the 'SIGN ME UP' button": {"description": "SIGN ME UP button in the first energy offer.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[4]/button[1]"}, "Click the '1 years' button": {"description": "Button for selecting the 1 year option in the energy offers filter.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/div[1]/div[2]/button[1]"}, "Type 'John Smith' into the 'signature' field": {"description": "Textbox for 'Your signature' where the user can type their signature.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[5]/div[1]/input[1]"}, "Type 'Barclays' into the 'Bank name' field": {"description": "Textbox for entering the Bank name.", "method": "fill", "arguments": ["Barclays"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[1]/div[1]/input[1]"}, "Type 'John Smith' into the 'Name of the account holder' field": {"description": "Textbox for 'Name of the account holder' field in the Contract signing form.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[2]/div[1]/input[1]"}, "Type '********' into the 'Account number' field": {"description": "Textbox for entering the account number associated with the 'Account number' label.", "method": "fill", "arguments": ["********"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[3]/div[1]/input[1]"}, "Type '108800' into the 'Sort code' field": {"description": "Textbox for 'Sort code' used to input the sort code value in the contract signing form.", "method": "fill", "arguments": ["108800"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[4]/div[1]/input[1]"}, "Type 'John Smith' into the 'Your signature' field": {"description": "textbox for 'Your signature' field in the Contract signing form", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[5]/div[1]/input[1]"}, "Type 'Optional comments about meter readings' into the 'Comments' field": {"description": "Textbox field labeled 'Comments' where users can input text.", "method": "fill", "arguments": ["Optional comments about meter readings"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[6]/div[1]/input[1]"}, "Check the 'I confirm that I am authorized to sign this contract' checkbox": {"description": "Checkbox labeled 'I confirm that I am authorized to sign this contract.'", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[7]/label[1]/span[1]/input[1]"}, "Check the 'I understand that from the signed price, Watt.co.uk will receive a commission' checkbox": {"description": "Checkbox for commission consent stating 'I understand that from the signed price, Watt.co.uk will receive a commission of no more than 1p/unit.'", "method": "check", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[8]/label[1]/span[1]/input[1]"}, "Click the 'Download Contract' button": {"description": "Download Contract button that allows the user to download the generated contract.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/a[1]/button[1]"}, "Type 'John Doe' into the 'Name of the account holder' field": {"description": "Textbox for 'Name of the account holder' used to input the account holder's name.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[2]/div[1]/input[1]"}, "Type 'John Doe' into the 'Your signature' field": {"description": "Textbox for 'Your signature' where the user can input their signature.", "method": "fill", "arguments": ["<PERSON>"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[5]/div[1]/input[1]"}, "Type '10000' into the 'Your estimated annual usage (kWh)' field": {"description": "Textbox for 'Your estimated annual usage (kWh)' where users enter their estimated annual usage.", "method": "fill", "arguments": ["10000"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Type '10002' into the 'Your estimated annual usage (kWh)' field": {"description": "Textbox for 'Your estimated annual usage (kWh)'", "method": "fill", "arguments": ["10002"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Type '15002' into the 'Your estimated annual usage (kWh)' field": {"description": "Textbox for the 'Your estimated annual usage (kWh)' field where the user can input an estimated annual usage value.", "method": "fill", "arguments": ["15002"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Type '20002' into the 'Your estimated annual usage (kWh)' field": {"description": "Textbox for entering your estimated annual usage (kWh)", "method": "fill", "arguments": ["20002"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[1]/div[4]/div[1]/input[1]"}, "Uncheck all checkboxes in the 'Filter by supplier' filter": {"description": "Checkbox for supplier: EDF Energy (EDFE) inside the 'Filter by supplier' filter.", "method": "uncheck", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]/input[1]"}, "Verify that 'No Quotes Found' is displayed on the page": {"description": "StaticText element displaying 'No Quotes Found', indicating that no quotes are available.", "method": "getByText", "arguments": ["No Quotes Found"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[2]/div[2]"}, "Verify that 'There are no quotes available for the selected filters.' is displayed on the page": {"description": "Paragraph element that displays the message 'There are no quotes available for the selected filters.'", "method": "toHaveText", "arguments": ["There are no quotes available for the selected filters."], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[2]/p[1]"}, "Type 'OX4 3PB' into the 'business address postcode' field": {"description": "Textbox for the business address postcode, labeled 'Postcode for the business address'.", "method": "fill", "arguments": ["OX4 3PB"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[7]/div[1]/input[1]"}, "Type '2, Lytton Road, Oxford, Oxfordshire, OX4 3PB' into the 'business address' field": {"description": "Business address field represented as a combobox where the address can be entered.", "method": "fill", "arguments": ["2, Lytton Road, Oxford, Oxfordshire, OX4 3PB"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[8]/div[1]/div[1]/div[1]"}, "Type 'GL1 4TU' into the 'business address postcode' field": {"description": "Textbox for 'Postcode for the business address'", "method": "fill", "arguments": ["GL1 4TU"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[7]/div[1]/input[1]"}, "Click to open the 'business address' dropdown": {"description": "The combobox for 'Business address' which acts as a dropdown to show business addresses.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[3]/div[8]/div[1]/div[1]/div[1]"}, "Click the '46, Conduit Road, Gloucester, Gloucestershire, GL1 4TU' option from the 'business address' dropdown menu": {"description": "Option for '46, Conduit Street, Gloucester, Gloucestershire, GL1 4TU' in the business address dropdown menu.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Uncheck the top checkbox in the filter": {"description": "Top checkbox in the filter for 'British Gas Lite'.", "method": "uncheck", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]/input[1]"}, "Type 'UB1 2AJ' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'", "method": "fill", "arguments": ["UB1 2AJ"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '12, South Avenue Gardens, Southall, Middlesex, UB1 2AJ' option from the 'site address' dropdown menu": {"description": "Option '12, South Avenue Gardens, Southall, Middlesex, UB1 2AJ' from the site address dropdown menu.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[14]"}, "Type '15241680' into the 'business registration number' field": {"description": "Textbox for Business registration number", "method": "fill", "arguments": ["15241680"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Click the 'Sign Contract' button": {"description": "Button element labeled 'SIGN CONTRACT' used to sign the contract.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/button[1]"}, "Type 'B24 9AG' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'. This is the field where the user should enter the site address postcode.", "method": "fill", "arguments": ["B24 9AG"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '1, Newman Road, Birmingham, West Midlands, B24 9AG' option from the 'site address' dropdown menu": {"description": "Option for '1, Newman Road, Birmingham, West Midlands, B24 9AG' from the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[2]"}, "Select month 'May'": {"description": "Next month button. Clicking this button will advance the calendar month forward. Since the calendar currently displays 'March 2025', clicking it twice should eventually display 'May'.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/button[2]"}, "Type 'OX4 3UJ' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address' where the postcode is entered", "method": "fill", "arguments": ["OX4 3UJ"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '23, Newman Road, Oxford, Oxfordshire, OX4 3UJ' option from the 'site address' dropdown menu": {"description": "Option element representing the address '23, Newman Road, Oxford, Oxfordshire, OX4 3UJ' in the site address dropdown", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[25]"}, "Type 'BN12 6PW' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'", "method": "fill", "arguments": ["BN12 6PW"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the 'Roundstone Farm, Littlehampton Road, Ferring, Worthing, West Sussex, BN12 6PW' option from the 'site address' dropdown menu": {"description": "option: Roundstone Farm, Littlehampton Road, Ferring, Worthing, West Sussex, BN12 6PW", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[30]"}, "Type '09835336' into the 'business registration number' field": {"description": "Textbox for Business registration number where the user is expected to enter the business registration number.", "method": "fill", "arguments": ["09835336"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Type 'OX3 0AJ' into the 'site address postcode' field": {"description": "Textbox for 'Postcode for the site address'", "method": "fill", "arguments": ["OX3 0AJ"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/input[1]"}, "Click the '2, Bowness Avenue, Headington, Oxford, Oxfordshire,OX3 0AJ' option from the 'site address' dropdown menu": {"description": "Option for site address with text '2, Bowness Avenue, Headington, Oxford, Oxfordshire, OX3 0AJ'.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Type '15833051' into the 'business registration number' field": {"description": "Textbox for Business registration number field", "method": "fill", "arguments": ["15833051"], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/form[1]/div[1]/div[1]/div[4]/div[1]/input[1]"}, "Click the '2, Bowness Avenue, Headington, Oxford, Oxfordshire, OX3 0AJ' option from the 'site address' dropdown menu": {"description": "Option for '2, Bowness Avenue, Headington, Oxford, Oxfordshire, OX3 0AJ' from the site address dropdown menu.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/ul[1]/li[5]"}, "Click the 'electricity' contract download button": {"description": "Button within the link for downloading the Electricity contract. It is nested under the 'Electricity contract' section where users are informed they can download the contract and read it offline.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[3]/a[1]/button[1]"}, "Click the 'gas' contract download button": {"description": "Button inside the Gas contract section that allows users to download the gas contract", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[3]/a[1]/button[1]"}, "Click the 'Get your quote' button in the 'gas' section": {"description": "<PERSON><PERSON> labeled 'GET YOUR QUOTE' within the gas section ('Want to renew or switch your utility supplier to reduce Gas bill?').", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/a[1]/button[1]"}, "Click the 'BACK' button": {"description": "<PERSON><PERSON> labeled BACK to navigate to the previous screen.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[1]/div[1]/main[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/button[1]"}, "Select month 'April'": {"description": "Next month button; clicking this should advance the calendar from March to April.", "method": "click", "arguments": [], "selector": "xpath=/html/body[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/button[2]"}}