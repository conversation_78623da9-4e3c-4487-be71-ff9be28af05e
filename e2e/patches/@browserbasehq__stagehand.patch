diff --git a/dist/index.js b/dist/index.js
index a26a87a50a3d9fd3dea900975ca9b404db8e30cc..56fdc3c93bafc7d10325e3dd648b86f4c5c24ea3 100644
--- a/dist/index.js
+++ b/dist/index.js
@@ -2012,21 +2012,24 @@ var StagehandActHandler = class {
           }
         });
         if (newOpenedTab) {
-          this.logger({
-            category: "action",
-            message: "new page detected (new tab) with URL",
-            level: 1,
-            auxiliary: {
-              url: {
-                value: newOpenedTab.url(),
-                type: "string"
+          const newOpenedTabUrl = newOpenedTab.url();
+          if (newOpenedTabUrl.startsWith("http")) {
+            this.logger({
+              category: "action",
+              message: "new page detected (new tab) with URL",
+              level: 1,
+              auxiliary: {
+                url: {
+                  value: newOpenedTabUrl,
+                  type: "string"
+                }
               }
-            }
-          });
-          yield newOpenedTab.close();
-          yield this.stagehandPage.page.goto(newOpenedTab.url());
-          yield this.stagehandPage.page.waitForLoadState("domcontentloaded");
-          yield this.stagehandPage._waitForSettledDom(domSettleTimeoutMs);
+            });
+            yield newOpenedTab.close();
+            yield this.stagehandPage.page.goto(newOpenedTabUrl);
+            yield this.stagehandPage.page.waitForLoadState("domcontentloaded");
+            yield this.stagehandPage._waitForSettledDom(domSettleTimeoutMs);
+          }
         }
         yield Promise.race([
           this.stagehandPage.page.waitForLoadState("networkidle"),
