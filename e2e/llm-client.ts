import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import type { LanguageModel } from 'ai';
import { AISdkClient } from './external_clients/aisdk.js';
import chalk from 'chalk';

// Define the configuration for each supported model using Zod
const ModelConfigSchema = z.object({
  provider: z.enum(['openai', 'google']),
  modelName: z.string(),
  apiKeyEnv: z.string(),
});

// Define the mapping of supported models to their configurations
const ModelConfigsSchema = z.object({
  'openai:o3-mini': ModelConfigSchema,
  'google:gemini-1.5-flash-latest': ModelConfigSchema,
  // Add more model configurations here
});

// Define the model configurations
const modelConfigs: z.infer<typeof ModelConfigsSchema> = {
  'openai:o3-mini': { provider: 'openai', modelName: 'o3-mini', apiKeyEnv: 'OPENAI_API_KEY' },
  'google:gemini-1.5-flash-latest': { provider: 'google', modelName: 'gemini-1.5-flash-latest', apiKeyEnv: 'GOOGLE_GENERATIVE_AI_API_KEY' },
  // Add more model configurations here
};

/**
 * Retrieves an LLM client based on the LLM_MODEL environment variable.
 * Ensures the required API key is present for the specified model.
 * @returns {AISdkClient} The configured LLM client.
 * @throws {Error} If LLM_MODEL is not set, invalid, or the required API key is missing.
 */
export function getLLMClient(): AISdkClient {
  // Read LLM_MODEL environment variable
  const llmModel = process.env.LLM_MODEL;
  if (!llmModel) {
    const availableModels = Object.keys(modelConfigs)
      .map(model => chalk.green(model))
      .join('\n  - ');
    throw new Error(
      `${chalk.red('LLM_MODEL environment variable is required')}\n` +
      `Available models:\n  - ${availableModels}`
    );
  }

  // Validate that the model exists in our config
  if (!(llmModel in modelConfigs)) {
    const availableModels = Object.keys(modelConfigs)
      .map(model => chalk.green(model))
      .join('\n  - ');
    throw new Error(
      `${chalk.red(`Unsupported model: ${chalk.yellow(llmModel)}`)}\n` +
      `Available models:\n  - ${availableModels}`
    );
  }

  // Get the configuration for the specified model
  const config = modelConfigs[llmModel as keyof typeof modelConfigs];
  ModelConfigSchema.parse(config);

  // Check if the required API key is set
  const apiKey = process.env[config.apiKeyEnv];
  if (!apiKey) {
    throw new Error(`${config.apiKeyEnv} is required for model ${llmModel}`);
  }

  // Create the LanguageModel instance based on the provider
  let languageModel: LanguageModel;
  if (config.provider === 'openai') {
    languageModel = openai(config.modelName);
  } else if (config.provider === 'google') {
    languageModel = google(config.modelName);
  } else {
    throw new Error(`Unsupported provider: ${config.provider}`);
  }

  // Initialize and return AISdkClient
  return new AISdkClient({ model: languageModel });
}
