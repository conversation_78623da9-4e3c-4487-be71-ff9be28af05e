import type { ObserveResult, Page } from "@browserbasehq/stagehand";
import chalk from "chalk";
import fs from "node:fs/promises";
import { clearOverlays, drawObserveOverlay } from "../utils/index.js";
import {
  showThinkingOverlay,
  clearThinkingOverlay,
} from "../utils/thinking-overlay.js";

// Constants
const CACHE_FILE_PATH = "stagehand-cache.json";
const DEFAULT_WAIT_FOR = Number.parseInt(
  process.env.OBSERVE_OVERLAY_DELAY ?? "0"
);

// Helper Functions

/**
 * Persists an action to the cache file.
 * @param instruction - The instruction key for caching
 * @param actionToCache - The action to store
 */
const persistActionToCache = async (
  instruction: string,
  actionToCache: ObserveResult
): Promise<void> => {
  try {
    const existingCacheContent = await fs
      .readFile(CACHE_FILE_PATH, "utf-8")
      .catch(() => "{}");
    const cache: Record<string, ObserveResult> =
      JSON.parse(existingCacheContent);
    cache[instruction] = actionToCache;
    await fs.writeFile(CACHE_FILE_PATH, JSON.stringify(cache, null, 2));
  } catch (error) {
    console.error(chalk.red("Failed to save to cache:"), error);
  }
};

/**
 * Retrieves an action from the cache file.
 * @param instruction - The instruction key to look up
 * @returns The cached action or null if not found
 */
const retrieveActionFromCache = async (
  instruction: string
): Promise<ObserveResult | null> => {
  try {
    const cacheContent = await fs.readFile(CACHE_FILE_PATH, "utf-8");
    const cache: Record<string, ObserveResult> = JSON.parse(cacheContent);
    return cache[instruction] || null;
  } catch {
    return null;
  }
};

/**
 * Observes the page for an action and caches it.
 * @param page - The Playwright page object
 * @param instruction - The instruction to observe
 * @returns The observed action or null if none found
 */
const observeAndCacheAction = async (
  page: Page,
  instruction: string
): Promise<ObserveResult | null> => {
  const observationResults = await page.observe(instruction);
  const actionToCache = observationResults.at(0);
  if (actionToCache) {
    console.log(chalk.blue("Caching action:"), actionToCache);
    await persistActionToCache(instruction, actionToCache);
  }
  return actionToCache ?? null;
};

/**
 * Ensures an element is visible before interaction.
 * @param page - The Playwright page object
 * @param selector - The selector to wait for
 * @param timeoutMs - Optional custom timeout in milliseconds (defaults to 30000)
 */
const ensureElementVisibility = async (
  page: Page,
  selector: string,
  timeoutMs: number = 30000
): Promise<void> => {
  try {
    await page.waitForSelector(selector, {
      state: "visible",
      timeout: timeoutMs
    });
  } catch (error) {
    console.error(`Element visibility timeout for selector: ${selector}`);
    console.log(`Attempting fallback approach for selector: ${selector}`);

    // Try alternative selectors if the original fails
    if (selector.includes('button') && (selector.includes('sign') || selector.includes('Sign'))) {
      // Try alternatives for sign-up buttons
      const alternativeSelectors = [
        '[data-testid="form-button-sign-me-up"]',
        'button:has-text("Sign me up")',
        'button:has-text("SIGN ME UP")',
        'button.sign-me-up',
        'button.cta',
        'button.primary'
      ];

      for (const altSelector of alternativeSelectors) {
        try {
          console.log(`Trying alternative selector: ${altSelector}`);
          await page.waitForSelector(altSelector, {
            state: "visible",
            timeout: 5000
          });
          console.log(`Found element with alternative selector: ${altSelector}`);
          return;
        } catch {
          // Continue to next alternative
        }
      }
    }

    // If we get here, re-throw the original error
    throw error;
  }
};

/**
 * Executes an action with visual feedback.
 * @param page - The Playwright page object
 * @param action - The action to execute
 * @param showOverlay - Whether to display the overlay
 * @param delay - Time to wait after drawing overlay
 */
const executeActionWithFeedback = async (
  page: Page,
  action: ObserveResult,
  showOverlay: boolean,
  delay: number
): Promise<void> => {
  if (showOverlay) {
    await drawObserveOverlay(page, [action]);
  }
  await page.waitForTimeout(delay);
  if (showOverlay) {
    await clearOverlays(page);
  }
  await page.act(action);
};

// Type Definition
/**
 * Options for the actWithCache function.
 */
type ActWithCacheOptions = {
  waitFor?: number; // Delay after action (in ms)
  hideThinkingOverlay?: boolean; // Toggle thinking overlay
  hideObserveOverlay?: boolean; // Toggle observation overlay
  waitForVisibility?: boolean; // Wait for element to be visible
  visibilityTimeout?: number; // Timeout for element visibility check
};

// Main Function
/**
 * Performs an action using a cached result or observes and caches it if not found.
 * Waits for element visibility if specified, then executes the action.
 * @param page - The Playwright page object
 * @param instruction - The instruction to act on
 * @param options - Configuration for waiting, visibility, and overlays
 */
export async function actWithCache(
  page: Page,
  instruction: string,
  options: ActWithCacheOptions = {
    waitFor: DEFAULT_WAIT_FOR,
    hideThinkingOverlay: false,
    hideObserveOverlay: false,
    waitForVisibility: true,
    visibilityTimeout: 30000
  }
): Promise<void> {
  const {
    waitFor = DEFAULT_WAIT_FOR,
    hideThinkingOverlay = false,
    hideObserveOverlay = false,
    waitForVisibility = true,
    visibilityTimeout = 30000
  } = options;

  // Step 1: Check cache first, no overlay needed here
  let actionToExecute = await retrieveActionFromCache(instruction);

  // Step 2: If not in cache, observe the page with thinking overlay
  if (!actionToExecute) {
    if (!hideThinkingOverlay) {
      await showThinkingOverlay(page);
    }
    actionToExecute = await observeAndCacheAction(page, instruction);
    if (!hideThinkingOverlay) {
      await clearThinkingOverlay(page);
    }
  }

  // Step 3: Execute the action if found
  if (actionToExecute) {
    const selector = actionToExecute.selector;
    if (waitForVisibility) {
      await ensureElementVisibility(page, selector, visibilityTimeout);
    }
    await executeActionWithFeedback(
      page,
      actionToExecute,
      !hideObserveOverlay,
      waitFor
    );
  } else {
    // Step 4: Fallback to direct action with thinking overlay
    console.log(
      chalk.yellow(
        `No specific action found for "${instruction}", falling back to direct act.`
      )
    );
    if (!hideThinkingOverlay) {
      await showThinkingOverlay(page);
    }
    await page.act(instruction);
    if (!hideThinkingOverlay) {
      await clearThinkingOverlay(page);
    }
  }
}
