import type { ObserveR<PERSON>ult, Page } from "@browserbasehq/stagehand";

/**
 * Draw an overlay over the relevant xpaths
 * @param page - The page to draw the overlay on
 * @param results - The results from the observe action
 */
export async function drawObserveOverlay(page: Page, results: ObserveResult[]) {
	// Convert single xpath to array for consistent handling
	const xpathList = results.map((result) => result.selector);

	// Filter out empty xpaths
	const validXpaths = xpathList.filter((xpath) => xpath !== "xpath=");

	await page.evaluate((selectors) => {
		for (const selector of selectors) {
			let element: Node | null;
			if (selector.startsWith("xpath=")) {
				const xpath = selector.substring(6);
				element = document.evaluate(
					xpath,
					document,
					null,
					XPathResult.FIRST_ORDERED_NODE_TYPE,
					null,
				).singleNodeValue;
			} else {
				element = document.querySelector(selector);
			}

			if (element instanceof HTMLElement) {
				const overlay = document.createElement("div");
				overlay.setAttribute("stagehandObserve", "true");
				const rect = element.getBoundingClientRect();
				overlay.style.position = "absolute";
				overlay.style.left = `${rect.left}px`;
				overlay.style.top = `${rect.top}px`;
				overlay.style.width = `${rect.width}px`;
				overlay.style.height = `${rect.height}px`;
				overlay.style.backgroundColor = "rgba(255, 255, 0, 0.3)";
				overlay.style.pointerEvents = "none";
				overlay.style.zIndex = "10000";
				document.body.appendChild(overlay);
			}
		}
	}, validXpaths);
}

/**
 * Clear the overlays from the page
 * @param page - The page to clear the overlays from
 */
export async function clearOverlays(page: Page) {
	// remove existing stagehandObserve attributes
	await page.evaluate(() => {
		const elements = document.querySelectorAll('[stagehandObserve="true"]');
		for (const el of elements) {
			const parent = el.parentNode;
			while (el.firstChild) {
				parent?.insertBefore(el.firstChild, el);
			}
			parent?.removeChild(el);
		}
	});
}
