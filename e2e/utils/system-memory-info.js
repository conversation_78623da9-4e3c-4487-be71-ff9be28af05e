/**
 * WARNING: This file must remain as a .js file because it is imported by cucumber.config.mjs
 * which doesn't support TypeScript imports directly.
 */

import os from "node:os";

/**
 * Returns the available system memory in gigabytes
 * @returns {number} Available memory in GB
 */
export const getAvailableMemory = () => {
	const freeMemoryInBytes = os.freemem();
	const freeMemoryInGB = freeMemoryInBytes / 1024 / 1024 / 1024;
	return Math.floor(freeMemoryInGB);
};
