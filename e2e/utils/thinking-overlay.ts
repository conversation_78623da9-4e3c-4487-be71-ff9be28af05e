import type { Page } from "@browserbasehq/stagehand";

export async function showThinkingOverlay(page: Page) {
	await page.evaluate(() => {
		const overlay = document.createElement("div");
		overlay.setAttribute("stagehandThinking", "true");

		// Add the animation styles
		const styleSheet = document.createElement("style");
		styleSheet.textContent = `
			@keyframes ellipsis {
				0% { content: '.'; }
				33% { content: '..'; }
				66% { content: '...'; }
			}
			
			.thinking-text::after {
				content: '.';
				display: inline-block;
				width: 12px;
				animation: ellipsis 1.5s infinite;
			}
		`;
		document.head.appendChild(styleSheet);

		// Sparkles icon from Lucide
		const sparklesSvg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles">
        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
        <path d="M5 3v4"/>
        <path d="M19 17v4"/>
        <path d="M3 5h4"/>
        <path d="M17 19h4"/>
      </svg>
    `;

		overlay.innerHTML = `
      <div style="
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: system-ui;
        z-index: 10001;
      ">
        ${sparklesSvg}
        <span class="thinking-text">Thinking</span>
      </div>
    `;

		document.body.appendChild(overlay);
	});
}

export async function clearThinkingOverlay(page: Page) {
	await page.evaluate(() => {
		const elements = document.querySelectorAll('[stagehandThinking="true"]');
		const styles = document.querySelectorAll("style");
		for (const el of elements) {
			el.remove();
		}
		// Remove our added styles
		for (const style of styles) {
			if (style.textContent?.includes("@keyframes ellipsis")) {
				style.remove();
			}
		}
	});
}
