import type { ConstructorParams, LogLine } from "@browserbasehq/stagehand";
import dotenv from "dotenv";
import { getLLMClient } from "./llm-client.js";
import { logLineToString } from "./utils/logging.js";
dotenv.config();

const StagehandConfig: ConstructorParams = {
  env: "LOCAL",
  apiKey: process.env.BROWSERBASE_API_KEY,
  projectId: process.env.BROWSERBASE_PROJECT_ID,
  debugDom: undefined,
  headless: process.env.STAGEHAND_HEADLESS === "true",
  logger: (message: LogLine) => console.log(logLineToString(message)),
  domSettleTimeoutMs: process.env.DOM_SETTLE_TIMEOUT_MS
    ? Number.parseInt(process.env.DOM_SETTLE_TIMEOUT_MS)
    : 60_000,
  browserbaseSessionCreateParams: {
    projectId: process.env.BROWSERBASE_PROJECT_ID ?? "",
  },
  enableCaching: undefined,
  browserbaseSessionID: undefined,
  llmClient: getLLMClient(),
  localBrowserLaunchOptions: {
    viewport: {
      width: 1920,
      height: 1080,
    },
  },
};

export default StagehandConfig;
