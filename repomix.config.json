{"output": {"filePath": "repomix-output.md", "style": "markdown", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": true, "compress": true, "topFilesLength": 30, "showLineNumbers": false, "copyToClipboard": false}, "include": ["python/"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}