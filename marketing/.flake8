[flake8]
ignore = E203, E266, E501, W503, F403, F401, E126, F821, W504, W605
exclude =
    .git
    venv
    .venv
    __pycache__
    notebooks
# Recommend matching the black line length (default 88),
# rather than using the flake8 default of 79:
max-line-length = 88
max-complexity = 18
select = B,C,E,F,W,T4,B9
extend-ignore =
    # See https://github.com/PyCQA/pycodestyle/issues/373
    E203,E501
show_source = true
count = true
