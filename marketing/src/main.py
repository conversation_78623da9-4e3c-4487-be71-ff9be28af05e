import base64
import json
from urllib.parse import quote

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import streamlit as st
from email_service.sendgrid_client import SendGridClient
from st_aggrid import AgGrid


@st.cache_data
def get_bounces():
    sg_client = SendGridClient()
    bounces = sg_client.get_bounces()
    return pd.DataFrame(bounces)


@st.cache_data
def get_blocks():
    sg_client = SendGridClient()
    blocks = sg_client.get_blocks()
    return pd.DataFrame(blocks)


@st.cache_data
def get_spam_reports():
    sg_client = SendGridClient()
    spam_reports = sg_client.get_spam_reports()
    return pd.DataFrame(spam_reports)


@st.cache_data
def get_unsubscribers():
    sg_client = SendGridClient()
    unsubscribers = sg_client.get_unsubscribers()
    return pd.DataFrame(unsubscribers)


@st.cache_data
def get_contact_lists():
    sg_client = SendGridClient()
    contact_lists = sg_client.get_contact_lists()
    df = pd.json_normalize(contact_lists["result"])
    df.columns = df.columns.map(lambda x: " ".join(x.split(".")))

    return df.applymap(lambda x: np.ravel(x) if isinstance(x, dict) else x)


@st.cache_data
def get_sender_identities():
    sg_client = SendGridClient()
    sender_identities = sg_client.get_sender_identities()

    df = pd.json_normalize(sender_identities)
    df.columns = df.columns.map(lambda x: " ".join(x.split(".")))

    return df.applymap(lambda x: np.ravel(x) if isinstance(x, dict) else x)


def format_datetime(df: pd.DataFrame) -> pd.DataFrame:
    if "created" in df.columns:
        df["created"] = pd.to_datetime(df["created"], unit="s")
    return df


def process_utility_column(value):
    # Convert to string and lowercase for case-insensitive comparison
    value_str = str(value).strip().lower()

    if value_str in ["true", "1"]:
        return True
    else:
        return False


def get_utilities_ids(row):
    utilities_ids = []

    has_electricity = process_utility_column(row.get("hasElectricity"))
    if has_electricity:
        utilities_ids.append(1)

    has_gas = process_utility_column(row.get("hasGas"))
    if has_gas:
        utilities_ids.append(2)

    return utilities_ids


def generate_json_base64encoded(row: pd.Series) -> str:
    # Check if company.type is provided, else set to 1 if company.registrationNumber is present

    company_type = (
        str(int(row.get("company.type")))
        if pd.notnull(row.get("company.type"))
        else ("1" if pd.notnull(row.get("company.registrationNumber")) else "")
    )

    data = {
        "company": {
            "type": company_type,
            "name": row.get("business_name", "")
            if pd.notnull(row.get("business_name"))
            else "",
            "registrationNumber": row.get("company.registrationNumber", "")
            if pd.notnull(row.get("company.registrationNumber"))
            else "",
            "charityNumber": row.get("company.charityNumber", "")
            if pd.notnull(row.get("company.charityNumber"))
            else "",
            "sitePostcode": row.get("company.sitePostcode", "")
            if pd.notnull(row.get("company.sitePostcode"))
            else "",
        },
        "contact": {
            "businessPhoneNumber": row.get("contact.businessPhoneNumber", "")
            if pd.notnull(row.get("contact.businessPhoneNumber"))
            else "",
            "contactForename": row.get("contact.contactForename", "")
            if pd.notnull(row.get("contact.contactForename"))
            else "",
            "contactSurname": row.get("contact.contactSurname", "")
            if pd.notnull(row.get("contact.contactSurname"))
            else "",
            "position": row.get("contact.position", "")
            if pd.notnull(row.get("contact.position"))
            else "",
        },
        "utilities": get_utilities_ids(row),
    }
    return base64.b64encode(json.dumps(data).encode()).decode()


def main():
    try:
        st.set_page_config(layout="wide")

        # Instantiate the SendGrid client
        sg_client = SendGridClient()

        tabs = st.sidebar.selectbox(
            "Choose a Tab",
            [
                "New Campaign",
                "Campaigns",
                "Filtered Emails",
                "Bounced",
                "Blocked",
                "Spam",
                "Unsubscribed",
                "Sender Management",
                "Contact Lists",
            ],
        )

        df_bounces = format_datetime(get_bounces())
        df_blocks = format_datetime(get_blocks())
        df_spam_reports = format_datetime(get_spam_reports())
        df_unsubscribers = format_datetime(get_unsubscribers())
        # df_sender_identities = format_datetime(get_sender_identities())
        df_contact_lists = format_datetime(get_contact_lists())

        # Combine the dataframes
        df_combined = pd.concat(
            [df_blocks, df_spam_reports, df_unsubscribers, df_bounces]
        )

        # df_filtered_emails will show only created and email
        df_filtered_emails = df_combined[["created", "email"]]

        # Filter specific domains here
        df_filtered_emails = df_filtered_emails[
            ~df_filtered_emails["email"].str.endswith("@watt.co.uk")
        ]
        df_remove_duplicates = df_filtered_emails.drop_duplicates()

        clicked_emails = []
        if tabs == "New Campaign":
            new_campaign_id = st.text_input("Enter New Campaign ID")

            # TODO (Stephen) Make it get this from the API endpoint instead
            clicked_file = st.file_uploader("Choose a 'clicked' CSV file", type="csv")
            if clicked_file is not None:
                clicked_data = pd.read_csv(clicked_file)
                # get a list of just target_email from clicked_data
                # TODO (Stephen): If they clicked but didn't do anything surely we want to email them again? This won't
                clicked_emails = clicked_data["target_email"].tolist()

            contact_list_uploaded_file = st.file_uploader(
                "Choose a 'contact list' CSV file", type="csv"
            )
            if contact_list_uploaded_file is not None:
                data = pd.read_csv(contact_list_uploaded_file)
                data["target_id"] = data["email"].apply(
                    lambda x: base64.b64encode(x.encode()).decode()
                    if isinstance(x, str)
                    else None
                )
                data["campaign_id"] = quote(new_campaign_id)
                data["data"] = data.apply(generate_json_base64encoded, axis=1)

                # Filter out emails present in df_remove_duplicates
                original_len = len(data)
                data_removed = data[data["email"].isin(df_remove_duplicates["email"])]
                data = data[~data["email"].isin(df_remove_duplicates["email"])]
                num_removed = original_len - len(data)

                original_len_before_clicked_filter = len(data)
                data_clicked_removed = data[data["email"].isin(clicked_emails)]
                data = data[~data["email"].isin(clicked_emails)]
                # also remove watt.co.uk emails
                data = data[~data["email"].str.endswith("@watt.co.uk")]
                num_removed_clicked = original_len_before_clicked_filter - len(data)

                st.write(
                    "Number of emails removed due to being on the filter list: ",
                    num_removed,
                )
                st.dataframe(data_removed)

                st.write("Number of emails removed has clicked: ", num_removed_clicked)
                st.dataframe(data_clicked_removed)

            if new_campaign_id and contact_list_uploaded_file:
                st.write("New Campaign: ", new_campaign_id)
                # map to rename current_supplier
                current_supplier_map = {
                    "British Gas Trading Ltd": "British Gas",
                    "Edf Energy Customers Limited": "Edf Energy",
                    "E.On Energy Ltd": "E.On Energy",
                }
                # Specify the columns to drop (in lower case)
                columns_to_drop = [
                    "company.type",
                    "company.registrationNumber",
                    "company.charityNumber",
                    "company.sitePostcode",
                    "contact.businessPhoneNumber",
                    "contact.contactForename",
                    "contact.contactSurname",
                ]

                # Drop the columns
                data = data.drop(columns=columns_to_drop)

                data["current_supplier"] = data["current_supplier"].map(
                    current_supplier_map
                )
                st.dataframe(data)

                # Download button for filtered DataFrame
                csv = data.to_csv(index=False)
                st.download_button(
                    "Download CSV File",
                    data=csv,
                    file_name="filtered_data.csv",
                    mime="text/csv",
                )

        if tabs == "Campaigns":
            campaigns = sg_client.get_campaigns()
            df_campaigns = pd.DataFrame(campaigns["result"])
            st.write("Total Campaigns:", len(df_campaigns))
            AgGrid(df_campaigns)

        elif tabs == "Filtered Emails":

            def create_waterfall_chart(
                df_bounces,
                df_blocks,
                df_spam_reports,
                df_unsubscribers,
                df_filtered_emails,
                df_remove_duplicates,
            ):
                fig = go.Figure(
                    go.Waterfall(
                        name="20",
                        orientation="v",
                        measure=[
                            "relative",
                            "relative",
                            "relative",
                            "relative",
                            "total",
                            "relative",
                            "relative",
                        ],
                        x=[
                            "Bounced Emails",
                            "Blocked Emails",
                            "Spam Reports",
                            "Unsubscribed Contacts",
                            "Combined Emails",
                            "Minus '@watt.co.uk' Emails",
                            "Deduped minus '@watt.co.uk' Emails",
                        ],
                        textposition="outside",
                        text=[
                            len(df_bounces),
                            len(df_blocks),
                            len(df_spam_reports),
                            len(df_unsubscribers),
                            "",
                            -len(df_filtered_emails),
                            len(df_remove_duplicates) - len(df_filtered_emails),
                        ],
                        y=[
                            len(df_bounces),
                            len(df_blocks),
                            len(df_spam_reports),
                            len(df_unsubscribers),
                            0,
                            -len(df_filtered_emails),
                            len(df_remove_duplicates) - len(df_filtered_emails),
                        ],
                        connector={"line": {"color": "rgb(63, 63, 63)"}},
                    )
                )

                fig.update_layout(title="Email categories count", showlegend=False)

                st.plotly_chart(fig)

            # Inside your main function
            create_waterfall_chart(
                df_bounces,
                df_blocks,
                df_spam_reports,
                df_unsubscribers,
                df_filtered_emails,
                df_remove_duplicates,
            )

            # Retrieve a list of filtered emails
            st.write("Total Bounced Emails:", len(df_bounces))
            st.write("Total Blocked Emails:", len(df_blocks))
            st.write("Total Spam Reports:", len(df_spam_reports))
            st.write("Total Unsubscribed Contacts:", len(df_unsubscribers))
            # Total of all above
            st.write("Total Combined Emails:", len(df_combined))
            st.write("Total minus '@watt.co.uk' Emails:", len(df_filtered_emails))
            st.write(
                "Total minus '@watt.co.uk' deduped Emails:", len(df_remove_duplicates)
            )
            AgGrid(df_filtered_emails)

        elif tabs == "Bounced":
            st.write("Total Bounced Emails:", len(df_bounces))
            AgGrid(df_bounces)

        elif tabs == "Blocked":
            st.write("Total Blocked Emails:", len(df_blocks))
            AgGrid(df_blocks)

        elif tabs == "Spam":
            st.write("Total Spam Reports:", len(df_spam_reports))
            AgGrid(df_spam_reports)

        elif tabs == "Unsubscribed":
            st.write("Total Unsubscribed Contacts:", len(df_unsubscribers))
            AgGrid(df_unsubscribers)

        # elif tabs == "Sender Management":
        #     update = st.button("Upload Senders")

        #     if update:
        #         sg_client = SendGridClient()
        #         response = sg_client.create_sender_identity()
        #         st.text("Senders updated")

        #         st.text(response)

        #     AgGrid(df_sender_identities)

        elif tabs == "Contact Lists":
            st.write("Total  Contact lists:", len(df_unsubscribers))
            AgGrid(df_contact_lists)

            # selected = st.session_state.get("selected", None)
            # if selected:
            #     # Get the details for the selected contact list
            #     list_id = selected["data"]["id"]
            #     client = SendGridClient()
            #     contact_list = client.get_contact_list(list_id)

            #     # Display the contact list details
            #     st.write(contact_list)

    except Exception as error:
        st.write(error)


if __name__ == "__main__":
    main()
