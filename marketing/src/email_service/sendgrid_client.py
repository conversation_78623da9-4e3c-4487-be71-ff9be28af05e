import json
import os

from dotenv import load_dotenv
from sendgrid import SendGridAPIClient

load_dotenv()


class SendGridClient:
    """For interacting with the SendGrid API.
    https://docs.sendgrid.com/api-reference/marketing-campaign-stats/get-all-single-sends-stats
    """

    def __init__(self):
        """Initialize the SendGrid client."""
        self.sendgrid = SendGridAPIClient(api_key=os.environ.get("SENDGRID_API_KEY"))

    def _get_json_response(self, response):
        """Decode the response and convert it into JSON."""
        return json.loads(response.body)

    def get_bounces(self) -> list[dict[str, str]]:
        """
        Retrieve a list of bounced emails.
        """
        response = self.sendgrid.client.suppression.bounces.get()
        return self._get_json_response(response)

    def get_blocks(self) -> list[dict[str, str]]:
        """
        Retrieve a list of blocked emails.
        """
        response = self.sendgrid.client.suppression.blocks.get()
        return self._get_json_response(response)

    def get_spam_reports(self) -> list[dict[str, str]]:
        """
        Retrieve a list of spam reports.
        """
        response = self.sendgrid.client.suppression.spam_reports.get()
        return self._get_json_response(response)

    def get_invalid_emails(self) -> list[dict[str, str]]:
        """
        Retrieve a list of invalid emails.
        """
        response = self.sendgrid.client.suppression.invalid_emails.get()
        return self._get_json_response(response)

    def get_unsubscribers(self) -> list[dict[str, str]]:
        """
        Retrieve a list of unsubscribe groups.
        """
        response = self.sendgrid.client.suppression.unsubscribes.get()
        return self._get_json_response(response)

    def get_contacts(self) -> list[dict[str, str]]:
        """
        Retrieve a list of contacts.
        """
        response = self.sendgrid.client.contacts.get()
        return self._get_json_response(response)

    def send_email(self, data: dict[str, str]) -> dict[str, str]:
        """
        Send an email.
        """
        response = self.sendgrid.client.mail.send.post(request_body=data)
        return self._get_json_response(response)

    def get_templates(self) -> list[dict[str, str]]:
        """
        Retrieve a list of templates.
        """
        response = self.sendgrid.client.templates.get()
        return self._get_json_response(response)

    def get_account_info(self) -> dict[str, str]:
        """
        Retrieve account information.
        """
        response = self.sendgrid.client.user.account.get()
        return self._get_json_response(response)

    def get_email_stats(self) -> list[dict[str, str]]:
        """
        Retrieve email statistics.
        """
        response = self.sendgrid.client.stats.get()
        return self._get_json_response(response)

    def get_tracking_settings(self) -> dict[str, str]:
        """
        Retrieve tracking settings.
        """
        response = self.sendgrid.client.tracking_settings.get()
        return self._get_json_response(response)

    def get_campaigns(self) -> list[dict[str, str]]:
        """
        Retrieve a list of campaigns.
        """
        params = {"page_size": 100}

        response = self.sendgrid.client.marketing.lists.get(query_params=params)
        return self._get_json_response(response)

    def get_campaign_entries(self, campaign_id) -> list[dict[str, str]]:
        """
        Retrieve entries for a specific campaign.
        """
        response = self.sendgrid.client.marketing.lists._(campaign_id).get()
        return self._get_json_response(response)

    def get_categories(self) -> list[dict[str, str]]:
        """
        Retrieve a list of categories.
        """
        response = self.sendgrid.client.categories.get()
        return self._get_json_response(response)

    def get_unsubscribe_groups(self) -> list[dict[str, str]]:
        """
        Retrieve a list of unsubscribe groups.
        """
        response = self.sendgrid.client.asm.groups.get()
        return self._get_json_response(response)

    def get_segments(self) -> list[dict[str, str]]:
        """
        Retrieve a list of segments.
        """
        response = self.sendgrid.client.segments.get()
        return self._get_json_response(response)

    def get_lists(self) -> list[dict[str, str]]:
        """
        Retrieve a list of lists.
        """
        response = self.sendgrid.client.lists.get()
        return self._get_json_response(response)

    def create_sender_identity(self) -> list[dict[str, str]]:
        """
        Create a sender identity.
        """
        senders = [
            {
                "nickname": "Corona Energy",
                "email": "<EMAIL>",
                "name": "Corona Energy",
            },
            {
                "nickname": "Haven Power",
                "email": "<EMAIL>",
                "name": "Haven Power",
            },
            {
                "nickname": "Octopus Energy",
                "email": "<EMAIL>",
                "name": "Octopus Energy",
            },
            {
                "nickname": "Opus Energy",
                "email": "<EMAIL>",
                "name": "Opus Energy",
            },
            {
                "nickname": "Positive Energy",
                "email": "<EMAIL>",
                "name": "Positive Energy",
            },
            {
                "nickname": "Scottish Power",
                "email": "<EMAIL>",
                "name": "Scottish Power",
            },
            {
                "nickname": "Smartest Energy",
                "email": "<EMAIL>",
                "name": "Smartest Energy",
            },
            {"nickname": "SSE", "email": "<EMAIL>", "name": "SSE"},
            {
                "nickname": "Total Gas & Power",
                "email": "<EMAIL>",
                "name": "Total Gas & Power",
            },
            {
                "nickname": "Valda Energy",
                "email": "<EMAIL>",
                "name": "Valda Energy",
            },
        ]

        responses = []
        for sender in senders:
            data = self.create_sender_payload(
                nickname=sender["nickname"], email=sender["email"], name=sender["name"]
            )
            response = self.sendgrid.client.senders.post(request_body=data)
            responses.append(self._get_json_response(response))

        return responses

    def get_sender_identities(self) -> list[dict[str, str]]:
        """
        Retrieve a list of sender identities.
        """
        response = self.sendgrid.client.senders.get()
        return self._get_json_response(response)

    def create_sender_payload(
        self, nickname: str, email: str, name: str
    ) -> dict[str, str]:
        return {
            "nickname": nickname,
            "from": {"email": email, "name": name},
            "reply_to": {"email": email, "name": name},
            "address": "St Ann's House",
            "address_2": "5th Floor, St Ann's Square",
            "city": "Manchester",
            "state": "",
            "zip": "M2 7LP",
            "country": "United Kingdom",
        }

    def get_contact_lists(self):
        """Retrieve a list of all contact lists."""
        response = self.sendgrid.client.marketing.contacts.get()
        return self._get_json_response(response)

    def get_contact_list(self, list_id):
        """Retrieve a specific contact list."""
        response = self.sendgrid.client.marketing.contacts.get(list_id)
        return self._get_json_response(response)
