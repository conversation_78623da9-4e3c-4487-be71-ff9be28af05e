[tool.poetry]
name = "marketing"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
sendgrid = "^6.10.0"
python-dotenv = "^1.0.0"
streamlit = "^1.23.1"
streamlit-aggrid = "^0.3.4.post3"
matplotlib = "^3.7.1"
plotly = "^5.14.1"
ipykernel = "^6.23.1"
numpy = "^1.24.3"

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"
playwright = "^1.28.0"
setuptools = "^67.6.1"
pre-commit = "^3.3.2"
pylint-pydantic = "^0.1.8"
pyright = "^1.1.305"
ipykernel = "^6.23.1"

[tool.poetry.group.lint.dependencies]
ruff = "^0.0.249"
types-toml = "^********"
types-redis = "^********"
black = "^23.1.0"

[tool.poetry.group.typing.dependencies]
mypy = "^0.991"
types-pyyaml = "^********"
types-requests = "^*********"

[tool.poetry.group.dev]
optional = true

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.ruff]
select = [
  "E",   # pycodestyle
  "W",   # pycodestyle
  "F",   # Pyflakes
  "PL",  # pylint
  "I",   # isort
  "B",   # flake8-bugbear
  "A",   # flake8-builtins
  "S",   # flake8-bandit
  "ISC", # flake8-implicit-str-concat
  "ICN", # flake8-import-conventions
  "PIE", # flake8-pie
  "Q",   # flake8-quotes
  "RET", # flake8-return
  "SIM", # flake8-simplify
  "TID", # flake8-tidy-imports
  "RUF", # Ruff-specific rules
  "YTT", # flake8-2020
  "UP",  # pyupgrade
  "C4",  # flake8-comprehensions
  "PTH", # flake8-use-pathlib
  "G",   # flake8-logging-format
  "INP", # flake8-no-pep420
  "T20", # flake8-print
]
ignore = [
  # (missing public docstrings) These work off of the Python sense of "public", rather than our
  # bespoke definition based off of `@public`. When ruff supports custom plugins then we can write
  # appropriate rules to require docstrings for `@public`.
  "D100",
  "D101", # Missing docstring in public class
  "D102",
  "D103",
  "D104",
  "D105",
  "D106",
  "D107",

  # (docstring imperative mood) Overly restrictive.
  "D401",

  # (module level import not at top) There are several places where we use e.g.
  # warnings.filterwarings calls before imports.
  "E402",

  # (line too long): This fires for comments, which black won't wrap.
  # Disabling until there is an autoformat solution available for comments.
  "E501",

  # (no type comparison): There are a few places where we use `== type(None)` which are more clear
  # than the equivalent `isinstance` check.
  'E721',

  # (bare exception): There are many places where we want to catch a maximally generic exception.
  'E722',

  # (no assign lambda): existing code assigns lambdas in a few places. With black formatting
  # requiring extra empty lines between defs, disallowing lambda assignment can make code less
  # readable.
  "E731",

  # (no concatenation) Existing codebase has many concatentations, no reason to disallow them.
  "RUF005",

  ##### TEMPORARY DISABLES

  # (assorted docstring rules) There are too many violations of these to enable
  # right now, but we should enable after fixing the violations.
  "D200", # (one-line docstring should fit)
  "D205", # (blank line after summary)
  "D417", # (missing arg in docstring)

  "PTH123",  # `open("foo")` should be replaced by `Path("foo").open()`
  "PTH120",  # os.path.dirname` should be replaced by `.parent`
  "PTH118",  # `os.path.join` should be replaced by foo_path / "bar"
  "PTH107",  # `os.remove` should be replaced by `.unlink()`
  "T201",    # `print` found
  "S101",    # Use of assert detected
  "G004",    # Logging statement uses string formatting
  "A003",    # Class attribute is shadowing a python builtin
  "PLR2004", # Magic value used in comparison
  "PTH110",  #`os.path.exists` should be replaced by `.exists()`
  "PTH103",  #`os.makedirs` should be replaced by `.mkdir(parents=True)`
  "PTH111",  # `os.path.expanduser` should be replaced by `.expanduser()`
  "PLR0913", # Too many arguments to function call (6/5)
]

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "I",
  "N",
  "Q",
  "S",
  "T",
  "W",
  "ANN",
  "ARG",
  "BLE",
  "COM",
  "DJ",
  "DTZ",
  "EM",
  "ERA",
  "EXE",
  "FBT",
  "ICN",
  "INP",
  "ISC",
  "NPY",
  "PD",
  "PGH",
  "PIE",
  "PL",
  "PT",
  "PTH",
  "PYI",
  "RET",
  "RSE",
  "RUF",
  "SIM",
  "SLF",
  "TCH",
  "TID",
  "TRY",
  "UP",
  "YTT",
]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
  ".bzr",
  ".direnv",
  ".eggs",
  ".git",
  ".hg",
  ".mypy_cache",
  ".nox",
  ".pants.d",
  ".pytype",
  ".ruff_cache",
  ".svn",
  ".tox",
  ".venv",
  "__pypackages__",
  "_build",
  "buck-out",
  "build",
  "dist",
  "node_modules",
  "venv",
  "templates",
]

extend-select = [
  "I",      # Missing required import (auto-fixable)
  "UP",     # Pyupgrade
  "RUF100", # Unused noqa (auto-fixable)

  # We ignore more pydocstyle than we enable, so be more selective at what we enable
  "D101",
  "D106",
  "D2",
  "D3",
  # "D401", # Not enabled by ruff, but we don't want it
  "D402",
  "D403",
  "D412",
  "D419",
]
extend-ignore = ["D203", "D205", "D212", "D213", "D214", "D215", "E731"]

# Same as Black.
line-length = 88
target-version = "py39"

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

# https://github.com/charliermarsh/ruff/issues/2382
[pyupgrade]
keep-runtime-typing = true

[tool.ruff.per-file-ignores]
# python scripts in bin/ needs some python path configurations before import
"bin/*.py" = [
  "E402",
  # S603: `subprocess` call: check for execution of untrusted input
  # these are dev tools and do not have risks of malicious inputs.
  # T201 `print` found
  # print() is allowed in bin/ as they are dev tools.
  "T201",
]

# Ignore pydoc style from these
"*.pyi" = ["D"]
"tests/*" = ["D"]
"scripts/*" = ["D"]
"dev/*" = ["D"]
"docs/*" = ["D"]
"provider_packages/*" = ["D"]
"docker_tests/*" = ["D"]
"kubernetes_tests/*" = ["D"]
"*/example_dags/*" = ["D"]
"chart/*" = ["D"]


[pylint]
max-args = 6                                                              # We have many functions reaching 6 args
load-plugins = "pylint_pydantic"
max-line-length = 88
disable = ["W1203", "R0903", "W0105", "W0621", "C0114", "C0301", "C0116"]


[tool.black]
line-length = 88
target_version = ['py310']
exclude = '''
(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | pip-wheel-metadata
    | examples
    | templates
  )/
)
'''

[tool.ruff.isort]
force-wrap-aliases = true
force-single-line = false
combine-as-imports = true
split-on-trailing-comma = true
order-by-type = true
force-sort-within-sections = true

[tool.autoflake]
recursive = true
remove-all-unused-imports = true
remove-unused-variables = true
ignore-init-module-imports = true
remove-duplicate-keys = true
expand-star-imports = true

[tool.mypy]
ignore_missing_imports = "True"
disallow_untyped_defs = "True"
exclude = ["notebooks"]

[tool.pytest.ini_options]
# * Disable `flaky` plugin for pytest. This plugin conflicts with `rerunfailures` because provide same marker.
# * Disable `nose` builtin plugin for pytest. This feature deprecated in 7.2 and will be removed in pytest>=8
# * And we focus on use native pytest capabilities rather than adopt another frameworks.
addopts = "-rasl --verbosity=2 -p no:flaky -p no:nose --asyncio-mode=strict"
norecursedirs = [
  ".eggs",
  "airflow",
  "tests/dags_with_system_exit",
  "tests/test_utils",
  "tests/dags_corrupted",
  "tests/dags",
  "tests/system/providers/google/cloud/dataproc/resources",
  "tests/system/providers/google/cloud/gcs/resources",
]
log_level = "INFO"
filterwarnings = [
  "error::pytest.PytestCollectionWarning",
  "ignore::DeprecationWarning:flask_appbuilder.filemanager",
  "ignore::DeprecationWarning:flask_appbuilder.widgets",
  # https://github.com/dpgaspar/Flask-AppBuilder/pull/1940
  "ignore::DeprecationWarning:flask_sqlalchemy",
  # https://github.com/dpgaspar/Flask-AppBuilder/pull/1903
  "ignore::DeprecationWarning:apispec.utils",
]
python_files = ["*.py"]
testpaths = ["tests"]

[tool.coverage.run]
source = ["src"]
branch = true
skip_empty = true
omit = ["tests/*", "src/__init__.py", "src/main.py"]
exclude_lines = [
  "pragma: no cover",
  "if __name__ == .__main__.:",
  "pass",
  "raise NotImplementedError",
  "return NotImplemented",
  "@abstractmethod",
  "@abstractproperty",
]

[tool.coverage.report]
exclude_lines = [
  "pragma: no cover",
  "if __name__ == .__main__.:",
  "pass",
  "raise NotImplementedError",
  "return NotImplemented",
]
precision = 2

[tool.coverage.html]
directory = "coverage_html_report"

[tool.coverage.xml]
output = "coverage.xml"

[tool.poetry.plugins."flake8.extension"]
"INI" = "flake8_init_creator.plugin:plugin"

[tool.poetry.plugins."flake8.options"]
"INI" = "flake8_init_creator.plugin:parse_options"
