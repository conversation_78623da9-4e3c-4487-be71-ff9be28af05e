# EditorConfig: https://EditorConfig.org

root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
indent_style = space
trim_trailing_whitespace = true

[*.py]
charset = utf-8
end_of_line = lf
indent_size = 4
indent_style = space
max_line_length = 120
trim_trailing_whitespace = true

[*.{js,ts,json,yml,yaml}]
charset = utf-8
end_of_line = lf
indent_size = 2
indent_style = space
max_line_length = 80
trim_trailing_whitespace = true

[*.md]
charset = utf-8
end_of_line = lf
max_line_length = off
trim_trailing_whitespace = true

[Makefile]
charset = utf-8
end_of_line = lf
insert_final_newline = true
indent_style = tab
tab_width = 4
trim_trailing_whitespace = true
