# https://mypy.readthedocs.io/en/stable/config_file.html#config-file-format

[mypy]
exclude = templates/*
python_version = 3.10
plugins = pydantic.mypy
show_error_codes = true
follow_imports = silent
ignore_missing_imports = true
local_partial_types = true
strict_equality = true
no_implicit_optional = true
warn_incomplete_stub = true
warn_redundant_casts = true
warn_unused_configs = true
warn_unused_ignores = true
disable_error_code = annotation-unchecked
strict_concatenate = false
check_untyped_defs = true
disallow_incomplete_defs = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
warn_return_any = true
warn_unreachable = true

[mypy-templates.*]
ignore_errors = True

[mypy-jsonschema,jsonschema.*]
ignore_missing_imports=True

[mypy-ruamel.*]
ignore_missing_imports = True

[mypy-docker.*]
ignore_missing_imports = True

[mypy-pkg_resources.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true
warn_untyped_fields = true

[mypy-yachalk.*]
ignore_missing_imports = True

[mypy-stevedore.*]
ignore_missing_imports = True

[mypy-papermill.*]
ignore_missing_imports = True

[mypy-click.*]
ignore_missing_imports = True

[mypy-click_plugins.*]
ignore_missing_imports = True
