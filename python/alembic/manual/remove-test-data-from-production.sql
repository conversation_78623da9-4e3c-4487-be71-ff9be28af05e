BEGIN;

-- Create a temporary table with the list of specific test email addresses
CREATE TEMP TABLE temp_email_list AS
VALUES
    ('<EMAIL>'),
    ('<EMAIL>'),
    ('yin<PERSON><PERSON><PERSON>@gmail.com'),
    ('yin<PERSON><PERSON><PERSON>@googlemail.com'),
    ('<EMAIL>'),
    ('<EMAIL>');

-- Create a temporary table to collect all email addresses to be processed
CREATE TEMP TABLE temp_email_addresses AS
SELECT email
FROM public.company_contact
WHERE email LIKE '%@watt.co.uk' OR email IN (SELECT * FROM temp_email_list)
UNION
SELECT target_email AS email
FROM public.company_aquisition_info
WHERE target_email LIKE '%@watt.co.uk' OR target_email IN (SELECT * FROM temp_email_list);

-- Create a temporary table to hold company IDs associated with these emails
CREATE TEMP TABLE temp_company_ids AS
SELECT DISTINCT cc.company_id
FROM public.company_contact cc
WHERE cc.email IN (SELECT email FROM temp_email_addresses)
UNION
SELECT c.id
FROM public.company c
JOIN public.company_aquisition_info cai ON c.aquisition_info_id = cai.id
WHERE cai.target_email IN (SELECT email FROM temp_email_addresses);

-- Log the email addresses and company IDs to be processed
SELECT 'Emails to be processed:' AS message;
SELECT email FROM temp_email_addresses;
SELECT 'Companies to be deleted:' AS message;
SELECT company_id FROM temp_company_ids;

-- Delete from dependent tables with logging
SELECT 'Deleting from soletrader_personal_address' AS message;
DELETE FROM public.soletrader_personal_address
WHERE soletrader_personal_details_id IN (
    SELECT id FROM public.soletrader_personal_details
    WHERE company_id IN (SELECT company_id FROM temp_company_ids)
);

SELECT 'Deleting from soletrader_personal_details' AS message;
DELETE FROM public.soletrader_personal_details
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_contact' AS message;
DELETE FROM public.company_contact
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_agreement_set' AS message;
DELETE FROM public.company_agreement_set
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_banking_details' AS message;
DELETE FROM public.company_banking_details
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_email_auth_token' AS message;
DELETE FROM public.company_email_auth_token
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_industry' AS message;
DELETE FROM public.company_industry
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_site' AS message;
DELETE FROM public.company_site
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from credit_score' AS message;
DELETE FROM public.credit_score
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from electricity_usage' AS message;
DELETE FROM public.electricity_usage
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from gas_usage' AS message;
DELETE FROM public.gas_usage
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from quote' AS message;
DELETE FROM public.quote
WHERE quote_list_id IN (
    SELECT id FROM public.quote_list
    WHERE company_id IN (SELECT company_id FROM temp_company_ids)
);

SELECT 'Deleting from quote_list' AS message;
DELETE FROM public.quote_list
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from contract' AS message;
DELETE FROM public.contract
WHERE company_id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company' AS message;
DELETE FROM public.company
WHERE id IN (SELECT company_id FROM temp_company_ids);

SELECT 'Deleting from company_aquisition_info' AS message;
DELETE FROM public.company_aquisition_info
WHERE target_email IN (SELECT email FROM temp_email_addresses);

SELECT 'Deleting from company_email_verification' AS message;
DELETE FROM public.company_email_verification
WHERE email IN (SELECT email FROM temp_email_addresses);

-- Use ROLLBACK for a dry run; change to COMMIT to apply changes
-- ROLLBACK;
COMMIT;
