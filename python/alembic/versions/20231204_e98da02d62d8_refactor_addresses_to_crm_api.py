"""refactor-addresses-to-crm-api

Revision ID: e98da02d62d8
Revises: f4d8a381d2c9
Create Date: 2023-12-04 15:39:50.767926

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e98da02d62d8"
down_revision = "f4d8a381d2c9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("entity_address", sa.Column("country", sa.String(), nullable=True))
    op.add_column("entity_address", sa.Column("address", sa.String(), nullable=False))
    op.add_column(
        "entity_address", sa.Column("address_line_2", sa.String(), nullable=True)
    )
    op.add_column("entity_address", sa.Column("house_name", sa.String(), nullable=True))
    op.add_column(
        "entity_address", sa.Column("house_number", sa.String(), nullable=True)
    )
    op.add_column(
        "entity_address", sa.Column("flat_number", sa.String(), nullable=True)
    )
    op.add_column("entity_address", sa.Column("mpan", sa.String(), nullable=True))
    op.add_column("entity_address", sa.Column("mprn", sa.String(), nullable=True))
    op.drop_column("entity_address", "display_name")
    op.drop_column("entity_address", "uprn")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "entity_address",
        sa.Column("uprn", sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.add_column(
        "entity_address",
        sa.Column("display_name", sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.drop_column("entity_address", "mprn")
    op.drop_column("entity_address", "mpan")
    op.drop_column("entity_address", "flat_number")
    op.drop_column("entity_address", "house_number")
    op.drop_column("entity_address", "house_name")
    op.drop_column("entity_address", "address_line_2")
    op.drop_column("entity_address", "address")
    op.drop_column("entity_address", "country")
    # ### end Alembic commands ###
