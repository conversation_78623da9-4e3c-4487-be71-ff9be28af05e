"""dev

Revision ID: c59954290d48
Revises: e98da02d62d8
Create Date: 2023-12-12 15:49:56.362124

"""
import sqlalchemy as sa
from sqlalchemy_utils.types import UUIDType

from alembic import op

# revision identifiers, used by Alembic.
revision = "c59954290d48"
down_revision = "e98da02d62d8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("company", sa.Column("main_address_id", UUIDType(), nullable=True))
    op.create_foreign_key(
        None, "company", "entity_address", ["main_address_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "company", type_="foreignkey")
    op.drop_column("company", "main_address_id")
    # ### end Alembic commands ###
