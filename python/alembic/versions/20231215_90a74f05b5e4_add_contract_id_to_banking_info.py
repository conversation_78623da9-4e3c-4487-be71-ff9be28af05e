"""add contract id to banking info

Revision ID: 90a74f05b5e4
Revises: c59954290d48
Create Date: 2023-12-15 15:26:10.019608

"""
import sqlalchemy as sa
from sqlalchemy_utils.types import UUIDType

from alembic import op

# revision identifiers, used by Alembic.
revision = "90a74f05b5e4"
down_revision = "c59954290d48"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "company_banking_details", sa.Column("contract_id", UUIDType(), nullable=True)
    )
    op.create_foreign_key(
        None, "company_banking_details", "contract", ["contract_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "company_banking_details", type_="foreignkey")
    op.drop_column("company_banking_details", "contract_id")
    # ### end Alembic commands ###
