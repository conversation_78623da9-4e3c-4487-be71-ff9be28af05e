"""entity address line one is nullable

Revision ID: afcd88c60bb3
Revises: 7eec8a4b18e0
Create Date: 2024-01-03 11:31:51.757670

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "afcd88c60bb3"
down_revision = "7eec8a4b18e0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "entity_address", "address_line_1", existing_type=sa.VARCHAR(), nullable=True
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "entity_address", "address_line_1", existing_type=sa.VARCHAR(), nullable=False
    )
    # ### end Alembic commands ###
