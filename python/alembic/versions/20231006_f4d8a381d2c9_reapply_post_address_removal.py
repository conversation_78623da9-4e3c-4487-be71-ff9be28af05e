"""reapply_post_address_removal

Revision ID: f4d8a381d2c9
Revises: cd44ffd494c2
Create Date: 2023-10-06 15:07:27.663808

"""
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy_utils.types import ChoiceType, UUIDType

from alembic import op
from app.models.enums import UtilityType
from app.models.utils import TextPickleType

# revision identifiers, used by Alembic.
revision = "f4d8a381d2c9"
down_revision = "cd44ffd494c2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "entity_address",
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("timezone('Europe/London', now())"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("id", UUIDType(), nullable=False),
        sa.Column("address_line_1", sa.String(), nullable=False),
        sa.Column("postal_town", sa.String(), nullable=False),
        sa.Column("county", sa.String(), nullable=True),
        sa.Column("postcode", sa.String(), nullable=False),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.Column("uprn", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "gas_usage",
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("timezone('Europe/London', now())"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("id", UUIDType(), nullable=False),
        sa.Column("company_id", UUIDType(), nullable=False),
        sa.Column(
            "utility_type",
            ChoiceType(choices=UtilityType, impl=sa.Integer()),
            nullable=False,
        ),
        sa.Column("start_date", sa.DateTime(), nullable=True),
        sa.Column("total_annual_usage", sa.Integer(), nullable=False),
        sa.Column("provider_id", UUIDType(), nullable=False),
        sa.Column("non_watt_provider_name", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("mprn", sa.String(), nullable=True),
        sa.Column("usage", TextPickleType(length=256), nullable=True),
        sa.Column("day_unit_rate", sa.Integer(), nullable=True),
        sa.Column("smart_meter", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["company_id"],
            ["company.id"],
        ),
        sa.ForeignKeyConstraint(
            ["provider_id"],
            ["provider.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("company_site", sa.Column("address_id", UUIDType(), nullable=True))
    op.add_column("company_site", sa.Column("mprn", sa.String(), nullable=True))
    op.add_column(
        "company_site",
        sa.Column("utilities_managed", TextPickleType(length=256), nullable=True),
    )
    op.create_foreign_key(
        None, "company_site", "entity_address", ["address_id"], ["id"]
    )
    op.add_column("provider", sa.Column("recco_id", sa.String(), nullable=True))
    op.add_column("quote", sa.Column("unit_rate", sa.Float(), nullable=True))
    op.alter_column(
        "quote",
        "day_unit_rate",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=True,
    )
    op.alter_column(
        "quote",
        "standing_charge",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=True,
    )
    op.alter_column(
        "quote", "price_guaranteed", existing_type=sa.INTEGER(), nullable=True
    )
    op.alter_column("quote", "contract_type", existing_type=sa.VARCHAR(), nullable=True)
    op.add_column(
        "quote_list", sa.Column("gas_total_annual_usage", sa.Float(), nullable=True)
    )
    op.alter_column(
        "quote_list",
        "electricity_total_annual_usage",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=True,
    )
    op.alter_column(
        "quote_list",
        "electricity_tariff_usage_splits",
        existing_type=sa.VARCHAR(length=256),
        nullable=True,
    )
    op.alter_column(
        "quote_list",
        "electricity_tariff_usage_values",
        existing_type=sa.VARCHAR(length=256),
        nullable=True,
    )
    op.add_column(
        "soletrader_personal_address",
        sa.Column("address_id", UUIDType(), nullable=True),
    )
    op.alter_column(
        "soletrader_personal_address",
        "address",
        existing_type=sa.VARCHAR(),
        nullable=True,
    )
    op.alter_column(
        "soletrader_personal_address",
        "postcode",
        existing_type=sa.VARCHAR(),
        nullable=True,
    )
    op.create_foreign_key(
        None, "soletrader_personal_address", "entity_address", ["address_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "soletrader_personal_address", type_="foreignkey")
    op.alter_column(
        "soletrader_personal_address",
        "postcode",
        existing_type=sa.VARCHAR(),
        nullable=False,
    )
    op.alter_column(
        "soletrader_personal_address",
        "address",
        existing_type=sa.VARCHAR(),
        nullable=False,
    )
    op.drop_column("soletrader_personal_address", "address_id")
    op.alter_column(
        "quote_list",
        "electricity_tariff_usage_values",
        existing_type=sa.VARCHAR(length=256),
        nullable=False,
    )
    op.alter_column(
        "quote_list",
        "electricity_tariff_usage_splits",
        existing_type=sa.VARCHAR(length=256),
        nullable=False,
    )
    op.alter_column(
        "quote_list",
        "electricity_total_annual_usage",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=False,
    )
    op.drop_column("quote_list", "gas_total_annual_usage")
    op.alter_column(
        "quote", "contract_type", existing_type=sa.VARCHAR(), nullable=False
    )
    op.alter_column(
        "quote", "price_guaranteed", existing_type=sa.INTEGER(), nullable=False
    )
    op.alter_column(
        "quote",
        "standing_charge",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=False,
    )
    op.alter_column(
        "quote",
        "day_unit_rate",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=False,
    )
    op.drop_column("quote", "unit_rate")
    op.drop_column("provider", "recco_id")
    op.drop_constraint(None, "company_site", type_="foreignkey")
    op.drop_column("company_site", "utilities_managed")
    op.drop_column("company_site", "mprn")
    op.drop_column("company_site", "address_id")
    op.drop_table("gas_usage")
    op.drop_table("entity_address")
    # ### end Alembic commands ###
