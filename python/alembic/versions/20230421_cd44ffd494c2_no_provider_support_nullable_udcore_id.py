"""no-provider-support-nullable-udcore-id

Revision ID: cd44ffd494c2
Revises: f7c6fea6b8d2
Create Date: 2023-04-21 12:10:18.558992

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "cd44ffd494c2"
down_revision = "f7c6fea6b8d2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "electricity_usage_provider_id_fkey", "electricity_usage", type_="foreignkey"
    )
    op.create_foreign_key(
        None,
        "electricity_usage",
        "provider",
        ["provider_id"],
        ["id"],
        ondelete="cascade",
    )
    op.alter_column("provider", "udcore_id", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("provider", "udcore_id", existing_type=sa.VARCHAR(), nullable=False)
    op.drop_constraint(None, "electricity_usage", type_="foreignkey")
    op.create_foreign_key(
        "electricity_usage_provider_id_fkey",
        "electricity_usage",
        "provider",
        ["provider_id"],
        ["id"],
    )
    # ### end Alembic commands ###
