"""Address for company"""

import uuid

from sqlalchemy import Column, String
from sqlalchemy_utils import UUIDType

from app.models.orm_builder import BuildableORMBase

from ..common import MPANType


class EntityAddress(BuildableORMBase):
    """Generic address object"""

    __tablename__ = "entity_address"

    id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
    postcode = Column(String, nullable=False)
    county = Column(String, nullable=True)
    postal_town = Column(String, nullable=False)
    country = Column(String, nullable=True)
    address = Column(String, nullable=False)
    address_line_1 = Column(String, nullable=True)
    address_line_2 = Column(String, nullable=True)
    house_name = Column(String, nullable=True)
    house_number = Column(String, nullable=True)
    flat_number = Column(String, nullable=True)
    mpan = Column(MPANType, nullable=True)
    mprn = Column(String, nullable=True)
