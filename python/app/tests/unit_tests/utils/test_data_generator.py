# from app.models.companies import CharityCompany, LtdCompany
# from app.tests.utils.data_generator import generate_company, generate_full_company


# def test_generate_ltd_company():
#     ltd_company = generate_company(business_type="LTD")
#     assert isinstance(ltd_company, LtdCompany)
#     # Add more assertions here for each field


# def test_generate_charity_company():
#     charity_company = generate_company(business_type="CHARITY")
#     assert isinstance(charity_company, CharityCompany)
#     # Add more assertions here for each field


# def test_generate_full_company():
#     full_company = generate_full_company()
#     # Assertions for relationships and data integrity
#     assert full_company.contacts is not None
#     # Add more assertions for each sub-entity and their fields


# def test_generate_company_address_format():
#     company = generate_company()
#     address_object = company.address_object

#     assert isinstance(address_object, dict), "Address should be a dictionary"
#     expected_keys = {
#         "address",
#         "address_line_1",
#         "address_line_2",
#         "house_name",
#         "house_number",
#         "flat_number",
#         "postcode",
#         "postal_town",
#         "county",
#         "country",
#         "mpan",
#         "mprn",
#     }
#     assert (
#         set(address_object.keys()) == expected_keys
#     ), "Address does not contain expected keys"

#     # Additional checks for each field can be added here
