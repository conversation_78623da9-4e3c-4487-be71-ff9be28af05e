from mpan import MPAN

from app.models.enums import UtilityType
from app.tests.utils.data_generator import generate_site


class TestCompanySite:
    """Tests for the CompanySite model"""

    VALID_TEST_MPAN = "001112221312345678345"
    """This is a valid MPAN that we can use for testing"""

    VALID_TEST_MPRN = "7401456606"
    """This is a valid MPRN that we can use for testing"""

    def test_get_not_quotable_reason_electricity_with_bad_utility_type(self):
        """Test that get_not_quotable_reason returns an error if the utility type is invalid"""
        invalid_utility_type = -1

        site = generate_site(mpan=MPAN(self.VALID_TEST_MPAN))

        assert (
            site.get_not_quotable_reason(invalid_utility_type)
            == f'Unknown utility type "{invalid_utility_type}"'
        )

    def test_get_not_quotable_reason_electricity_no_mpan(self):
        """Test that we can't quote for electricity if there's no MPAN"""
        site = generate_site(mpan=None)

        assert (
            site.get_not_quotable_reason(UtilityType.ELECTRICITY)
            == "No (or invalid) MPAN"
        )

    def test_get_not_quotable_reason_electricity_with_mpan(self):
        """Test that we can quote for electricity if there's an MPAN"""
        site = generate_site(mpan=MPAN(self.VALID_TEST_MPAN))

        assert site.get_not_quotable_reason(UtilityType.ELECTRICITY) is None

    def test_get_not_quotable_reason_gas_no_mpan(self):
        """Test that we can't quote for electricity if there's no MPAN"""
        site = generate_site(mprn=None)

        assert site.get_not_quotable_reason(UtilityType.GAS) == "No (or invalid) MPRN"

    def test_get_not_quotable_reason_gas_with_mpan(self):
        """Test that we can quote for electricity if there's an MPAN"""
        site = generate_site(mprn=self.VALID_TEST_MPRN)

        assert site.get_not_quotable_reason(UtilityType.GAS) is None
