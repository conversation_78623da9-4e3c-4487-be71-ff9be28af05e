import pytest

from app.common.utils.hide_meter_numbers import hide_meter_number


@pytest.mark.parametrize(
    "n, k, expected",
    [
        ("123456789", 4, "*****6789"),
        ("123456789", 5, "****56789"),
        ("123456789", 6, "***456789"),
        ("123456789", 7, "**3456789"),
        ("123456789", 8, "*23456789"),
    ],
)
def test_expected(n, k, expected):
    """Test expected values."""
    assert hide_meter_number(n, k) == expected
