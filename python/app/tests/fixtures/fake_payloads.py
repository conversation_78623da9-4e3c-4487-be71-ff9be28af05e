import pytest

from app.integrations.lambdas.payloads.pdf import create_pdf_payload
from app.models.companies.company import Company
from app.tests.utils.data_generator import (
    generate_agreement,
    generate_full_company,
    generate_usage,
)

from .fake import fake


@pytest.fixture()
def pdf_payload():
    company: Company = generate_full_company()
    contract = company.contracts
    usage = generate_usage(company.id)
    agreement = generate_agreement(company.id)

    template_key = fake.name()
    current_provider_name = fake.company()
    new_provider_name = fake.company()
    industries = fake.company()

    company_registration_number = fake.random_number(digits=8)

    payload = create_pdf_payload(
        template_key,
        company,
        contract,
        company_registration_number,
        usage,
        current_provider_name,
        new_provider_name,
        industries,
        company.contacts[0],
        company.sites[0],
        agreement,
    )

    return payload
