"""Configuration for test functions to use."""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.config import settings as app_settings

# fixtures
# from .fixtures.http_async_client import mock_http_async_client  # noqa: F401

# ensure that pytest reads all fixtures defines under fixtures folder
pytest_plugins = [
    "app.tests.fixtures.fake_payloads",
    "app.tests.fixtures.fake",
    "app.tests.fixtures.http_async_client",
]


@pytest.fixture(scope="session")
def settings():
    """Main app settings"""
    return app_settings


@pytest_asyncio.fixture
async def async_session(settings):
    """Create a new async database session for each test."""
    portal_db_url = f"{settings.PORTAL_DB_USED}://{settings.PORTAL_DB_USER}:{settings.PORTAL_DB_PASSWORD}@{settings.PORTAL_DB_HOST}:{settings.PORTAL_DB_PORT}/{settings.PORTAL_DB_NAME}"
    async_engine = create_async_engine(portal_db_url)

    async with async_engine.begin() as conn:
        session = sessionmaker(conn, expire_on_commit=False, class_=AsyncSession)
        async with session() as db_session:
            yield db_session
            db_session.close()
            await db_session.rollback()
