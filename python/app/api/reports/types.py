ELECTRICITY_QUOTE_JSON_MAP = {
    "contract_duration": str,
    "contract_end_date": str,
    "is_comparison_provider": str,
    "day_unit_rate": str,
    "night_unit_rate": str,
    "weekend_unit_rate": str,
    "evening_unit_rate": str,
    "off_peak_unit_rate": str,
    "annual_price": str,
    "standing_charge": str,
    "capacity_charge": str,
    "price_guarantee": str,
    "contract_type": str,
}

GAS_QUOTE_JSON_MAP = {
    "contract_duration": str,
    "contract_end_date": str,
    "is_comparison_provider": str,
    "day_unit_rate": str,  # Set to this so that we can use the same template for both gas and electricity, (wont add any empty fields. )
    "annual_price": str,
    "standing_charge": str,
    "price_guarantee": str,
    "contract_type": str,
}
