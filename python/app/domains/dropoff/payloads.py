from pydantic import BaseModel
from typing_extensions import override

from app.common.utils.case_styles import CaseStyles


class DropoffEmailPayload(BaseModel):
    """This DTO represents the payload for the dropoff email."""

    company_name: str
    registration_number: str
    main_address: str
    main_postcode: str
    meter_type: str
    meter_number: str
    business_type: str
    industries: str
    current_supplier_name: str
    total_annual_usage: str
    supplier_name: str
    utility_type_name: str
    continue_path: str

    class Config:
        """Pydantic configuration."""

        # (Jude): this is a hack to get around the fact that the server is expecting camelCase
        alias_generator = CaseStyles.to_camel_case
        allow_population_by_field_name = True

    @override
    def dict(self, *args, **kwargs):
        """Override the dict method to use camelCase."""
        return super().dict(*args, **kwargs, by_alias=True)
