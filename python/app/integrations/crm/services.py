"""
This module provides functionality to retrieve services for an address from our own addresses API

Functions:
    get_services_by_address_id(postcode: str, address_id: str) -> dict | None:
        Fetches address details for a given postcode and address ID from the local API.
        It returns a dictionary containing address details if found, otherwise None.
"""

from typing import Optional, TypedDict

import httpx
from sanic.log import logger

from app.common.utils import call_endpoint
from app.config import settings


class Address(TypedDict, total=True):
    """Model for the CRM Addresses API response.

    Args:
        TypedDict (_type_): _description_
        total (bool, optional): _description_. Defaults to False.
    """

    id: str
    postcode: str
    county: Optional[str]
    postalTown: Optional[str]
    country: Optional[str]
    address: Optional[str]
    addressLine1: Optional[str]
    addressLine2: Optional[str]
    houseName: Optional[str]
    houseNumber: Optional[str]
    flatNumber: Optional[str]
    bgServiceArea: Optional[bool]
    createdAt: Optional[str]
    updatedAt: Optional[str]
    mpans: list[str]
    mprns: list[str]


async def get_address_by_address_id(address_id: str):
    """
    Gets the addresses with MPRN keys for a given address id.

    Args:
        address_id (str): The specific address ID to filter the results.

    Returns:
        Address | None: A dictionary with address details if found, otherwise None.

    The function queries a local API endpoint, filters the results based on the
    provided address ID, and returns the relevant address details.
    """

    # Construct the URL for the API request
    url = f"{settings.CRM_APP_URL}/api/addresses/{address_id}"

    logger.info(f"Calling API endpoint: {url}")

    try:
        address: dict[Address] = await call_endpoint(
            url, headers={"x-api-key": settings.CRM_PRIVATE_API_KEY}
        )
        return address
    except httpx.HTTPError as error:
        print(f"Error calling API endpoint: {error}")
        return None
    except Exception as error:
        print(f"Error calling API endpoint: {error}")
        return None


async def get_companies_house(company_number: str):
    """
    Gets companies house details for a given company number.
    """

    # Construct the URL for the API request
    url = f"{settings.CRM_APP_URL}/api/companies-house/{company_number}"

    logger.info(f"Calling API endpoint: {url}")

    try:
        address: Address = await call_endpoint(
            url, headers={"x-api-key": settings.CRM_PRIVATE_API_KEY}
        )
        return address
    except httpx.HTTPError as error:
        print(f"Error calling API endpoint: {error}")
        return None
    except Exception as error:
        print(f"Error calling API endpoint: {error}")
        return None
