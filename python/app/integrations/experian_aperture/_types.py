from typing import Optional

from pydantic import BaseModel


class MPRNAnnualUsageData(BaseModel):
    """Represents the annual usage data for a single MPRN"""

    # TODO: (Olly): Please note: These are not the current values within Aperture.
    mprn: Optional[str] = None

    total_eac: Optional[float] = None

    supplier_name: str
    """
    The supplier name

    This is shown on the frontend
    """

    supplier_efd: str
    """
    The EFD (effective date) of the supplier

    TODO (James) should we parse this as a date?
    """

    post_code: str
    """
    The postcode of the MPRN

    TODO (<PERSON>) we should remove this and link it to a CompanySite instead
    """
