from sanic.exceptions import Server<PERSON>rror
from sanic.log import logger

from app.common import local_mode
from app.common.db import AsyncSession, with_db_session
from app.common.is_in_local_mode import is_in_local_mode
from app.config import settings
from app.repository.provider import get_provider_by_recco_id

from ._types import MPRNAnnualUsageData
from .api_utils import make_experian_aperture_api_call


@with_db_session
async def get_supplier_id_by_recco_id(recco_id: str, session: AsyncSession = None):
    """Gets the supplier_id by given recco_id (Aperture supplier_name)"""

    return await get_provider_by_recco_id(recco_id, session)


async def get_gas_meter_by_mprn(mprn: str):
    """Gets gas meter details by MPRN."""
    if is_in_local_mode(settings):
        mock_meter_details = local_mode.MPRN_LOOKUP_METER_ADDITIONAL_DETAILS_8901264506

        mock_meter = mock_meter_details["result"]["addresses_formatted"][0]["address"][
            "gas_meters"
        ][0]

        return MPRNAnnualUsageData(
            mprn=mock_meter["mprn"],
            total_eac=mock_meter["offtake_quantity_annual"],
            supplier_name=mock_meter["supplier_name"],
            supplier_efd="2019-01-01",  # Not applicable for gas
            post_code=mock_meter["rel_address_postcode"],
        )
    else:
        response = await make_experian_aperture_api_call(
            version="v2",
            endpoint="lookup",
            mprn=mprn,
        )

        if response.status_code == 200:
            raw = response.json()
            addresses_formatted = raw["result"]["addresses_formatted"]

            if not addresses_formatted or len(addresses_formatted) == 0:
                logger.warning(
                    f"[ExperianAperture] No addresses found for MPRN ({mprn})"
                )
                # We return None to allow manual entry flow
                return None

            gas_meters = addresses_formatted[0]["address"]["gas_meters"]

            if not gas_meters or len(gas_meters) == 0:
                logger.warning(
                    f"[ExperianAperture] No gas meters found for MPRN ({mprn})"
                )
                # We return None to allow manual entry flow
                return None

            gas_meter = gas_meters[0]

            return MPRNAnnualUsageData(
                mprn=gas_meter["mprn"],
                total_eac=gas_meter["offtake_quantity_annual"],
                supplier_name=gas_meter["supplier_name"],
                supplier_efd="2019-01-01",  # Not applicable for gas
                post_code=gas_meter["rel_address_postcode"],
            )

    error = (
        f"[ExperianAperture] Error ({response.status_code}) getting gas meter "
        f"by mprn ({mprn}): {response.text}"
    )
    logger.error(error)
    raise ServerError(error)
