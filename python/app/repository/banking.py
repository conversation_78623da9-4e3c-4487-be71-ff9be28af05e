from typing import Union

from sqlalchemy import select

from app.common.db import AsyncSession
from app.models.companies.banking import CompanyBankingDetails


async def get_banking_details_by_contract_id(
    contract_id: str, session: AsyncSession
) -> Union[CompanyBankingDetails, None]:
    """
    Gets the copany bank details for the specific contract.
    Users can provide multiple bank details, so we need to ensure we show the correct one
    """
    result = await session.execute(
        select(CompanyBankingDetails).where(
            CompanyBankingDetails.contract_id == contract_id
        )
    )

    return result.scalars().first()
