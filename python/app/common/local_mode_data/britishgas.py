from datetime import datetime

from dateutil.relativedelta import relativedelta

from app.common.utils import JSONTemplateRenderer

recent_end_date = (datetime.now() + relativedelta(years=-1, days=90)).strftime(
    "%Y-%m-%d"
)

LOCAL_MODE_POSTCODE_SEARCH_EH6_5DY = JSONTemplateRenderer.render_template(
    "udprn_postcode_search_eh6_5dy"
)
LOCAL_MODE_UDPRN_POSTCODE_SEARCH_EH6_5DY_906338794 = (
    JSONTemplateRenderer.render_template(
        "udprn_postcode_search_eh6_5dy_906338794",
        {
            "recent_end_date": recent_end_date,
        },
    )
)
