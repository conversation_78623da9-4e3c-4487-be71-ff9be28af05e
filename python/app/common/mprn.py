def is_mprn_valid(mprn: str):
    """
    Checks if an MPRN is valid: it has to pass the validation for the last 2 digits
    (checksum).

    The final 2 digits in the MPRN are the check digits, and validate the preceding
    digits using a modulus 11 test. See
    https://en.everybodywiki.com/Meter_Point_Reference_Number for details.

        Args:
            mprn (str): MPRN value.

        Returns:
            bool: True if valid. False if invalid.
    """
    try:
        if (
            int(mprn[-2:])
            == sum(
                factor * int(digit)
                for factor, digit in zip(list(range(len(mprn) - 2, 0, -1)), mprn)
            )
            % 11
        ):
            return True
        else:
            return False
    except Exception:
        return False
