from typing import Union


def hide_meter_number(n: Union[str, int], k: int = 4) -> str:
    """Hide the meter number, only show last `k` digits."""
    assert len(n) >= k, f"Meter number must be at least {k} digits long."
    if isinstance(n, int):
        n = str(n)
    i = len(n) - k
    return f"{'*'* i}{n[i:]}"


def hide_mpan_middle(mpan: str):
    """Hide the middle of the mpan"""
    return mpan[:8] + ("*" * 10) + mpan[-3:]
