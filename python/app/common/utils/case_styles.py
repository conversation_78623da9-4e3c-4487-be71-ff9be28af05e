"""Case styles utility class to convert to/fro camelCase/snake_case."""


class CaseStyles:
    @staticmethod
    def to_camel_case(text: str) -> str:
        """Convert from snake_case to camelCase.

        Args:
            text (str): Text to convert.

        Returns:
            str: Camel case text.
        """
        tokens = text.split("_")
        return tokens[0] + "".join([token.capitalize() for token in tokens[1:]])

    @staticmethod
    def to_snake_case(text: str) -> str:
        """Convert from camelCase to snake_case.

        Args:
            text (str): Text to convert.

        Returns:
            str: Snake case text.
        """
        return "".join(
            ["_" + char.lower() if char.isupper() else char for char in text]
        ).lstrip("_")
