{"extends": "@muravjev/configs-ts-react", "compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "strict": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "target": "es6", "types": ["@types/jest", "node"]}, "include": ["./packages/components/**/*"], "exclude": ["node_modules", ".yarn", ".changeset", ".github", ".husky", ".turbo", "vercel", ".vscode"]}