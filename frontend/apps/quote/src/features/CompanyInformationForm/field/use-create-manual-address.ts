import { useCallback, useState, useEffect } from 'react'
import { useSnackbar } from 'notistack'
import { useFormContext, type Path } from 'react-hook-form'
import type { EntityAddress } from '@watt/constants'
import en from '~/i18n'
import { submitManualAddressThunk } from '~/store/reducers/company/extraReducers'
import { useAppDispatch } from '~/store/selectors'
import type { CompanyDetailsForm } from '../formConfig'

const i18n = en.companyInformation.contactForm

type CreateManualAddressProps = {
  isManualAddress: boolean
  addressLine1: Path<CompanyDetailsForm>
  addressLine2: Path<CompanyDetailsForm>
  postalTown: Path<CompanyDetailsForm>
  county: Path<CompanyDetailsForm>
  postcode: Path<CompanyDetailsForm>
  setSelectedAddress: (address: EntityAddress) => void
  instanceId: string
}

/**
 * Custom hook containing manual address creation logic
 */
export function useCreateManualAddress(props: CreateManualAddressProps) {
  const {
    isManualAddress,
    addressLine1: addressLine1Path,
    addressLine2: addressLine2Path,
    postalTown: postalTownPath,
    county: countyPath,
    postcode: postcodePath,
    setSelectedAddress,
    instanceId,
  } = props

  const formContext = instanceId

  const snackbar = useSnackbar()
  const dispatch = useAppDispatch()
  const { setValue, setError, clearErrors, getValues } = useFormContext<CompanyDetailsForm>()
  const [isLoading, setIsLoading] = useState(false)
  const [manualAddressCreated, setManualAddressCreated] = useState(false)

  const clearManualAddress = useCallback(() => {
    setManualAddressCreated(false)
    setValue(addressLine1Path, '')
    setValue(addressLine2Path, '')
    setValue(postalTownPath, '')
    setValue(countyPath, '')
    clearErrors([addressLine1Path, addressLine2Path, postalTownPath, countyPath])
  }, [setValue, addressLine1Path, addressLine2Path, postalTownPath, countyPath, clearErrors])

  /**
   * Validate and create the manual address
   */
  const createManualAddress = useCallback(async () => {
    if (!isManualAddress) {
      return
    }

    const addressLine1 = getValues(addressLine1Path) as string
    const addressLine2 = getValues(addressLine2Path) as string
    const postalTown = getValues(postalTownPath) as string
    const county = getValues(countyPath) as string
    const postcode = getValues(postcodePath) as string
    let hasError = false
    clearErrors([addressLine1Path, postalTownPath, postcodePath])

    if (!addressLine1) {
      setError(addressLine1Path, { type: 'required', message: 'Required information.' })
      hasError = true
    }

    if (!postalTown) {
      setError(postalTownPath, { type: 'required', message: 'Required information.' })
      hasError = true
    }

    if (!postcode) {
      setError(postcodePath, { type: 'required', message: 'Required information.' })
      hasError = true
    }

    if (hasError) {
      return
    }

    setIsLoading(true)
    setManualAddressCreated(false)

    try {
      console.log('Submitting manual address:', {
        line_1: addressLine1,
        line_2: addressLine2,
        postal_town: postalTown,
        county: county,
        postcode: postcode,
        instanceId,
      })

      // TODO: Dispatch the create manual address thunk

      setSelectedAddress({
        id: '123',
        postcode: 'SN7 7AA',
        county: 'Berkshire',
        postalTown: 'Newbury',
        country: 'United Kingdom',
        address: '12 Bakers Street, Gosford House, Newbury, Berkshire, SN7 7AA',
        addressLine1: 'Bakers Street',
        addressLine2: '',
        meterPoints: {
          1: 0,
          2: 0,
        },
      })

      try {
        /** 🔥 dispatch the thunk and wait for canonical address back */
        const verifiedAddress = await dispatch(
          submitManualAddressThunk({
            postcode,
            county,
            postalTown,
            country: 'United Kingdom',
            displayName: `${addressLine1}, ${addressLine2 ? `${addressLine2}, ` : ''}${postalTown}, ${
              county ? `${county}, ` : ''
            }${postcode}`,
            addressLine1,
            addressLine2,
            houseName: '',
            houseNumber: '',
            flatNumber: '',
          })
        ).unwrap()

        /** 🎉 success – update parent component & UX */
        // Use the backend-provided UUID for the address ID
        setSelectedAddress(verifiedAddress)
        setManualAddressCreated(true)
        snackbar.enqueueSnackbar(i18n.notifications.successfulManualAddress, {
          variant: 'success',
        })
        /** We don't clear manual fields now, they're displayed in the readonly field */
        // clearManualAddress()
      } catch (message) {
        // `message` is set in rejectWithValue
        snackbar.enqueueSnackbar(String(message), { variant: 'error' })
        setManualAddressCreated(false)
      } finally {
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Error creating manual address:', error)
      snackbar.enqueueSnackbar('An unexpected error occurred.', { variant: 'error' })
      setManualAddressCreated(false)
    } finally {
      setIsLoading(false)
    }
  }, [
    isManualAddress,
    getValues,
    addressLine1Path,
    addressLine2Path,
    postalTownPath,
    countyPath,
    postcodePath,
    setError,
    clearErrors,
    snackbar,
    dispatch,
    setSelectedAddress,
    instanceId,
  ])

  useEffect(() => {
    if (!isManualAddress) {
      setManualAddressCreated(false)
      // Optionally clear errors when switching away
      // clearErrors([addressLine1Path, addressLine2Path, postalTownPath, countyPath]);
    }
  }, [isManualAddress])

  return {
    isLoading,
    manualAddressCreated,
    createManualAddress,
    clearManualAddress,
  }
}
