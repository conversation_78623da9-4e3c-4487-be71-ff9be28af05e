import type { Theme } from '@mui/material/styles'
import { makeStyles } from '@mui/styles'

export const useStyles = makeStyles((theme: Theme) => ({
  circularProgress: {
    position: 'absolute',
    top: 'calc(50% - 8px)',
    left: 'calc(50% - 8px)',
  },
  linkButton: {
    padding: theme.spacing(0.5, 0), // Minimal vertical padding, no horizontal
    minWidth: 0,
    height: 'auto',
    lineHeight: 'inherit',
    textTransform: 'none',
    color: theme.palette.primary.main, // Use primary green color
    fontWeight: 'normal', // Use normal font weight like standard text
    justifyContent: 'flex-start', // Align text to the left
    '&:hover': {
      textDecoration: 'underline',
      backgroundColor: 'transparent', // Ensure no background on hover
    },
    marginBottom: '20px !important',
  },
  removeMarginBottom: {
    marginBottom: '0px !important',
  },
  buttonContainer: {
    marginBottom: '20px',
  },
  manualAddressErrorText: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingTop: '0.25em',
  },
}))
