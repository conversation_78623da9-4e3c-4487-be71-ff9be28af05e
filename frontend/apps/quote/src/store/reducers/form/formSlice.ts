import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { NOT_QUOTABLE_ERROR_CODE, UtilityKindType } from '@watt/constants'
import { getUsageByUtilityTypeThunk } from '../usage/extraReducers'

export interface FormState {
  selectedUtilities: UtilityKindType[]
  notQuotableUtilities: NotQuotableUtility[]
  currentFlow?: UtilityKindType
}

const initialState: FormState = {
  selectedUtilities: [],
  notQuotableUtilities: [],
  currentFlow: undefined,
}

type NotQuotableUtility = {
  utility: UtilityKindType
  reason: string
}

type SetSelectedUtilitiesPayload = {
  selectedUtilities: UtilityKindType[]
}

type SetCurrentFlowPayload = {
  flow: UtilityKindType
}

export type GetUsageByUtilityTypeThunkRejectedPayload = {
  utilityType: UtilityKindType
  error: {
    description: string,
    message: string,
    status: number
  }
}

function sortUtilities(utilities: UtilityKindType[]) {
  return utilities.sort((a, b) => {
    return a - b
  })
}

export const formSlice = createSlice({
  name: 'company',
  initialState,
  reducers: {
    setSelectedUtilities(state, action: PayloadAction<SetSelectedUtilitiesPayload>) {
      state.selectedUtilities = action.payload.selectedUtilities
      state.selectedUtilities = sortUtilities(action.payload.selectedUtilities)
    },
    setCurrentFlow(state, action: PayloadAction<SetCurrentFlowPayload>) {
      state.currentFlow = action.payload.flow
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getUsageByUtilityTypeThunk.fulfilled, (state, action) => {
      const { type: utilityType } = action.payload

      state.notQuotableUtilities = state.notQuotableUtilities.filter((item) => {
        return item.utility !== utilityType
      })
    })
    builder.addCase(getUsageByUtilityTypeThunk.rejected, (state, action) => {
      const {
        utilityType,
        error: { message, status: code },
      } = action.payload as GetUsageByUtilityTypeThunkRejectedPayload
      if (code === NOT_QUOTABLE_ERROR_CODE) {
        state.notQuotableUtilities = [
          ...state.notQuotableUtilities,
          {
            utility: utilityType,
            reason: message,
          },
        ]
      }
    })
    builder.addCase('CLEAR_PERSISTED_DATA', () => {
      return initialState;
    })
  },
})

export const { setSelectedUtilities, setCurrentFlow } = formSlice.actions
