import { Container } from '@mui/material'
import { NextPage } from 'next'
import { UTILITIES_LOOKUP } from '@watt/constants'
import { LayoutWithSubheader } from '~/components/LayoutWithSubheader/LayoutWithSubheader'
import { cfg } from '~/config/config'
import { UtilityQuote } from '~/features/UtilityQuote/UtilityQuote'
import { RootState } from '~/store'
import { useAppSelector } from '~/store/selectors'
import { getStepsBySelectedUtilityTypes } from '~/utils/steps'

const GasQuotePage: NextPage = () => {
  const utilityTypes = useAppSelector((state: RootState) => state.form.selectedUtilities)
  const steps = getStepsBySelectedUtilityTypes({ currentPage: cfg.pages.gas.quote, utilityTypes })
  return (
    <LayoutWithSubheader steps={steps}>
      <Container>
        <UtilityQuote utilityType={UTILITIES_LOOKUP.GAS} />
      </Container>
    </LayoutWithSubheader>
  )
}

export default GasQuotePage
