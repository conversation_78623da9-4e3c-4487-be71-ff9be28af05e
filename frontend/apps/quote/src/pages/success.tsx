import { useEffect, useMemo } from 'react'
import { DoneOutline, Phone } from '@mui/icons-material'
import { Container, Grid, Link, Typography } from '@mui/material'
// use styles mui
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import type { NextPage } from 'next'
import { Button } from '@watt/components'
import { GasIcon, ElectricIcon, WaterIcon } from '@watt/components'
import { UTILITIES_LOOKUP, UTILITY_TYPES } from '@watt/constants'
import { useSpacing } from '@watt/theme'
import { PRIMARY_COLORS } from '@watt/theme'
import { ConfirmationPageBox } from '~/components/ConfirmationPageBox/ConfirmationPageBox.component'
import { LayoutDefault } from '~/components/LayoutDefault/LayoutDefault'
import { ContractPreviewSection } from '~/components/UtilityContract'
import { WATT_PHONE_NUMBER } from '~/constants/global'
import { getContractsByUtilityTypeThunk } from '~/store/reducers/contracts/extraReducers'
import { useAppDispatch, useAppSelector } from '~/store/selectors'

export const useConfirmationStyles = makeStyles(() => ({
  root: {
    padding: 30,
    borderRadius: '10px',
  },
  thankyouHeader: {
    display: 'flex',
    color: PRIMARY_COLORS.dark,
  },
  acceptedHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tickIcon: {
    color: PRIMARY_COLORS.light,
    background: PRIMARY_COLORS.dark,
    borderRadius: '50%',
    padding: '10px',
    animation: '$form 2s cubic-bezier(0.2, 0.8, 0.2, 1) forwards',
  },
  '@keyframes form': {
    '0%': { transform: 'scale(0.5) rotate(-45deg)', opacity: 0 },
    '100%': { transform: 'scale(1) rotate(0deg)', opacity: 1 },
  },
  contractMain: {},
  bottomCardRoot: {
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    padding: '1em',
  },
  bottomCardFlex: {
    display: 'flex',
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    background: '#073D55',
    padding: '1.75em',
    borderRadius: '10px',
  },
  getInTouchCTA: {
    background: 'white',
    color: 'black',
    padding: '0.5em',
    border: '1px solid yellow',
  },
  bottomCardEmpty: {
    display: 'flex',
    flex: 1,
  },
  cardsRoot: {
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardGridLeft: {
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    background: 'white',
    border: '3px solid #FEDF59',
    borderRadius: '30px',
    padding: 10,
  },
  iconLeft: {
    background: 'white',
    border: '5px solid #09415D',
    boxShadow: '0px 0px 5px 5px',
  },
  getQuoteCTA: {
    background: 'yellow',
  },
  cardGridMid: {
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    background: 'white',
    border: '3px solid ' + PRIMARY_COLORS.light,
    borderRadius: '30px',
    padding: 10,
  },
  iconMid: {
    background: 'white',
    border: '5px solid #09415D',
    boxShadow: '0px 0px 5px 5px',
  },
  cardGridRight: {
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    background: 'white',
    border: '3px solid #2CB9FF',
    borderRadius: '30px',
    padding: 10,
  },
  iconRight: {
    background: 'white',
    border: '5px solid #09415D',
    boxShadow: '0px 0px 5px 5px',
  },
  icon: {
    background: 'white',
    border: '5px solid #09415D',
    boxShadow: '0px 0px 5px 5px',
  },
  textWhite: {
    color: 'white',
  },
  textBlack: {
    color: '#073D55',
    fontWeight: 'bold',
  },
}))

const ConfirmationPage: NextPage = () => {
  const [mt8, mt4, mt2, mb8, pt8, pr8, pl8] = useSpacing('mt8', 'mt4', 'mt2', 'pt8', 'pr8', 'pl8', 'pb8')
  const dispatch = useAppDispatch()

  const selectedUtilities = useAppSelector((state) => state.form.selectedUtilities)
  const notQuotableUtilities = useAppSelector((state) => state.form.notQuotableUtilities)
  const currentFlow = useAppSelector((state) => state.form.currentFlow)

  /**
   * Utilities will have been marked as not quotable if we weren't able to provide a quote
   */
  const validUtilities = useMemo(
    () => selectedUtilities.filter((utility) => !notQuotableUtilities.find((u) => u.utility === utility)),
    [selectedUtilities, notQuotableUtilities]
  )

  // TODO (James) recommend a refactor here in future to support variable-length utilities, i.e. after gas/electricity
  const electricityPdfUrlFromStore = useAppSelector((state) => state.cart['electricity']?.contract.pdfUrl)
  const gasPdfUrlFromStore = useAppSelector((state) => state.cart['gas']?.contract.pdfUrl)

  useEffect(() => {
    // TODO (James) consider a loading spinner while we wait for these - see how it looks
    for (const utility of selectedUtilities) {
      dispatch(getContractsByUtilityTypeThunk({ utilityType: utility, isSigned: true }))
    }
  }, [dispatch, selectedUtilities])

  const electricityPdfUrl = validUtilities.includes(UTILITIES_LOOKUP.ELECTRICITY) ? electricityPdfUrlFromStore : null
  const gasPdfUrl = validUtilities.includes(UTILITIES_LOOKUP.GAS) ? gasPdfUrlFromStore : null

  const classes = useConfirmationStyles()

  return (
    <LayoutDefault>
      <Container className={mt4}>
        <div className={clsx(mb8, pt8, pr8, pl8)}>
          <div className={classes.root}>
            <Grid container spacing={1} direction="column">
              <Grid item xs={12} md={5}>
                <Typography variant="h4" className={classes.thankyouHeader}>
                  Thank you for using Watt.co.uk
                </Typography>
              </Grid>

              <Grid container direction={'row'} spacing={10} className={classes.acceptedHeader}>
                <Grid item xs={11} md={6}>
                  <Typography variant="h4">Your chosen quote has been accepted....</Typography>
                </Grid>
                <Grid item xs={1}>
                  <Typography variant="h1">
                    <DoneOutline fontSize="inherit" className={classes.tickIcon} />
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <br />
            <hr />
            <br />
            <Typography variant="h5">Next steps...</Typography>
            <div className={mt2}>
              <Typography variant="body2">
                Now you have chosen your contract and tariff, we will confirm when your contract has been locked in by
                your chosen supplier and confirm your energy supply start date. If you have more than one energy supply
                point, we will inform you of the start date for each one, as it&apos;s confirmed. In some cases,
                suppliers can reject the contract subject to credit check, pricing issues or reasons beyond our control
                and if this happens we will contact you with an alternative solution. In some cases, previous suppliers
                can object to accounts being transferred. Reasons for objecting can include a customer is still in a
                fixed term contract or there is debt on the account. If this happens we will contact you and we can work
                to resolve the objection. For more information about why suppliers object, please view this FAQ on our
                website.
              </Typography>
            </div>
            <br />
            <hr />
            <br />
            <div className={classes.contractMain}>
              {electricityPdfUrl && (
                <div className={mt2}>
                  <Grid container>
                    <Typography variant="h5">Electricity contract</Typography>
                    <Grid item className={mt8} xs={12}>
                      <ContractPreviewSection url={electricityPdfUrl} contractViewed={true} />
                    </Grid>
                  </Grid>
                </div>
              )}
              {gasPdfUrl && (
                <div className={mt2}>
                  <Grid container>
                    <Typography variant="h5">Gas contract</Typography>
                    <Grid item className={mt8} xs={12}>
                      <ContractPreviewSection url={gasPdfUrl} contractViewed={true} />
                    </Grid>
                  </Grid>
                </div>
              )}
            </div>
            <div className={mt8}>
              <Typography variant="subtitle2">Terms and conditions</Typography>
              <Typography variant="body1" className={mt4}>
                A copy of your terms and conditions and the principle terms are available on our site{' '}
                <Link
                  href="/assets/pdf/watt-terms-and-conditions.pdf"
                  target="_blank"
                  rel="noreferrer"
                  style={{ fontWeight: '600', color: 'black' }}
                >
                  here.
                </Link>
              </Typography>
              <Typography variant="caption">
                Prices are valid at the time of quotation but may be withdrawn at the supplier&apos;s discretion due to
                fluctuations in the energy market and are subject to a satisfactory credit check. All Prices quoted are
                base unity rate+1p/kwh commision and standing charge for energy. These prices may include FIT, RO and
                other pass-through charges and government charges or taxes unless otherwise stated. In order for you to
                move to another supplier, contract termination has to be sent to your current supplier. If you
                subsequently fail to agree a new contract before the end of the contract, the current supplier will
                charge you out-of-contract rates until such time as a new contract is agreed. For more information
                please see you contract terms and conditions or contact the sender. The sender does not accept
                responsibility for any errors or omissions.
              </Typography>
            </div>
          </div>
          <div className={clsx(mt8, classes.cardsRoot)}>
            <Grid container spacing={4}>
              {currentFlow && (
                <ConfirmationPageBox
                  title={`Want to renew or switch your utility supplier to reduce ${UTILITY_TYPES[currentFlow]} bill?`}
                  body={
                    <>
                      Want to renew or switch your utility supplier to reduce gas bill? Want to renew or switch your
                      utility supplier to reduce gas bill? Want to renew or switch your utility supplier to reduce gas
                      bill?
                    </>
                  }
                  buttonTitle={'Get your quote'}
                  buttonHref={'/'}
                  buttonColor={'#FEDF59'}
                  icon={<GasIcon />}
                  cardClass={classes.cardGridLeft}
                  classes={classes}
                  insertSlogan={false}
                />
              )}

              <ConfirmationPageBox
                title={'Our Energy Solutions'}
                body={
                  <>
                    <Link
                      href="https://watt.co.uk"
                      target="_blank"
                      rel="noreferrer"
                      style={{ fontWeight: '600', color: 'black' }}
                    >
                      Watt.co.uk
                    </Link>{' '}
                    and our partners assist businesses in their push for energy demand destruction by using energy
                    efficient equipment and/or micro-generation to reduce customers&apos; energy requirements to create
                    a more stable UK energy market for the future.
                    <br />
                  </>
                }
                buttonTitle={'Give us a call'}
                buttonHref={'https://watt.co.uk/contact/'}
                buttonColor={''}
                icon={<ElectricIcon />}
                cardClass={classes.cardGridMid}
                classes={classes}
                insertSlogan={true}
              />

              <ConfirmationPageBox
                title={'Save on your water bills today'}
                body={
                  <>
                    We&apos;re here to help you find the best possible deal for your business&apos; water and waste
                    water services. A member of a team can help guide you through the switching process and help you
                    make the most of your buying power in the newly deregulated market.
                    <br />
                  </>
                }
                buttonTitle="Get a quote"
                buttonHref={'https://watt.co.uk/contact/'}
                buttonColor={'#2CB9FF'}
                icon={<WaterIcon />}
                cardClass={classes.cardGridRight}
                classes={classes}
                insertSlogan={true}
              />
            </Grid>
          </div>
          <div className={clsx(mt8, classes.bottomCardRoot)}>
            <div className={classes.bottomCardEmpty} />
            <div className={classes.bottomCardFlex}>
              <Typography variant="h6" style={{ color: 'white' }}>
                ALTERNATIVELY, GIVE US A CALL ON:
              </Typography>
              <Link href={`tel:${WATT_PHONE_NUMBER}`}>
                <Button variant="contained" color="secondary" className={mt4} endIcon={<Phone />}>
                  {WATT_PHONE_NUMBER}
                </Button>
              </Link>
            </div>
            <div className={classes.bottomCardEmpty} />
          </div>
        </div>
      </Container>
    </LayoutDefault>
  )
}

export default ConfirmationPage
