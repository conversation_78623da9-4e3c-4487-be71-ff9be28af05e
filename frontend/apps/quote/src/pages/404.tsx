import { Typography } from '@mui/material'
import { Theme } from '@mui/material'
import { makeStyles } from '@mui/styles'
import { NextPage } from 'next'
import Link from 'next/link'
import { PRIMARY_COLORS } from '@watt/theme'

export const useStyles = makeStyles((theme: Theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column',
    },
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    padding: '0 2rem',
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
  },
  title: {
    fontSize: '3rem',
    marginBottom: '1rem',
  },
  text: {
    fontSize: '1.6rem',
    marginBottom: '0.5rem',
  },
  linkText: {
    color: PRIMARY_COLORS.dark,
    fontSize: '1.6rem',
    marginBottom: '0.5rem',
  },
}))

const NotFoundPage: NextPage = () => {
  const classes = useStyles()

  return (
    <div className={classes.container}>
      <div>
        <img src="/assets/img/404.png" alt="Bill logo" width={200} height={284} />
      </div>
      <div className={classes.content}>
        <Typography variant="h5" className={classes.title}>
          Page Not Found
        </Typography>
        <Typography variant="body1" className={classes.text}>
          The page you requested could not be found.
        </Typography>
        <Typography variant="body1" className={classes.text}>
          Return to the{' '}
          <Link href="/" passHref>
            <Typography variant="body2" component="a" className={classes.linkText}>
              home page
            </Typography>
          </Link>
          .
        </Typography>
      </div>
    </div>
  )
}

export default NotFoundPage
