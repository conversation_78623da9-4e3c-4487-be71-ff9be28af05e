import { Container } from '@mui/material'
import { NextPage } from 'next'
import { UTILITIES_LOOKUP } from '@watt/constants'
import { LayoutWithSubheader } from '~/components/LayoutWithSubheader/LayoutWithSubheader'
import { UtilityQuote } from '~/features/UtilityQuote/UtilityQuote'
import { RootState } from '~/store'
import { useAppSelector } from '~/store/selectors'
import { getStepsBySelectedUtilityTypes } from '~/utils/steps'

const ElectricityQuotePage: NextPage = () => {
  const utilityTypes = useAppSelector((state: RootState) => state.form.selectedUtilities)
  const steps = getStepsBySelectedUtilityTypes({ currentPage: '/electricity/quote', utilityTypes })

  return (
    <LayoutWithSubheader steps={steps}>
      <Container>
        <UtilityQuote utilityType={UTILITIES_LOOKUP.ELECTRICITY} />
      </Container>
    </LayoutWithSubheader>
  )
}

export default ElectricityQuotePage
