import { useState } from 'react'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { Divider, List, ListItem, Typography } from '@mui/material'
import clsx from 'clsx'
import { Button } from '@watt/components'
import { useSpacing } from '@watt/theme'
import { useStyles } from './mobile.drawer.content.styles'

// TODO: Update the links to be in the constants file once the https://watt.co.uk/ is updated.
const DRAWER_LIST = [
  {
    label: 'GET A QUOTE',
    link: '/',
  },
  {
    label: 'BUSINESS BENEFITS',
    children: [
      {
        label: 'Best Utility Contracts',
        link: `https://watt.co.uk/large-businesses/`,
      },
      {
        label: 'Our Tailored Services',
        link: `https://watt.co.uk/large-businesses-our-tailored-services/`,
      },
      {
        label: 'Corporate Customers',
        link: `https://watt.co.uk/large-businesses-corporate-customers/`,
      },
      {
        label: 'Pass Through & Fixed Contracts',
        link: `https://watt.co.uk/pass-through-fixed-contracts/`,
      },
      {
        label: 'Case Studies',
        link: `https://watt.co.uk/case-studies/`,
      },
      {
        label: 'Enquire Today',
        link: '',
      },
    ],
    icon: <ArrowDropDownIcon />,
  },
  {
    label: 'REVIEWS',
    link: 'https://watt.co.uk/reviews/',
  },
  {
    label: 'NEWS',
    link: 'https://watt.co.uk/news/',
  },
  {
    label: 'ABOUT US',
    link: 'https://watt.co.uk/about-watt/',
  },
  {
    label: 'CONTACT',
    link: 'https://watt.co.uk/contact/',
  },
]

export const MobileDrawerContent = (): JSX.Element => {
  const classes = useStyles()
  const [pl10, pl14, mx10, mt8, mb4, py4] = useSpacing('pl10', 'pl14', 'mx10', 'mt8', 'mb4', 'py4')
  const [openState, setOpenState] = useState(false)
  const open = Boolean(openState)

  return (
    <>
      <List>
        {DRAWER_LIST.map((item) => {
          return (
            <ListItem key={item.label} className={classes.listItem} disableGutters>
              {item.children ? (
                <div>
                  <Button
                    classes={{ root: classes.buttonRoot }}
                    fullWidth
                    onClick={() => setOpenState(!open)}
                    className={clsx(pl10, py4, classes.link, classes.benefitsHeader)}
                  >
                    {item.label} {item.icon ? item.icon : null}
                  </Button>
                  {open &&
                    item.children.map((child) => {
                      return (
                        <Button
                          key={child.label}
                          fullWidth
                          size="small"
                          href={child.link}
                          className={clsx(pl14, classes.link, classes.benefitsLinks)}
                        >
                          <Typography variant="caption">{child.label}</Typography>
                        </Button>
                      )
                    })}
                </div>
              ) : (
                <Button
                  classes={{ root: classes.buttonRoot }}
                  fullWidth
                  size="small"
                  className={clsx(pl10, py4, classes.link)}
                  href={item.link}
                >
                  {item.label}
                </Button>
              )}
            </ListItem>
          )
        })}
      </List>
      <Divider className={clsx(mx10, mt8, mb4)} />
    </>
  )
}
