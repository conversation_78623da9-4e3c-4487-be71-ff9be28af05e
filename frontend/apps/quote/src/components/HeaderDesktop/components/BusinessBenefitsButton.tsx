import React from 'react'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { Link, Menu, MenuItem, Typography } from '@mui/material'
import { useStyles } from './BusinessBenefitsButton.styles'

// TODO: Update the links to be in the constants file once the https://watt.co.uk/ is updated.
const benefitsLinks = [
  { link: `https://watt.co.uk/large-businesses/`, text: 'Best Utility Contracts' },
  {
    link: `https://watt.co.uk/large-businesses-our-tailored-services/`,
    text: 'Our Tailored Services',
  },
  {
    link: `https://watt.co.uk/large-businesses-corporate-customers/`,
    text: 'Corporate Customers',
  },
  {
    link: `https://watt.co.uk/pass-through-fixed-contracts/`,
    text: 'Pass Through & Fixed Contracts',
  },
  { link: `https://watt.co.uk/case-studies/`, text: 'Case Studies' },
  { link: '', text: 'Enquire Today' },
]

export function BusinessBenefitsButton() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const classes = useStyles()

  function handleClick(event: React.MouseEvent<HTMLAnchorElement>) {
    if (anchorEl !== event.currentTarget) {
      setAnchorEl(event.currentTarget)
    }
  }

  function handleClose() {
    setAnchorEl(null)
  }

  return (
    <div>
      <Link
        aria-owns={anchorEl ? 'simple-menu' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
        onMouseOver={handleClick}
      >
        <Typography variant="button" className={classes.button}>
          Business Benefits <ArrowDropDownIcon />
        </Typography>
      </Link>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        MenuListProps={{ onMouseLeave: handleClose }}
        PaperProps={{
          sx: {
            bgcolor: '#073348',
          },
        }}
      >
        {benefitsLinks.map((link) => (
          <MenuItem key={link.text}>
            <Link href={link.link}>
              <Typography variant="caption" className={classes.menuItem}>
                {link.text}
              </Typography>
            </Link>
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}
