import { App<PERSON><PERSON>, <PERSON>rid, Too<PERSON><PERSON>, Container, <PERSON>, Typo<PERSON>, Box, Theme } from '@mui/material'
import { makeStyles } from '@mui/styles'
import { useSpacing } from '@watt/theme'
import { BusinessBenefitsButton } from './components/BusinessBenefitsButton'

const useStyles = makeStyles((theme: Theme) => ({
  button: {
    color: theme.palette.common.white,
    fontWeight: '600',
  },
  items: {
    justifyContent: 'flex-end',
    fontWeight: '900',
  },
  phoneButton: {
    color: theme.palette.common.white,
    fontWeight: '600',
    fontSize: '1.1rem',
  },
  hoverImage: {
    transition: 'transform 0.3s',
    '&:hover': {
      transform: 'scale(1.075)',
    },
  },
}))

// TODO: Update the links to be in the constants file once the https://watt.co.uk/ is updated.
const Links = [
  { link: `https://watt.co.uk/reviews/`, text: 'Reviews' },
  { link: `https://watt.co.uk/news/`, text: 'News' },
  { link: `https://watt.co.uk/about-watt/`, text: 'About Us' },
  { link: `https://watt.co.uk/contact/`, text: 'Contact' },
]

export const HeaderDesktop = (): JSX.Element => {
  const [pt4, mb3] = useSpacing('pt4', 'mb3')
  const classes = useStyles()

  return (
    <AppBar color="secondary">
      <Typography variant="caption" style={{ position: 'absolute', right: 3 }}>
        {process.env.NEXT_PUBLIC_GITHUB_SHA && <span>SHA: {process.env.NEXT_PUBLIC_GITHUB_SHA}</span>}
      </Typography>
      <Toolbar>
        <Container className={mb3}>
          <Container className={pt4}>
            <Grid container spacing={1} direction="row" display="flex" alignItems="center">
              <Grid item xs={2}>
                <Container>
                  <Link href={process.env.NEXT_PUBLIC_WATT_HOMEPAGE_URL} color="inherit" underline="none">
                    <Box>
                      <img
                        src="/assets/img/watt-logo.svg"
                        alt="Watt.co.uk - Home"
                        height="40px"
                        className={classes.hoverImage}
                      />
                    </Box>
                  </Link>
                </Container>
              </Grid>
              <Grid container item className={classes.items} sm={10} md={10} lg={8}>
                <Grid key="get-a-quote-grid" item>
                  <Container>
                    <Link href="/">
                      <Typography variant="button" className={classes.button}>
                        GET A QUOTE
                      </Typography>
                    </Link>
                  </Container>
                </Grid>
                <Grid key="bus-benefits" item>
                  <Container>
                    <BusinessBenefitsButton />
                  </Container>
                </Grid>
                {Links.map((link) => (
                  <Grid key={link.text} item>
                    <Container>
                      <Link href={link.link}>
                        <Typography variant="button" className={classes.button}>
                          {link.text}
                        </Typography>
                      </Link>
                    </Container>
                  </Grid>
                ))}
                <Grid key="telephone" item>
                  <Container>
                    <Link href="tel:***********">
                      <Typography variant="button" className={classes.phoneButton}>
                        0161 833 8661
                      </Typography>
                    </Link>
                  </Container>
                </Grid>
              </Grid>
            </Grid>
          </Container>
        </Container>
      </Toolbar>
    </AppBar>
  )
}
