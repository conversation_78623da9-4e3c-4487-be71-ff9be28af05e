import { Grid, Typography, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button } from '@mui/material'

export interface ConfirmationPageBoxProps {
  title: string
  body: JSX.Element
  buttonTitle: string
  buttonHref: string
  buttonColor: string
  icon: JSX.Element
  cardClass: string
  classes: any
  insertSlogan: boolean
}

export const ConfirmationPageBox = (props: ConfirmationPageBoxProps): JSX.Element => {
  const { title, body, buttonTitle, buttonHref, buttonColor, icon, cardClass, classes, insertSlogan } = props
  return (
    <Grid item xs={12} md={4}>
      <Grid container direction="row" className={cardClass}>
        <Grid item xs={9}>
          <Typography variant="subtitle2" className={classes.textBlack}>
            {title}{' '}
          </Typography>
          <br />
          <Grid container direction={'row'}>
            <Grid item xs={12} style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography variant="caption" className={classes.textBlack}>
                {body}
              </Typography>
              {insertSlogan && (
                <Typography variant="caption" className={classes.textBlack}>
                  It’s Watt we do!{' '}
                </Typography>
              )}
              <br />
              <Grid container direction={'row'} spacing={15}>
                <Grid item xs={2}>
                  <IconButton className={classes.icon} disabled>
                    {icon}
                  </IconButton>
                </Grid>
                <Grid item xs={9} md={4} lg={10}>
                  <Link href={buttonHref} target="_blank" rel="noreferrer">
                    <Button
                      variant="contained"
                      color="secondary"
                      sx={{ backgroundColor: buttonColor, borderRadius: '2em' }}
                    >
                      {buttonTitle}
                    </Button>
                  </Link>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}
