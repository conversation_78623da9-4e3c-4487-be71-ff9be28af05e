import { Theme } from '@mui/material'
import { makeStyles } from '@mui/styles'

export const useStyles = makeStyles((theme: Theme) => ({
  rootContainer: {
    backgroundColor: theme.palette.secondary.main,
  },
  root: {
    position: 'relative',
    display: 'flex',
    width: '100%',
    height: '100%',
    padding: theme.spacing(2),
  },
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: 0,
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  link: {
    lineHeight: 0,
  },
  drawer: {
    width: 280,
  },
  paper: {
    backgroundColor: theme.palette.secondary.main,
    paddingTop: theme.spacing(4),
  },
  mobilePhone: {
    paddingRight: '1.5em',
    color: 'white',
  },
}))
