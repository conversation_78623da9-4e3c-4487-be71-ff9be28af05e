import { useCallback } from 'react'
import clsx from 'clsx'
import { useIsDesktop } from '@watt/components'
import { QuoteRatesType } from '@watt/constants'
import { useSpacing } from '@watt/theme'
import en from '~/i18n'
import { useStyles } from './quote.rates.styles'

const i18n = en.electricityQuote

type Props = {
  rates: QuoteRatesType
  className?: string
}

export const QuoteRates = ({ rates, className }: Props): JSX.Element => {
  const classes = useStyles()
  const [pb4] = useSpacing('pb4')
  const isDesktop = useIsDesktop()
  const planType = rates.contract_type
  const getLengthWord = useCallback((years: number) => (years > 1 ? 'Years' : 'Year'), [])
  const convertMonthToYear = (months: number) => months / 12

  return (
    <table className={clsx(classes.root, className)}>
      <tbody>
        <tr>
          <th align="left" className={clsx(classes.label, pb4)}>
            {i18n.rates.contractLength}:
          </th>
          <td className={clsx(classes.values, pb4)}>
            {convertMonthToYear(rates.durationMonths)} {getLengthWord(rates.durationMonths)} fixed price
          </td>
        </tr>
        {rates.unit_rate > 0 && (
          <tr>
            <th align="left" className={clsx(classes.label, pb4)}>
              {i18n.rates.unitRate}:
            </th>
            <td className={clsx(classes.values, pb4)}>{parseFloat(rates.unit_rate.toString()).toFixed(5)}p / unit </td>
          </tr>
        )}
        {rates.day_unit_rate > 0 && (
          <tr>
            <th align="left" className={clsx(classes.label, pb4)}>
              {i18n.rates.dayUnitRate}:
            </th>
            <td className={clsx(classes.values, pb4)}>
              {parseFloat(rates.day_unit_rate.toString()).toFixed(5)}p / unit{' '}
            </td>
          </tr>
        )}
        {rates.night_unit_rate > 0 && (
          <tr>
            <th align="left" className={clsx(classes.label, pb4)}>
              {i18n.rates.nightUnitRate}:
            </th>
            <td className={clsx(classes.values, pb4)}>
              {parseFloat(rates.night_unit_rate.toString()).toFixed(5)}p / unit{' '}
            </td>
          </tr>
        )}
        <tr>
          <th align="left" className={clsx(classes.label, pb4)}>
            {i18n.rates.standingCharge}:
          </th>
          <td className={clsx(classes.values, pb4)}>
            {parseFloat(rates.standing_charge.toString()).toFixed(5)}p / day{' '}
          </td>
        </tr>
        {parseFloat(rates.capacity_charge_kva) > 0 && (
          <tr>
            <th align="left" className={clsx(classes.label, pb4)}>
              {i18n.rates.capacityChargeKva}:
            </th>
            <td className={clsx(classes.values, pb4)}>{rates.capacity_charge_kva}</td>
          </tr>
        )}
        <tr>
          <th align="left" className={clsx(classes.label, isDesktop ? null : pb4)}>
            {i18n.rates.planType}:
          </th>
          <td className={clsx(classes.values, isDesktop ? null : pb4)}>{planType}</td>
        </tr>
      </tbody>
    </table>
  )
}
