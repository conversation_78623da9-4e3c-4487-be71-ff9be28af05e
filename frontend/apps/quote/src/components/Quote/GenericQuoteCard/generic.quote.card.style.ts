import { Theme } from '@mui/material'
import { makeStyles } from '@mui/styles'

export const useStyles = makeStyles((theme: Theme) => ({
  borderBottom: {
    // borderBottom: `1px solid ${GREY_SHADES[200]}`,
    [theme.breakpoints.up('lg')]: {
      borderBottom: 'none',
    },
  },
  buttonRoot: {
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(2),
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  buttonLabel: {
    lineHeight: 1,
  },
  marginBottom: {
    marginBottom: theme.spacing(0),
  },
}))
