import { axiosCrm } from '@watt/api-interface'
import type { EntityAddress } from '@watt/constants'
import { cfg } from '~/config/config'

export type VerifyAddressPayload = {
  postcode: string
  county: string
  postalTown: string
  country: string
  displayName: string
  addressLine1: string
  addressLine2: string
  houseName: string
  houseNumber: string
  flatNumber: string
}

/**
 * Verifies a manually-entered address and returns the normalised
 * `EntityAddress` DTO from the BE.
 *
 * @throws `Error` – bubbled up to the thunk / caller
 */
export async function verifyManualAddress(payload: VerifyAddressPayload): Promise<EntityAddress> {
  const url = cfg.api.routes.verifyAddress // e.g. "/api/addresses/verify"
  const { data } = await axiosCrm.post<EntityAddress[]>(url, payload, {
    withCredentials: true,
  })
  return data[0]
}
