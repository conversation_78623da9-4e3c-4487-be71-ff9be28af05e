import * as React from 'react'
import { Story, Meta } from '@storybook/react'
import { IconBoxCircle } from '.'

export default {
  title: 'Quote/Components/IconBoxCircle',
  component: IconBoxCircle,
  argTypes: {
    bgcolor: { defaultValue: '#2341e1', control: 'color' },
    height: { control: 'number' },
    width: { control: 'number' },
    className: { control: 'text' },
  },
} as Meta

const Template: Story<React.ComponentProps<typeof IconBoxCircle>> = (args) => <IconBoxCircle {...args} />

export const EmptyIconBoxCircle = Template.bind({})
