import * as React from 'react'
import clsx from 'clsx'
import { useStyles } from './icon.box.circle.styles'

interface Props {
  children: React.ReactElement<SVGElement>
  bgcolor?: string
  height?: number | string
  width?: number | string
  className?: string
}

export const IconBoxCircle: React.FunctionComponent<Props> = ({
  children,
  height = '4rem',
  width = '4rem',
  className,
}) => {
  const classes = useStyles({ height, width })

  return (
    <div className={clsx(classes.root, className)}>
      <div className={classes.inner} style={{ border: `4px solid #063348`, borderRadius: 50 }} />
      {children}
    </div>
  )
}
