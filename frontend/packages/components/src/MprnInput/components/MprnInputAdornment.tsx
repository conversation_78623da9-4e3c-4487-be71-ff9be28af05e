import { InputAdornment, Typography } from '@mui/material'
import { InfoIconWithTooltip } from '@watt/components'

export const MprnInputAdornment = () => {
  return (
    <InputAdornment position="start">
      <InfoIconWithTooltip
        tooltipProps={{
          disableFocusListener: true,
          title: (
            <>
              <Typography color="inherit">
                You can find your Meter Point Reference Number (MPRN) on your gas bill issued by your supplier.
              </Typography>
              <Typography color="inherit" gutterBottom>
                It has 11 digits and should be printed in the format below
              </Typography>
              <img src="/assets/img/mprn_example.png" width="285px" alt="Meter Point Reference Number" />
            </>
          ),
        }}
      />
    </InputAdornment>
  )
}
