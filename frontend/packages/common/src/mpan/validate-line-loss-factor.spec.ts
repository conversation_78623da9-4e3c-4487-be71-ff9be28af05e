import { validateLineLossFactor } from "./validate-line-loss-factor";

type TestCase = [string, boolean];

describe("validateLineLossFactor", () => {
  it.each<TestCase>([
    ["AHJ", true],
    ["NPZ", true],
    ["123", true],
    ["A1B", true],
    ["IIO", false],
    ["OOO", false],
    ["A!@", false],
    ["AB", false],
    ["ABCD", false],
  ])('should return %s for input "%s"', (input, expected) => {
    expect(validateLineLossFactor(input)).toBe(expected);
  });

  it("should handle non-string inputs", () => {
    expect(validateLineLossFactor(null as unknown as string)).toBe(false);
    expect(validateLineLossFactor(undefined as unknown as string)).toBe(false);
  });
});
