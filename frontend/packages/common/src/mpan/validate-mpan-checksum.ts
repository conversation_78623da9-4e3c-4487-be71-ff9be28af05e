//11 is deliberately missing as-per the rules for the validation algorithm.
const PRIMES = [3, 5, 7, 13, 17, 19, 23, 29, 31, 37, 41, 43] as const

export function validateMpanChecksum(core: string): boolean {
  const skipValidationForSensorMpan = core.includes('*')
  if (skipValidationForSensorMpan) {
    return true
  }

  if (core.length !== 13 || !/^\d+$/.test(core)) {
    return false // Ensure length is 13 and contains only digits
  }
  const coreMinusFinalChecksumDigit = Array.from(core.slice(0, -1), Number)
  const finalChecksumDigit = parseInt(core.slice(-1))

  const sumOfCorrespondingPrimesForEachDigit =
    coreMinusFinalChecksumDigit.reduce((sum, digit, index) => sum + digit * PRIMES[index], 0) % 11
  const checksumToCompare = sumOfCorrespondingPrimesForEachDigit === 10 ? 0 : sumOfCorrespondingPrimesForEachDigit

  return checksumToCompare === finalChecksumDigit
}
