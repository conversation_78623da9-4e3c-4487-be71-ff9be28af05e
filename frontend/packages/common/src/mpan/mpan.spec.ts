import { Invalid<PERSON><PERSON><PERSON><PERSON>r, <PERSON><PERSON><PERSON><PERSON>, MPANShort, parseMpan } from "./mpan"; // Adjust the import path as needed

type TestCase = [string, string, boolean, Partial<MPANShort | MPANLong>];

describe("MPAN Function Tests", () => {
  describe.each<TestCase>([
    [
      "short mpan valid string",
      "1900000268965",
      true,
      {
        core: "1900000268965",
        distributor: "19",
        uniqueIdentifier: "00000268",
        checksum: "965",
      },
    ],
    [
      "long mpan valid string",
      "018010031015680495647",
      true,
      {
        topLine: "01801003",
        profileClass: "01",
        meterTimeSwitchCode: "801",
        lineLossFactorClass: "003",
        core: "1015680495647",
        distributor: "10",
        uniqueIdentifier: "15680495",
        checksum: "647",
      },
    ],
    ["invalid mpan string", "invalid_mpan_string", false, {}],
  ])("Testing %s", (description, input, isValid, expectedParts) => {
    it(`should ${isValid ? "successfully parse" : "throw an error for"} the given input`, () => {
      const result = parseMpan(input);

      if (isValid) {
        expect(result.error).toBeUndefined();
        expect(result.data).toBeDefined();

        if (result.data) {
          Object.entries(expectedParts).forEach(([key, value]) => {
            if (key in result.data) {
              expect({ [key]: value }).toEqual({
                [key]: result.data[key as keyof typeof result.data],
              });
            }
          });
        }
      } else {
        expect(result.data).toBeUndefined();
        expect(result.error).toBeInstanceOf(InvalidMPANError);
      }
    });
  });
});
