import { z, ZodIssue } from 'zod'
import { Response } from '../types/response'
import { validateDistributor } from './validate-distributor'
import { validateLineLossFactor } from './validate-line-loss-factor'
import { validateMpanChecksum } from './validate-mpan-checksum'
import { validateMtcRange } from './validate-mtc-range'
import { validateProfileClass } from './validate-profile-class'

export class InvalidMPANError extends Error {
  private errors: { error: string; type: string }[]

  constructor(message: string, errors: { error: string; type: string }[] = []) {
    super(message)
    this.name = 'InvalidMPANError'
    this.errors = errors
  }

  getErrors() {
    return this.errors
  }
}

const MPAN_INPUTS_LENGTHS = {
  profileClass: 2,
  meterTimeSwitchCode: 3,
  lineLossFactor: 3,
  distributorId: 2,
  meterPointIdNumber: 4,
  meterPointIdNumberPartTwo: 4,
  checkSum: 3,
  unquieIdentifier: 8,
  topLine: 8,
  core: 13,
} as const

const MPANShort = z.object({
  core: z
    .string()
    .length(MPAN_INPUTS_LENGTHS.core, 'Core must be 13 characters')
    .refine((core) => validateMpanChecksum(core), {
      message: 'Invalid MPAN checksum',
    }),
  distributor: z
    .string()
    .length(MPAN_INPUTS_LENGTHS.distributorId, 'Distribution ID must be 2 characters')
    .refine(validateDistributor, {
      message: 'Invalid Distributor ID',
    }),
  uniqueIdentifier: z.string().length(MPAN_INPUTS_LENGTHS.unquieIdentifier, 'Unique Identifier must be 8 characters'),
  checksum: z.string().length(MPAN_INPUTS_LENGTHS.checkSum, 'Checksum must be 3 characters'),
})

const MPANLong = MPANShort.extend({
  topLine: z.string().length(MPAN_INPUTS_LENGTHS.topLine, 'Top Line must be 8 characters'),
  profileClass: z
    .string()
    .length(MPAN_INPUTS_LENGTHS.profileClass, 'Profile Class must be 2 characters')
    .refine(validateProfileClass, {
      message: 'Profile class must be 00 - 08',
    }),
  meterTimeSwitchCode: z
    .string()
    .length(MPAN_INPUTS_LENGTHS.meterTimeSwitchCode, 'Meter Time Switch Code must be 3 characters')
    .refine((meterTimeSwitchCode) => validateMtcRange(meterTimeSwitchCode), {
      message: 'Invalid Meter Time Switch Code',
    }),
  lineLossFactorClass: z
    .string()
    .length(MPAN_INPUTS_LENGTHS.lineLossFactor, 'Line Loss Factor must be 3 characters')
    .refine(validateLineLossFactor, {
      message: 'Invalid Line Loss Factor',
    }),
})

export type MPANShort = z.infer<typeof MPANShort>
export type MPANLong = z.infer<typeof MPANLong>

const MPAN_REGEX_SHORT = /^(\d{2}|\*{2})(\d{8}|\*{8})(\d{2})(\d)$/
const MPAN_REGEX_LONG = /^(\d{2})(\d{3})([A-Z0-9]{3})((\d{2}|\*{2})(\d{8}|\*{8})(\d{2})(\d))$/

function isShortMpan(rawMpan: string) {
  return MPAN_REGEX_SHORT.test(rawMpan)
}

function isLongMpan(rawMpan: string) {
  return MPAN_REGEX_LONG.test(rawMpan)
}

function parseShortMpan(rawMpan: string): Response<MPANShort, InvalidMPANError> {
  const match = rawMpan.match(MPAN_REGEX_SHORT)
  if (!match) {
    return {
      data: undefined,
      error: new InvalidMPANError('Invalid Short MPAN format'),
    }
  }

  const safeParse = MPANShort.safeParse({
    core: match[0],
    distributor: match[1],
    uniqueIdentifier: match[2],
    checksum: match[3] + match[4],
  })

  if (!safeParse.success) {
    return {
      data: undefined,
      error: new InvalidMPANError(
        'Invalid Long MPAN format',
        safeParse.error.issues.map((issue: ZodIssue) => ({
          error: issue.message,
          type: issue.code,
        }))
      ),
    }
  }

  return {
    data: safeParse.data,
    error: undefined,
  }
}

function parseLongMpan(raw_string: string): Response<MPANLong, InvalidMPANError> {
  const match = raw_string.match(MPAN_REGEX_LONG)

  console.log({ match })

  if (!match) {
    return {
      data: undefined,
      error: new InvalidMPANError('Invalid Long MPAN format'),
    }
  }

  const safeParse = MPANLong.safeParse({
    topLine: match[1] + match[2] + match[3],
    profileClass: match[1],
    meterTimeSwitchCode: match[2],
    lineLossFactorClass: match[3],
    core: match[4],
    distributor: match[5],
    uniqueIdentifier: match[6],
    checksum: match[7] + match[8],
  })

  if (!safeParse.success) {
    return {
      data: undefined,
      error: new InvalidMPANError(
        'Invalid Long MPAN format',
        safeParse.error.issues.map((issue: ZodIssue) => ({
          error: issue.message,
          type: issue.code,
        }))
      ),
    }
  }

  return {
    data: safeParse.data,
    error: undefined,
  }
}

export function parseMpan(rawMpan: string): Response<MPANShort | MPANLong, InvalidMPANError> {
  try {
    console.log({ rawMpan })
    if (isShortMpan(rawMpan)) {
      return parseShortMpan(rawMpan)
    }

    if (isLongMpan(rawMpan)) {
      return parseLongMpan(rawMpan)
    }

    return {
      data: undefined,
      error: new InvalidMPANError('Invalid MPAN format'),
    }
  } catch (e) {
    const error = e as InvalidMPANError
    return {
      data: undefined,
      error,
    }
  }
}
