import { validateMpanChecksum } from "./validate-mpan-checksum";

type TestCase = [string, boolean];

describe("validateMpanChecksum", () => {
  it.each<TestCase>([
    ["1015680495647", true], // Valid MPAN
    ["1111111111111", false], // Invalid MPAN
    ["1200067438294", false], // Invalid checksum
    ["1234567890", false], // Incorrect length
    ["12A006743Z295", false], // Non-numeric characters
    [" 1015680495647 ", false], // Leading/trailing whitespace
    ["10156804956470", false], // Mispositioned checksum digit
    ["9999999999198", false], // Correct checksum but invalid format
  ])("given MPAN '%s', returns %s", (mpan, expected) => {
    expect(validateMpanChecksum(mpan)).toBe(expected);
  });
});
