import profileClassDescriptions from "../constants/profile-class-descriptions.json";
import { validateProfileClass } from "./validate-profile-class";

describe("validateProfileClass", () => {
  // Test each key (Profile Class) in the profile class descriptions object
  Object.keys(profileClassDescriptions).forEach(profileClass => {
    it(`should return true for a valid profile class: ${profileClass}`, () => {
      expect(validateProfileClass(profileClass)).toBe(true);
    });
  });

  it("should return false for an invalid profile class", () => {
    const invalidProfileClass = "09"; // Assuming '09' is not a valid profile class.
    expect(validateProfileClass(invalidProfileClass)).toBe(false);
  });
});
