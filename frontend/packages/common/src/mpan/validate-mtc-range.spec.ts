import { MTC_RANGES, validateMtcRange } from "./validate-mtc-range";

describe("validateMtcRange", () => {
  it.each(MTC_RANGES)("should return true for an MTC within the range %p", range => {
    // Use the middle value of the range for testing to be inside the range
    const testValue = Math.floor((range.upper + range.lower) / 2).toString();
    expect(validateMtcRange(testValue)).toBe(true);
  });

  it("should return false for an MTC outside of valid ranges", () => {
    const invalidMTC = "1234"; // Outside the 000-999 range used in validateMtcRange
    expect(validateMtcRange(invalidMTC)).toBe(false);
  });

  it("should return false for non-numeric MTC codes", () => {
    const nonNumericMTC = "ABC";
    expect(validateMtcRange(nonNumericMTC)).toBe(false);
  });
});
