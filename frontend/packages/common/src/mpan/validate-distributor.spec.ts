import distributors from "../constants/distribution-network-operators.json";
import { validateDistributor } from "./validate-distributor";

type DistributorKey = keyof (typeof distributors)["DNOS"] | keyof (typeof distributors)["IDNOS"];
type TestCase = [Distributor<PERSON>ey, boolean];

const validDnoIds = Object.keys(distributors.DNOS) as Array<keyof (typeof distributors)["DNOS"]>;
const validIdnoIds = Object.keys(distributors.IDNOS) as Array<keyof (typeof distributors)["IDNOS"]>;
const invalidId = "99";

describe("validateDistributor", () => {
  describe.each(validDnoIds.map(id => [id, true] as TestCase))(
    "with DNO Distributor ID: %s",
    (id, expected) => {
      it(`should return ${expected} for DNO distributor ID`, () => {
        expect(validateDistributor(id)).toBe(expected);
      });
    }
  );

  describe.each(validIdnoIds.map(id => [id, true] as TestCase))(
    "with IDNO Distributor ID: %s",
    (id, expected) => {
      it(`should return ${expected} for IDNO distributor ID`, () => {
        expect(validateDistributor(id)).toBe(expected);
      });
    }
  );

  describe.each([[invalidId as DistributorKey, false]] as TestCase[])(
    "with invalid Distributor ID: %s",
    (id, expected) => {
      it(`should return ${expected} for invalid distributor ID`, () => {
        expect(validateDistributor(id)).toBe(expected);
      });
    }
  );
});
