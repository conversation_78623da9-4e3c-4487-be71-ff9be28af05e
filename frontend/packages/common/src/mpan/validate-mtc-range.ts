type MTCRange = {
  upper: number;
  lower: number;
  description: string;
};

export const MTC_RANGES: MTCRange[] = [
  { upper: 1, lower: 399, description: "DNO specific" },
  { upper: 400, lower: 499, description: "Reserved" },
  {
    upper: 500,
    lower: 509,
    description: "Codes for related Metering Systems – common across the Industry",
  },
  { upper: 510, lower: 799, description: "Codes for related Metering Systems – DNO specific" },
  { upper: 800, lower: 999, description: "Codes common across the Industry" },
];

export function validateMtcRange(mtc: string): boolean {
  const mtcValue = parseInt(mtc, 10);
  if (isNaN(mtcValue) || !(mtcValue > 0 && mtcValue < 1000)) {
    return false;
  }
  return MTC_RANGES.some(range => mtcValue >= range.upper && mtcValue <= range.lower);
}
