# It only ignores the files in the root .gitignore file
# Add patterns to ignore here, one per line
# Example:
# *.log
# tmp/

node_modules/
.serverless/
.webpack/
.invoke-output/
.DS_Store

.yarn/install-state.gz
.yarn/*
!.yarn/releases
!.yarn/versions
yarn-error.log

!.env
!.env.*
output/
notebooks/data/


.env
!.env.example
node_modules
tmp
downloads
.DS_Store
dist
screenshots


.env*
!.env.example
unsubscribers.json
output.csv


# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars
*.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*
.idea


# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# next.js
.next/
out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# Yarn
.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

### VisualStudioCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

### VisualStudioCode Patch ###
# Ignore all local history of files
.history
.ionide

### Windows ###
# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

# Storybook static build
storybook-static/

**/cypress/screenshots/*
**/cypress/videos/*

.lighthouseci


notebooks/
ssl/
*.tar.gz
fixtures/
dist/
.DS_Store
*.tsbuildinfo
infracost-usage.yml
*.drawio
*.svg
e2e/
newrelic.js
.serverless/
*.hbs
providers.ts
.github/
terraform/
wordpress/
marketing/
cypress/
newrelic.ini
local-helpers/
alembic/
.flake8
.pylintrc
alembic.ini
Dockerfile
Pipfile
pyproject.toml
pyrightconfig.json
pytest.ini
scripts/
__init__.py
.gitkeep
tests/
