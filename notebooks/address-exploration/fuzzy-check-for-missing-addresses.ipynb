{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fuzzy match with caching for addresses\n", "The below script is a fuzzy match checking addresses against the CRM DB. It also checks for MPXN matches.\n", "\n", "1. Read in the CSV file and extract the addresses.\n", "2. Use the post codes from the CSV file to query the database for the addresses.\n", "3. Performs queries on the CRM applications PRODUCTION database in batches to avoid timeouts.\n", "4. Gets the MPXNs from the CSV file and checks if they exist in the database.\n", "5. <PERSON><PERSON><PERSON> MPXNs in Redis for 31 days. (to speed up re-runs)\n", "6. <PERSON>ache the addresses in Redis for 31 days. (to speed up re-runs)\n", "7. Use the Fuse library to fuzzy match the addresses.\n", "8. Save the results to a JSONL file with a fuzzy match cut off of 0.3.\n", "9. Genrates a statistics json file with the following breakdown:\n", "    - Exact address match with MPXN match\n", "    - Fuzzy address match with MPXN match\n", "    - Fuzzy address match with no MPXN in either CSV or DB\n", "    - Fuzzy address match with MPXN mismatch (one exists but they don't match)\n", "10. The closer the score is to ZERO the better!\n", "\n", "\n", "The output is the JSONL file with the fuzzy match and perfect MPXN match data and the JSON statistics file. The statistics file is used to produce plots to explore the data distribution and the overlap between the addresses.\n", "\n", "Subsequent analysis is performed in `plots-for-fuzzy-check-for-missing-addresses.ipynb`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["////////////////////////////////////////////////////////////////\n", "// 1) All Imports and Setup\n", "////////////////////////////////////////////////////////////////\n", "import { <PERSON>ys<PERSON>, PostgresDialect, Generated } from \"npm:kysely\";\n", "import Pool from \"npm:pg-pool\";\n", "import * as dotenv from \"npm:dotenv\";\n", "import { readFile, mkdir, appendFile, writeFile } from \"node:fs/promises\";\n", "import { parse } from \"npm:csv-parse/sync\";\n", "import Fuse from \"npm:fuse.js\";\n", "import { createHash } from \"node:crypto\";\n", "import { createClient } from \"npm:redis\";\n", "\n", "dotenv.config({ path: \"../.env\" });\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 2) Constants & Interfaces\n", "////////////////////////////////////////////////////////////////\n", "\n", "const CSV_FILE = \"bgas_10_03_2025_marketing_campaign.csv\";\n", "const EXPECTED_HEADERS = [\n", "  \"email\",\n", "  \"company.type\",\n", "  \"business_name\",\n", "  \"company.registrationNumber\",\n", "  \"company.charityNumber\",\n", "  \"company.address1\",\n", "  \"company.address2\",\n", "  \"company.address3\",\n", "  \"company.town\",\n", "  \"company.county\",\n", "  \"company.sitePostcode\",\n", "  \"Mpxn\",\n", "  \"contact.businessPhoneNumber\",\n", "  \"contact.contactForename\",\n", "  \"contact.contactSurname\",\n", "  \"contact.position\",\n", "  \"hasElectricity\",\n", "  \"hasGas\",\n", "  \"current_supplier\",\n", "];\n", "const FUSE_THRESHOLD = 0.3;\n", "\n", "interface EntityAddress {\n", "  id: Generated<string>;\n", "  postcode: string;\n", "  county: string | null;\n", "  postalTown: string | null;\n", "  addressLine1: string | null;\n", "  addressLine2: string | null;\n", "  houseName: string | null;\n", "  houseNumber: string | null;\n", "  flatNumber: string | null;\n", "  fullAddress?: string;\n", "  mpxn?: string;\n", "}\n", "\n", "interface Database {\n", "  entity_address: <PERSON><PERSON><PERSON><PERSON>dd<PERSON>;\n", "  mpans: {\n", "    id: string;\n", "    value: string;\n", "    entityAddressId: string;\n", "  };\n", "  mprns: {\n", "    id: string;\n", "    value: string;\n", "    entityAddressId: string;\n", "  };\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 3) Database & Redis Connections\n", "////////////////////////////////////////////////////////////////\n", "\n", "const connectionString = process.env.DIRECT_URL;\n", "if (!connectionString) {\n", "  throw new Error(\"DIRECT_URL not found in environment variables\");\n", "}\n", "\n", "const pool = new Pool({\n", "  connectionString,\n", "  max: 10,\n", "  connectionTimeoutMillis: 10000,\n", "  idleTimeoutMillis: 30000,\n", "  statement_timeout: 30000,\n", "});\n", "\n", "const db = new Kysely<Database>({\n", "  dialect: new PostgresDialect({ pool }),\n", "});\n", "\n", "const redisUrl = process.env.REDIS_URL || \"redis://localhost:6379\";\n", "const redisClient = createClient({ url: redisUrl });\n", "redisClient.on(\"error\", (err) => console.error(\"Redis Client Error\", err));\n", "await redisClient.connect();\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 4) Helper Functions\n", "////////////////////////////////////////////////////////////////\n", "\n", "async function retryAsync<T>(fn: () => Promise<T>, attempts = 3, delayMs = 2000): Promise<T> {\n", "  let lastError: any;\n", "  for (let i = 0; i < attempts; i++) {\n", "    try {\n", "      return await fn();\n", "    } catch (error) {\n", "      lastError = error;\n", "      console.error(`Attempt ${i + 1} failed. Retrying in ${delayMs} ms...`, error);\n", "      await new Promise((r) => setTimeout(r, delayMs));\n", "    }\n", "  }\n", "  throw lastError;\n", "}\n", "\n", "function batchArray<T>(array: T[], batchSize: number): T[][] {\n", "  const batches: T[][] = [];\n", "  for (let i = 0; i < array.length; i += batchSize) {\n", "    batches.push(array.slice(i, i + batchSize));\n", "  }\n", "  return batches;\n", "}\n", "\n", "function computeCacheKey(values: string[]): string {\n", "  const sorted = [...values].sort();\n", "  const hash = createHash(\"sha256\").update(sorted.join(\",\")).digest(\"hex\");\n", "  return hash;\n", "}\n", "\n", "async function getCSVRecords(filePath: string, expectedHeaders: string[]): Promise<any[]> {\n", "  const data = await readFile(filePath, \"utf-8\");\n", "  const records = parse(data, {\n", "    columns: true,\n", "    skip_empty_lines: true,\n", "  });\n", "  const actualHeaders = Object.keys(records[0] || {});\n", "  const missingHeaders = expectedHeaders.filter((h) => !actualHeaders.includes(h));\n", "  if (missingHeaders.length > 0) {\n", "    throw new Error(`CSV file is missing expected headers: ${missingHeaders.join(\", \")}`);\n", "  }\n", "  return records;\n", "}\n", "\n", "async function queryAndCachePostcodes(postcodes: string[]): Promise<EntityAddress[]> {\n", "  const cacheKey = `entity_addresses_${computeCacheKey(postcodes)}`;\n", "  const cachedData = await redisClient.get(cacheKey);\n", "  if (cachedData) {\n", "    console.log(`Loaded cached addresses from Redis for key ${cacheKey}`);\n", "    return JSON.parse(cachedData);\n", "  }\n", "\n", "  const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 100;\n", "  const batches = batchArray(postcodes, batchSize);\n", "  let results: En<PERSON><PERSON><PERSON>dd<PERSON>[] = [];\n", "\n", "  for (const batch of batches) {\n", "    const batchResult = await retryAsync(() =>\n", "      db\n", "        .selectFrom(\"entity_address\")\n", "        .select([\n", "          \"id\",\n", "          \"postcode\",\n", "          \"county\",\n", "          \"postalTown\",\n", "          \"addressLine1\",\n", "          \"addressLine2\",\n", "          \"houseName\",\n", "          \"houseNumber\",\n", "          \"flatNumber\",\n", "        ])\n", "        .where(\"postcode\", \"in\", batch)\n", "        .execute()\n", "    );\n", "    results = results.concat(batchResult);\n", "    console.log(`Processed DB batch: ${batch.join(\", \")}`);\n", "  }\n", "\n", "  await redisClient.set(cacheKey, JSON.stringify(results), {\n", "    EX: 60 * 60 * 24 * 31, // 31 days\n", "  });\n", "  console.log(`Cached addresses in Redis with key ${cacheKey}`);\n", "  return results;\n", "}\n", "\n", "async function queryAndCacheMpxns(mpxns: string[]): Promise<Record<string, any>> {\n", "  const uniqueMpxns = Array.from(new Set(mpxns));\n", "  const cacheKey = `entity_mpxns_${computeCacheKey(uniqueMpxns)}`;\n", "  const cachedData = await redisClient.get(cacheKey);\n", "  if (cachedData) {\n", "    console.log(`Loaded cached mpxn records from Redis for key ${cacheKey}`);\n", "    return JSON.parse(cachedData);\n", "  }\n", "\n", "  const mpansValues = uniqueMpxns.filter((val) => val.length === 13);\n", "  const mprnsValues = uniqueMpxns.filter((val) => val.length !== 13);\n", "\n", "  let results: Record<string, any> = {};\n", "\n", "  if (mpansValues.length > 0) {\n", "    const mpansRecords = await retryAsync(() =>\n", "      db\n", "        .selectFrom(\"mpans\")\n", "        .select([\"id\", \"value\", \"entityAddressId\"])\n", "        .where(\"value\", \"in\", mpansValues)\n", "        .execute()\n", "    );\n", "    mpansRecords.forEach((rec) => {\n", "      results[rec.value] = rec;\n", "    });\n", "  }\n", "\n", "  if (mprnsValues.length > 0) {\n", "    const mprnsRecords = await retryAsync(() =>\n", "      db\n", "        .selectFrom(\"mprns\")\n", "        .select([\"id\", \"value\", \"entityAddressId\"])\n", "        .where(\"value\", \"in\", mprnsValues)\n", "        .execute()\n", "    );\n", "    mprnsRecords.forEach((rec) => {\n", "      results[rec.value] = rec;\n", "    });\n", "  }\n", "\n", "  await redisClient.set(cacheKey, JSON.stringify(results), {\n", "    EX: 60 * 60 * 24 * 31, // 31 days\n", "  });\n", "  console.log(`Cached mpxn records in Redis with key ${cacheKey}`);\n", "  return results;\n", "}\n", "\n", "async function checkAddresses(\n", "  csvBatch: any[],\n", "  dbAddresses: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[],\n", "  dbMpxns: Record<string, any>\n", "): Promise<{ csvRecord: any; dbAddress: EntityAddress; fuseScore: number }[]> {\n", "  const fuseOptions = {\n", "    includeScore: true,\n", "    keys: [\"fullAddress\"],\n", "    threshold: FUSE_THRESHOLD,\n", "  };\n", "\n", "  const dbAddressesWithFull = dbAddresses.map((addr) => {\n", "    const parts = [\n", "      addr.house<PERSON><PERSON>,\n", "      addr.house<PERSON><PERSON><PERSON>,\n", "      addr.flat<PERSON><PERSON>,\n", "      addr.addressLine1,\n", "      addr.addressLine2,\n", "      addr.postalTown,\n", "      addr.county,\n", "      addr.postcode,\n", "    ].filter(Boolean);\n", "    return { ...addr, fullAddress: parts.join(\" \") };\n", "  });\n", "\n", "  const fuse = new Fuse(dbAddressesWithFull, fuseOptions);\n", "  const matches: { csvRecord: any; dbAddress: <PERSON><PERSON><PERSON>Address; fuseScore: number }[] = [];\n", "\n", "  for (const record of csvBatch) {\n", "    const csvParts = [\n", "      record[\"company.address1\"],\n", "      record[\"company.address2\"],\n", "      record[\"company.address3\"],\n", "      record[\"company.town\"],\n", "      record[\"company.county\"],\n", "      record[\"company.sitePostcode\"],\n", "    ].filter(Boolean);\n", "\n", "    const csvFullAddress = csvParts.join(\" \");\n", "    const results = fuse.search(csvFullAddress);\n", "    if (results.length > 0) {\n", "      const bestMatch = results[0];\n", "      if (bestMatch.score !== undefined && bestMatch.score <= FUSE_THRESHOLD) {\n", "        const mpxnValue = record[\"Mpxn\"];\n", "        if (mpxnValue && dbMpxns[mpxnValue]) {\n", "          bestMatch.item = { ...bestMatch.item, mpxn: dbMpxns[mpxnValue].value };\n", "        }\n", "        matches.push({\n", "          csvRecord: record,\n", "          dbAddress: bestMatch.item,\n", "          fuseScore: bestMatch.score,\n", "        });\n", "      }\n", "    }\n", "  }\n", "  return matches;\n", "}\n", "\n", "/**\n", " * Computes direct matching statistics by checking which unique MPXNs from the CSV exist in the database.\n", " */\n", "async function getDirectMatchingStats(allMpxns: string[]): Promise<{\n", "  mpans: { found: number; total: number; percentage: string };\n", "  mprns: { found: number; total: number; percentage: string };\n", "  mpxns: { found: number; total: number; percentage: string };\n", "}> {\n", "  const uniqueMpxns = Array.from(new Set(allMpxns));\n", "  const mpansCSV = uniqueMpxns.filter((val) => val.length === 13);\n", "  const mprnsCSV = uniqueMpxns.filter((val) => val.length !== 13);\n", "  const totalMpans = mpansCSV.length;\n", "  const totalMprns = mprnsCSV.length;\n", "  const totalMpxns = uniqueMpxns.length;\n", "\n", "  let mpanFound = 0;\n", "  let mprnFound = 0;\n", "\n", "  const batchSize = 1000; // Adjust as needed for performance\n", "\n", "  // Process MPANs in batches\n", "  const mpanBatches = batchArray(mpansCSV, batchSize);\n", "  for (const batch of mpanBatches) {\n", "    const found = await db\n", "      .selectFrom(\"mpans\")\n", "      .select(\"value\")\n", "      .where(\"value\", \"in\", batch)\n", "      .execute();\n", "    mpanFound += found.length;\n", "  }\n", "\n", "  // Process MPRNs in batches\n", "  const mprnBatches = batchArray(mprnsCSV, batchSize);\n", "  for (const batch of mprnBatches) {\n", "    const found = await db\n", "      .selectFrom(\"mprns\")\n", "      .select(\"value\")\n", "      .where(\"value\", \"in\", batch)\n", "      .execute();\n", "    mprnFound += found.length;\n", "  }\n", "\n", "  const mpanPercentage = totalMpans > 0 ? (mpanFound / totalMpans * 100).toFixed(2) + \"%\" : \"0.00%\";\n", "  const mprnPercentage = totalMprns > 0 ? (mprnFound / totalMprns * 100).toFixed(2) + \"%\" : \"0.00%\";\n", "  const mpxnsFound = mpanFound + mprnFound;\n", "  const mpxnsPercentage = totalMpxns > 0 ? (mpxnsFound / totalMpxns * 100).toFixed(2) + \"%\" : \"0.00%\";\n", "\n", "  return {\n", "    mpans: { found: mpanFound, total: totalMpans, percentage: mpanPercentage },\n", "    mprns: { found: mprnFound, total: totalMprns, percentage: mprnPercentage },\n", "    mpxns: { found: mpxnsFound, total: totalMpxns, percentage: mpxnsPercentage },\n", "  };\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 5) Main Pipeline\n", "////////////////////////////////////////////////////////////////\n", "\n", "async function mainPipeline(allCsvRecords: any[]): Promise<void> {\n", "  await mkdir(\"output\", { recursive: true });\n", "  await mkdir(\"cache\", { recursive: true });\n", "\n", "  const totalRecords = allCsvRecords.length;\n", "  console.log(`Total CSV records: ${totalRecords}`);\n", "\n", "  let startIndex = 0;\n", "  try {\n", "    const progressData = await readFile(\"output/progress.json\", \"utf-8\");\n", "    const progress = JSON.parse(progressData);\n", "    startIndex = progress.lastIndex || 0;\n", "  } catch (_e) {\n", "    startIndex = 0;\n", "  }\n", "  console.log(`Resuming from record index: ${startIndex}`);\n", "\n", "  if (startIndex === totalRecords) {\n", "    const response = prompt(\n", "      \"All records have been processed previously. Do you want to re-run the pipeline? (Y/N)\"\n", "    );\n", "    if (response && response.toLowerCase() === \"y\") {\n", "      console.log(\"Re-running the pipeline from the beginning.\");\n", "      startIndex = 0;\n", "    } else {\n", "      console.log(\"Skipping re-run and proceeding to generate final output.\");\n", "      return;\n", "    }\n", "  }\n", "\n", "  const csvBatchSize = process.env.CSV_BATCH_SIZE ? parseInt(process.env.CSV_BATCH_SIZE) : 100;\n", "  const csvBatches = batchArray(allCsvRecords.slice(startIndex), csvBatchSize);\n", "\n", "  for (let i = 0; i < csvBatches.length; i++) {\n", "    const batchIndex = startIndex + i * csvBatchSize;\n", "    const csvBatch = csvBatches[i];\n", "    console.log(`Processing CSV batch starting at index: ${batchIndex}`);\n", "\n", "    const batchPostcodes = Array.from(\n", "      new Set(csvBatch.map((rec) => rec[\"company.sitePostcode\"]))\n", "    );\n", "    const dbAddresses = await queryAndCachePostcodes(batchPostcodes);\n", "\n", "    const batchMpxns = Array.from(new Set(csvBatch.map((rec) => rec[\"Mpxn\"]).filter(Boolean)));\n", "    const dbMpxns = await queryAndCacheMpxns(batchMpxns);\n", "\n", "    const batchMatches = await checkAddresses(csvBatch, dbAddresses, dbMpxns);\n", "\n", "    for (const match of batchMatches) {\n", "      match.evaluatedAt = new Date().toISOString();\n", "      await appendFile(\"output/matches.jsonl\", JSON.stringify(match) + \"\\n\", \"utf-8\");\n", "    }\n", "    console.log(`Batch processed, found ${batchMatches.length} matches.`);\n", "\n", "    const newIndex = batchIndex + csvBatch.length;\n", "    await writeFile(\n", "      \"output/progress.json\",\n", "      JSON.stringify({ lastIndex: newIndex }, null, 2),\n", "      \"utf-8\"\n", "    );\n", "    console.log(`Progress updated: ${newIndex} records processed.`);\n", "  }\n", "  console.log(\"All batches processed. Job complete.\");\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 6) Statistics Generation Function\n", "////////////////////////////////////////////////////////////////\n", "\n", "async function generateStatistics(\n", "  directMatchingStats: {\n", "    mpans: { found: number; total: number; percentage: string };\n", "    mprns: { found: number; total: number; percentage: string };\n", "    mpxns: { found: number; total: number; percentage: string };\n", "  }\n", "): Promise<void> {\n", "  const allCsvRecords = await getCSVRecords(`data/${CSV_FILE}`, EXPECTED_HEADERS);\n", "  const totalCSVRecords = allCsvRecords.length;\n", "\n", "  let matches: any[] = [];\n", "  try {\n", "    const jsonlData = await readFile(\"output/matches.jsonl\", \"utf8\");\n", "    const lines = jsonlData.split(\"\\n\").filter((line) => line.trim() !== \"\");\n", "    matches = lines.map((line) => JSON.parse(line));\n", "  } catch (err) {\n", "    console.error(\"Failed to read matches.jsonl or parse data:\", err);\n", "  }\n", "  const totalMatches = matches.length;\n", "  const missingMatches = totalCSVRecords - totalMatches;\n", "\n", "  // Calculate totals for fuzzy matching categories\n", "  const totalMpxnsInCsv = allCsvRecords.filter((rec) => rec[\"Mpxn\"] && rec[\"Mpxn\"].trim() !== \"\").length;\n", "  const totalNoMpxnInCsv = totalCSVRecords - totalMpxnsInCsv;\n", "\n", "  // Initialize counters for fuzzy matching breakdown\n", "  let addressExactAndMpxnMatch = { mpans: 0, mprns: 0, mpxns: 0 };\n", "  let addressFuzzyAndMpxnMatch = { mpans: 0, mprns: 0, mpxns: 0 };\n", "  let addressFuzzyNoMpxnFound = 0;\n", "  let addressFuzzyMpxnMismatchFound = 0;\n", "  const fuseScores: number[] = [];\n", "\n", "  // Process each match to categorize it\n", "  for (const match of matches) {\n", "    const csvMpxn = match.csvRecord[\"Mpxn\"]?.trim();\n", "    const dbMpxn = match.dbAddress.mpxn?.trim();\n", "    const fuseScore = match.fuseScore;\n", "    fuseScores.push(fuseScore);\n", "\n", "    if (csvMpxn && dbMpxn && csvMpxn === dbMpxn) {\n", "      const isMpan = csvMpxn.length === 13; // MPANs are 13 digits, MPRNs are not\n", "      if (fuseScore === 0) {\n", "        // Exact address match with MPXN match\n", "        if (isMpan) {\n", "          addressExactAndMpxnMatch.mpans++;\n", "        } else {\n", "          addressExactAndMpxnMatch.mprns++;\n", "        }\n", "      } else {\n", "        // Fuzzy address match with MPXN match\n", "        if (isMpan) {\n", "          addressFuzzyAndMpxnMatch.mpans++;\n", "        } else {\n", "          addressFuzzyAndMpxnMatch.mprns++;\n", "        }\n", "      }\n", "    } else if (!csvMpxn && !dbMpxn) {\n", "      // Fuzzy address match with no MPXN in either CSV or DB\n", "      addressFuzzyNoMpxnFound++;\n", "    } else {\n", "      // Fuzzy address match with MPXN mismatch (one exists but they don't match)\n", "      addressFuzzyMpxnMismatchFound++;\n", "    }\n", "  }\n", "\n", "  // Calculate total MPXNs for exact and fuzzy matches\n", "  addressExactAndMpxnMatch.mpxns = addressExactAndMpxnMatch.mpans + addressExactAndMpxnMatch.mprns;\n", "  addressFuzzyAndMpxnMatch.mpxns = addressFuzzyAndMpxnMatch.mpans + addressFuzzyAndMpxnMatch.mprns;\n", "\n", "  // Use directMatchingStats totals for addressFuzzyAndMpxnMatch\n", "  const totalMpansCsv = directMatchingStats.mpans.total;\n", "  const totalMprnsCsv = directMatchingStats.mprns.total;\n", "  const totalMpxnsCsv = directMatchingStats.mpxns.total;\n", "\n", "  // Calculate percentages for addressFuzzyAndMpxnMatch\n", "  const fuzzyMpansPercentage = totalMpansCsv > 0\n", "    ? (addressFuzzyAndMpxnMatch.mpans / totalMpansCsv * 100).toFixed(2) + \"%\"\n", "    : \"0.00%\";\n", "  const fuzzyMprnsPercentage = totalMprnsCsv > 0\n", "    ? (addressFuzzyAndMpxnMatch.mprns / totalMprnsCsv * 100).toFixed(2) + \"%\"\n", "    : \"0.00%\";\n", "  const fuzzyMpxnsPercentage = totalMpxnsCsv > 0\n", "    ? (addressFuzzyAndMpxnMatch.mpxns / totalMpxnsCsv * 100).toFixed(2) + \"%\"\n", "    : \"0.00%\";\n", "\n", "  // Calculate percentages for addressFuzzyNoMpxn\n", "  const fuzzyNoMpxnPercentage = totalNoMpxnInCsv > 0\n", "    ? (addressFuzzyNoMpxnFound / totalNoMpxnInCsv * 100).toFixed(2) + \"%\"\n", "    : \"0.00%\";\n", "\n", "  // Calculate percentages for addressFuzzyMpxnMismatch\n", "  const fuzzyMpxnMismatchPercentage = totalMpxnsInCsv > 0\n", "    ? (addressFuzzyMpxnMismatchFound / totalMpxnsInCsv * 100).toFixed(2) + \"%\"\n", "    : \"0.00%\";\n", "\n", "  // Calculate fuse score statistics\n", "  const avgFuseScore = fuseScores.length > 0\n", "    ? fuseScores.reduce((a, b) => a + b, 0) / fuseScores.length\n", "    : 0;\n", "  const minFuseScore = fuseScores.length > 0 ? Math.min(...fuseScores) : 0;\n", "  const maxFuseScore = fuseScores.length > 0 ? Math.max(...fuseScores) : 0;\n", "\n", "  // Construct the statistics object with detailed breakdowns\n", "  const stats = {\n", "    directMatching: directMatchingStats,\n", "    fuzzyMatching: {\n", "      breakdown: {\n", "        addressExactAndMpxnMatch, // Keeping as counts for exact matches\n", "        addressFuzzyAndMpxnMatch: {\n", "          mpans: {\n", "            found: addressFuzzyAndMpxnMatch.mpans,\n", "            total: totalMpansCsv,\n", "            percentage: fuzzyMpansPercentage,\n", "          },\n", "          mprns: {\n", "            found: addressFuzzyAndMpxnMatch.mprns,\n", "            total: totalMprnsCsv,\n", "            percentage: fuzzyMprnsPercentage,\n", "          },\n", "          mpxns: {\n", "            found: addressFuzzyAndMpxnMatch.mpxns,\n", "            total: totalMpxnsCsv,\n", "            percentage: fuzzyMpxnsPercentage,\n", "          },\n", "        },\n", "        addressFuzzyNoMpxn: {\n", "          found: addressFuzzyNoMpxnFound,\n", "          total: totalNoMpxnInCsv,\n", "          percentage: fuzzyNoMpxnPercentage,\n", "        },\n", "        addressFuzzyMpxnMismatch: {\n", "          found: addressFuzzyMpxnMismatchFound,\n", "          total: totalMpxnsInCsv,\n", "          percentage: fuzzyMpxnMismatchPercentage,\n", "        },\n", "      },\n", "      fuseScore: {\n", "        avgFuseScore,\n", "        minFuseScore,\n", "        maxFuseScore,\n", "      },\n", "    },\n", "    totalCSVRecords,\n", "    totalMatches,\n", "    missing<PERSON><PERSON><PERSON>,\n", "    timestamp: new Date().toISOString(),\n", "  };\n", "\n", "  console.log(\"=== Final Statistics ===\");\n", "  console.log(JSON.stringify(stats, null, 2));\n", "\n", "  const timestampForFile = new Date().toISOString().replace(/[:.]/g, \"-\");\n", "  const statsFilename = `output/statistics-fuzzy-address-match-${timestampForFile}.json`;\n", "  await writeFile(statsFilename, JSON.stringify(stats, null, 2), \"utf-8\");\n", "  console.log(`Statistics written to ${statsFilename}`);\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 7) Run the Pipeline and Final Output\n", "////////////////////////////////////////////////////////////////\n", "\n", "async function run() {\n", "  // Read CSV once\n", "  const allCsvRecords = await getCSVRecords(`data/${CSV_FILE}`, EXPECTED_HEADERS);\n", "\n", "  // Compute direct matching statistics\n", "  const allMpxns = allCsvRecords.map((rec) => rec[\"Mpxn\"]?.trim()).filter(Boolean);\n", "  const directMatchingStats = await getDirectMatchingStats(allMpxns);\n", "\n", "  // Run the pipeline with the CSV records\n", "  await main<PERSON><PERSON>eline(allCsvRecords);\n", "\n", "  // Disconnect DB & Redis\n", "  await redisClient.disconnect();\n", "  await db.destroy();\n", "\n", "  // Generate statistics with precomputed direct matching stats\n", "  await generateStatistics(directMatchingStats);\n", "}\n", "\n", "// Execute the pipeline\n", "await run();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read in matches JSONL and view the top 10 worst matches\n", "\n", "This reads the result from the `matches.jsonl` file generated in the above script and outputs the top 10 worst matches.\n", "\n", "Writes to `output/top_10_poor_matches.jsonl`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import { readFile, writeFile, readdir, access } from 'node:fs/promises';\n", "import { constants } from 'node:fs';\n", "\n", "async function extractWorstMatches() {\n", "  const inputFilePath = 'output/matches.jsonl';\n", "  const outputFilePath = 'output/top_10_poor_matches.jsonl';\n", "\n", "  // Check if the input file exists.\n", "  try {\n", "    await access(inputFilePath, constants.F_OK);\n", "    console.log(`File found: ${inputFilePath}`);\n", "  } catch (err) {\n", "    console.error(`File not found: ${inputFilePath}`);\n", "    // List files in the \"output\" directory for debugging purposes.\n", "    try {\n", "      const files = await readdir('output');\n", "      console.error('Files in the \"output\" directory:', files);\n", "    } catch (readDirError) {\n", "      console.error('Error reading the \"output\" directory:', readDirError);\n", "    }\n", "    throw err;\n", "  }\n", "\n", "  // Read the entire matches.jsonl file as text.\n", "  let data;\n", "  try {\n", "    data = await readFile(inputFilePath, 'utf-8');\n", "    console.log(`Read ${data.length} characters from ${inputFilePath}`);\n", "  } catch (readError) {\n", "    console.error(`Error reading file ${inputFilePath}:`, readError);\n", "    throw readError;\n", "  }\n", "\n", "  // Split the file into lines and filter out any empty lines.\n", "  const lines = data.split('\\n').filter(line => line.trim() !== '');\n", "  console.log(`Found ${lines.length} non-empty lines in ${inputFilePath}`);\n", "\n", "  // Parse each line into a JSON object.\n", "  const matches = [];\n", "  for (const [index, line] of lines.entries()) {\n", "    try {\n", "      matches.push(JSON.parse(line));\n", "    } catch (parseError) {\n", "      console.error(`Error parsing JSON on line ${index + 1}: ${line}`);\n", "      throw parseError;\n", "    }\n", "  }\n", "\n", "  // Sort matches descending by fuseScore (higher scores indicate worse matches).\n", "  matches.sort((a, b) => b.fuseScore - a.fuseScore);\n", "  console.log('Matches sorted by fuseScore in descending order.');\n", "\n", "  // Extract the top 10 worst scores.\n", "  const top10Worst = matches.slice(0, 10);\n", "  console.log(`Extracted ${top10Worst.length} worst matches.`);\n", "\n", "  // Convert each match back into a JSON line.\n", "  const outputData = top10Worst.map(match => JSON.stringify(match)).join('\\n');\n", "\n", "  // Write the result to a new file.\n", "  try {\n", "    await writeFile(outputFilePath, outputData, 'utf-8');\n", "    console.log(`Top 10 worst matches written to ${outputFilePath}`);\n", "  } catch (writeError) {\n", "    console.error(`Error writing file ${outputFilePath}:`, writeError);\n", "    throw writeError;\n", "  }\n", "}\n", "\n", "extractWorstMatches().catch(err => {\n", "  console.error('Error processing matches:', err);\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Count Mpxn\n", "\n", "1. Reads in the CSV file and counts the total number of entries classified as MPAN or MPRN based on the length of the Mpxn field.\n", "2. Counts the number of entries with MPAN (Mpxn length === 13).\n", "3. Counts the number of entries with MPRN (non-empty Mpxn with length !== 13).\n", "\n", "The below `countMpxnEntries` function counts the number of entries in the CSV file that have a non-empty Mpxn value."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import { readFile } from \"node:fs/promises\";\n", "import { parse } from \"npm:csv-parse/sync\";\n", "import * as dotenv from \"npm:dotenv\";\n", "\n", "dotenv.config({ path: \"../.env\" });\n", "\n", "const CSV_FILE = \"bgas_10_03_2025_marketing_campaign.csv\";\n", "\n", "/**\n", " * Reads the CSV file and logs:\n", " * 1. Total number of entries.\n", " * 2. Count of entries with MPAN (Mpxn length === 13).\n", " * 3. Count of entries with MPRN (non-empty Mpxn with length !== 13).\n", " */\n", "async function countMpxnTypes(): Promise<void> {\n", "  try {\n", "    const data = await readFile(`data/${CSV_FILE}`, \"utf-8\");\n", "    const records = parse(data, {\n", "      columns: true,\n", "      skip_empty_lines: true,\n", "    });\n", "\n", "    const totalEntries = records.length;\n", "    let mpanCount = 0;\n", "    let mprnCount = 0;\n", "\n", "    for (const record of records) {\n", "      const mpxn = record[\"Mpxn\"]?.trim();\n", "      if (mpxn) {\n", "        if (mpxn.length === 13) {\n", "          mpanCount++;\n", "        } else {\n", "          mprnCount++;\n", "        }\n", "      }\n", "    }\n", "\n", "    console.log(`Total CSV entries: ${totalEntries}`);\n", "    console.log(`Entries with MPAN (length 13): ${mpanCount}`);\n", "    console.log(`Entries with MPRN (non-empty, not length 13): ${mprnCount}`);\n", "  } catch (error) {\n", "    console.error(\"Error reading or parsing CSV file:\", error);\n", "  }\n", "}\n", "\n", "countMpxnTypes();\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Count if MPAN exists in CRM PRODUCTION DB\n", "\n", "The below script checks the DB in batches of 100 for MPXNs that exist in the CSV. This was to understand limitations in the first script above which was leading to poor matches.\n", "\n", "## Conclusion\n", "\n", "I was initially trying to match by MPXN and fuzzy or perfect match on address. This led to a ~40% match rate, whilst matching via MPXN only leads to ~95% match rate.\n", "\n", "This led me to believe that customer would NOT be able to find their addresses in the list. But after accomidating for the fact that the MPXN only match is ~95% this turned out to be wrong. We proceeded with the sending of the emails for the market campaign ~60ks worth."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["////////////////////////////////////////////////////////////////\n", "// 1) All Imports and Setup\n", "////////////////////////////////////////////////////////////////\n", "import { <PERSON><PERSON><PERSON>, PostgresDialect } from \"npm:kysely\";\n", "import Pool from \"npm:pg-pool\";\n", "import * as dotenv from \"npm:dotenv\";\n", "import { readFile, mkdir, appendFile, writeFile } from \"node:fs/promises\";\n", "import { parse } from \"npm:csv-parse/sync\";\n", "\n", "dotenv.config({ path: \"../.env\" });\n", "\n", "// CSV file and expected headers\n", "const CSV_FILE = \"bgas_10_03_2025_marketing_campaign.csv\";\n", "const EXPECTED_HEADERS = [\n", "  \"email\",\n", "  \"company.type\",\n", "  \"business_name\",\n", "  \"company.registrationNumber\",\n", "  \"company.charityNumber\",\n", "  \"company.address1\",\n", "  \"company.address2\",\n", "  \"company.address3\",\n", "  \"company.town\",\n", "  \"company.county\",\n", "  \"company.sitePostcode\",\n", "  \"Mpxn\",\n", "  \"contact.businessPhoneNumber\",\n", "  \"contact.contactForename\",\n", "  \"contact.contactSurname\",\n", "  \"contact.position\",\n", "  \"hasElectricity\",\n", "  \"hasGas\",\n", "  \"current_supplier\",\n", "];\n", "\n", "// Minimal interface definitions for our DB tables.\n", "interface MpansRecord {\n", "  id: string;\n", "  value: string;\n", "  entityAddressId: string;\n", "}\n", "\n", "interface MprnsRecord {\n", "  id: string;\n", "  value: string;\n", "  entityAddressId: string;\n", "}\n", "\n", "interface Database {\n", "  mpans: MpansRecord;\n", "  mprns: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;\n", "}\n", "\n", "// Set up the database connection.\n", "const connectionString = Deno.env.get(\"DIRECT_URL\");\n", "if (!connectionString) {\n", "  throw new Error(\"DIRECT_URL not found in environment variables\");\n", "}\n", "\n", "const pool = new Pool({\n", "  connectionString,\n", "  max: 10,\n", "  connectionTimeoutMillis: 10000,\n", "  idleTimeoutMillis: 30000,\n", "  statement_timeout: 30000,\n", "});\n", "\n", "const db = new Kysely<Database>({\n", "  dialect: new PostgresDialect({ pool }),\n", "});\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 2) Helper Functions\n", "////////////////////////////////////////////////////////////////\n", "\n", "/**\n", " * Splits an array into batches of the specified size.\n", " *\n", " * @param array - The array to split.\n", " * @param batchSize - The size of each batch.\n", " * @returns An array of batches.\n", " */\n", "function batchArray<T>(array: T[], batchSize: number): T[][] {\n", "  const batches: T[][] = [];\n", "  for (let i = 0; i < array.length; i += batchSize) {\n", "    batches.push(array.slice(i, i + batchSize));\n", "  }\n", "  return batches;\n", "}\n", "\n", "/**\n", " * Reads a CSV file, verifies headers, and returns an array of records.\n", " *\n", " * @param filePath - Path to the CSV file.\n", " * @param expectedHeaders - List of expected header strings.\n", " * @returns Array of parsed records.\n", " */\n", "async function getCSVRecords(\n", "  filePath: string,\n", "  expectedHeaders: string[]\n", "): Promise<any[]> {\n", "  const data = await readFile(filePath, \"utf-8\");\n", "  const records = parse(data, {\n", "    columns: true,\n", "    skip_empty_lines: true,\n", "  });\n", "  const actualHeaders = Object.keys(records[0] || {});\n", "  const missingHeaders = expectedHeaders.filter((h) => !actualHeaders.includes(h));\n", "  if (missingHeaders.length > 0) {\n", "    throw new Error(`CSV file is missing expected headers: ${missingHeaders.join(\", \")}`);\n", "  }\n", "  return records;\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 3) Function: Check Mpxn Existence Incrementally\n", "////////////////////////////////////////////////////////////////\n", "\n", "/**\n", " * Reads the CSV file, extracts all \"Mpxn\" values, batches them (100 per batch),\n", " * then for each batch:\n", " *   - Separates values into MPANs (length === 13) and MPRNs (other lengths).\n", " *   - Queries the appropriate database table(s) for each batch.\n", " *   - Builds a mapping for each value indicating whether it was found.\n", " *   - Incrementally writes the result of each batch to the output file.\n", " *   - Maintains counters for found / not found for both MPANs and MPRNs.\n", " *   - Finally, outputs a summary to the console and writes a statistics file.\n", " *\n", " * @param outputFile - The file path where results are written (in JSONL format).\n", " */\n", "async function checkMpxnExistenceIncrementally(\n", "  outputFile: string\n", "): Promise<void> {\n", "  // Read CSV records.\n", "  const records = await getCSVRecords(`data/${CSV_FILE}`, EXPECTED_HEADERS);\n", "\n", "  // Extract all non-empty Mpxn values (trimming any whitespace) and remove duplicates.\n", "  const allMpxns = records\n", "    .map((rec) => rec[\"Mpxn\"]?.trim())\n", "    .filter((val) => val && val !== \"\");\n", "  const uniqueMpxns = Array.from(new Set(allMpxns));\n", "  console.log(`Total unique Mpxn values: ${uniqueMpxns.length}`);\n", "\n", "  // Ensure output folder exists and clear the output file.\n", "  await mkdir(\"output\", { recursive: true });\n", "  await writeFile(outputFile, \"\");\n", "\n", "  // Initialize counters.\n", "  let mpanFound = 0;\n", "  let mpanNotFound = 0;\n", "  let mprnFound = 0;\n", "  let mprnNotFound = 0;\n", "\n", "  // Batch the unique Mpxn values into groups of 100.\n", "  const batches = batchArray(uniqueMpxns, 100);\n", "\n", "  for (const batch of batches) {\n", "    // Separate the batch into MPANs (length === 13) and MPRNs (all other lengths).\n", "    const mpanBatch = batch.filter((val) => val.length === 13);\n", "    const mprnBatch = batch.filter((val) => val.length !== 13);\n", "\n", "    let foundMpans: string[] = [];\n", "    let foundMprns: string[] = [];\n", "\n", "    // Query the \"mpans\" table if there are any MPANs in the batch.\n", "    if (mpanBatch.length > 0) {\n", "      const mpanResults = await db\n", "        .selectFrom(\"mpans\")\n", "        .select([\"value\"])\n", "        .where(\"value\", \"in\", mpanBatch)\n", "        .execute();\n", "      foundMpans = mpanResults.map((r) => r.value);\n", "    }\n", "\n", "    // Query the \"mprns\" table if there are any MPRNs in the batch.\n", "    if (mprnBatch.length > 0) {\n", "      const mprnResults = await db\n", "        .selectFrom(\"mprns\")\n", "        .select([\"value\"])\n", "        .where(\"value\", \"in\", mprnBatch)\n", "        .execute();\n", "      foundMprns = mprnResults.map((r) => r.value);\n", "    }\n", "\n", "    // Combine the found values into a single Set.\n", "    const foundSet = new Set([...foundMpans, ...foundMprns]);\n", "\n", "    // Build the result mapping for this batch and update counters.\n", "    const batchResult = batch.map((mpxn) => {\n", "      const exists = foundSet.has(mpxn);\n", "      if (mpxn.length === 13) {\n", "        exists ? mpanFound++ : mpanNotFound++;\n", "      } else {\n", "        exists ? mprnFound++ : mprnNotFound++;\n", "      }\n", "      return { mpxn, exists };\n", "    });\n", "\n", "    // Incrementally write each result (as a JSON line) to the output file.\n", "    for (const result of batchResult) {\n", "      const line = JSON.stringify(result) + \"\\n\";\n", "      await appendFile(outputFile, line);\n", "    }\n", "    console.log(`Processed batch: ${batch.join(\", \")}`);\n", "  }\n", "\n", "  // Compute totals and percentages.\n", "  const totalMpans = mpanFound + mpanNotFound;\n", "  const totalMprns = mprnFound + mprnNotFound;\n", "  const totalMpxns = totalMpans + totalMprns;\n", "  const overallFound = mpanFound + mprnFound;\n", "  const mpanPercentage = totalMpans > 0 ? (mpanFound / totalMpans) * 100 : 0;\n", "  const mprnPercentage = totalMprns > 0 ? (mprnFound / totalMprns) * 100 : 0;\n", "  const overallPercentage = totalMpxns > 0 ? (overallFound / totalMpxns) * 100 : 0;\n", "\n", "  // Log final counts.\n", "  console.log(\"=== Summary of MPXN Existence Check ===\");\n", "  console.log(`Total MPANs: ${totalMpans}`);\n", "  console.log(`Total MPRNs: ${totalMprns}`);\n", "  console.log(`Total Found: ${overallFound}`);\n", "  console.log(`Percentage Found: ${overallPercentage.toFixed(2)}%`);\n", "\n", "  // Prepare a summary object.\n", "  const summary = {\n", "    mpans: {\n", "      found: mpanFound,\n", "      total: totalMpans,\n", "      percentage: mpanPercentage.toFixed(2) + \"%\",\n", "    },\n", "    mprns: {\n", "      found: mprnFound,\n", "      total: totalMprns,\n", "      percentage: mprnPercentage.toFixed(2) + \"%\",\n", "    },\n", "    mpxns: {\n", "      found: overallFound,\n", "      total: totalMpxns,\n", "      percentage: overallPercentage.toFixed(2) + \"%\",\n", "    },\n", "    timestamp: new Date().toISOString(),\n", "  };\n", "\n", "  // Generate a timestamped filename.\n", "  const timestamp = new Date().toISOString().replace(/[:.]/g, \"-\");\n", "  const statsFilename = `output/statistics-mpxn-${timestamp}.json`;\n", "\n", "  // Write the summary object to the statistics file.\n", "  await writeFile(statsFilename, JSON.stringify(summary, null, 2), \"utf-8\");\n", "  console.log(`Statistics written to ${statsFilename}`);\n", "}\n", "\n", "////////////////////////////////////////////////////////////////\n", "// 4) Main Function\n", "////////////////////////////////////////////////////////////////\n", "\n", "async function main() {\n", "  const outputFile = \"output/mpan_existence.jsonl\";\n", "  await checkMpxnExistenceIncrementally(outputFile);\n", "  console.log(`MPxn existence results written to ${outputFile}`);\n", "\n", "  // Clean up the database connection.\n", "  await db.destroy();\n", "  // Removed pool.end() to avoid calling end on the pool twice.\n", "}\n", "\n", "await main();\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"codemirror_mode": "typescript", "file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nbconvert_exporter": "script", "pygments_lexer": "typescript", "version": "5.6.2"}}, "nbformat": 4, "nbformat_minor": 2}