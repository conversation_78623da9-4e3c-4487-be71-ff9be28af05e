# Allowing and verifying manual address entries

Currently the address picker allows users to enter their postcode and this pulls back a list of addresses from our static address database list. Customers can then proceed to pick an existing address from the drop down. Each address is associated with it's electric or gas meters allowing us to identify their meter for the site address they wish to get a quote for.

However, since the addresses are static their address might not be present in the list. We need to allow customers to enter their address manually and then verify that the address is correct against Experian Aperture API.

## Resources

<https://api.experianaperture.io/address/search/v1>

Headers:

```json
{
    "Auth-Token": "301be824-070c-41bb-90b5-1e74b4036fb9"
}
```

Body (electricity):

```json
{
    "country_iso": "GBR",
    "datasets": [
        "gb-additional-electricity"
    ],
    "components": {
        "unspecified": [
            "M2 7LP"
        ]
    },
    "options": [
        {
            "name": "search_type",
            "value": "singleline"
        },
        {
            "name": "prompt_set",
            "value": "optimal"
        }
    ]
}
```

Body (gas):

```json
{
  "country_iso": "GBR",
  "datasets": [
    "gb-additional-gas"
  ],
  "components": {
    "unspecified": [
      "M2 7LP"
    ]
  },
  "options":[
      {
          "name":"search_type",
          "value":"singleline"
      },
      {
          "name": "prompt_set",
          "value": "optimal"
      }
  ]
}
```

Response:

```json
{
    "result": {
        "more_results_available": true,
        "confidence": "Multiple matches",
        "suggestions_key": "aWQ9fmFsdF9rZXk9fmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczME5NR0JSRlFQcEJ3QUFBQUFCQVFnQUFRQUFBQURTRVV1U0FDRVFBZ0FBQUFBQUFBQUFBUC4uWkFBQUFBRC4uLi4uQUFBQUFBQUFBQUFBQUFBQUFBQUJBQUFBQU5JUlRaSUFJUkFDQUFBQUFBQUFBQUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUVBQUFBQTBoRlBrZ0FoRUFJQUFBQUFBQUFBQUFELi4yUUFBQUFBLi4uLi53QUFBQUFBQUFBQUFBQUFBQUFBQVFBQUFBRFNFVlJTQUNFQWdnQUFBQUFBQUFBQUFQLi5aQUFBQUFELi4uLi5BQUFBQUFBQUFBQUFBQUFBQUFBQkFBQUFBTklSVlZJQUlRQ0NBQUFBQUFBQUFBQUEuLjlrQUFBQUFQLi4uLjhBQUFBQUFBQUFBQUFBQUFBQUFBRUFBQUFBMGhGWFVnQWhBSUlSQUNBQUFBQUFBQUFBQVAuLlpBQUFBQUQuLi4uLkFBQUFBQUFBQUFBQUFBQUFBQUFCQUFBQUFOSVJXaElBSVFDQ0VRQWdBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFRQUFBQURTRVZMU0FDRUFoaEVBSUFBQUFBQUFBQUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQS0tfmVsZWM9dHJ1ZX5nYWtfdHlwZT1zaW5nbGVsaW5lfmxvY2FsaXR5PX5wb3N0YWxfY29kZT1-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
        "suggestions_prompt": "Enter selection",
        "suggestions": [
          // ...
        ]
    }
}
```

Each response looks like the following.

```json
{
    "global_address_key": "aWQ9SW5nZXVzLCBTdC4gQW5ucyBIb3VzZSwgU3QuIEFubnMgUGxhY2UsIE1BTkNIRVNURVIgTTIgN0xQLCBVbml0ZWQgS2luZ2RvbX5hbHRfa2V5PTU2ODQyMzkxfmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczMHlPR0JSRlFQcEJ3QUFBQUFCQXdFQUFBQUEwaEZMa2dBaEVBSUFBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFFMHlJRGRNVUFBQUFBQUF-ZWxlYz10cnVlfnBvcz0xfmdha190eXBlPXNpbmdsZWxpbmV-bG9jYWxpdHk9TUFOQ0hFU1RFUn5wb3N0YWxfY29kZT1NMiA3TFB-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
    "text": "Ingeus, St. Anns House, St. Anns Place, MANCHESTER M2 7LP",
    "format": "https://api.experianaperture.io/address/format/v1/aWQ9SW5nZXVzLCBTdC4gQW5ucyBIb3VzZSwgU3QuIEFubnMgUGxhY2UsIE1BTkNIRVNURVIgTTIgN0xQLCBVbml0ZWQgS2luZ2RvbX5hbHRfa2V5PTU2ODQyMzkxfmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczMHlPR0JSRlFQcEJ3QUFBQUFCQXdFQUFBQUEwaEZMa2dBaEVBSUFBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFFMHlJRGRNVUFBQUFBQUF-ZWxlYz10cnVlfnBvcz0xfmdha190eXBlPXNpbmdsZWxpbmV-bG9jYWxpdHk9TUFOQ0hFU1RFUn5wb3N0YWxfY29kZT1NMiA3TFB-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
    "additional_attributes": [
        {
            "name": "picklist_display",
            "value": "Ingeus, St. Anns House, St. Anns Place, MANCHESTER"
        },
        {
            "name": "score",
            "value": "100"
        },
        {
            "name": "postcode",
            "value": "M2 7LP"
        },
        {
            "name": "full_address",
            "value": "true"
        }
    ]
}
```

The second individual address look up;

<https://api.experianaperture.io/address/format/v1/${global_address_key}>

```json
{
  "layouts": [
    "ElectricityUtility"
  ],
  "layout_format": "default"
}
```

```json
{
  "layouts": [
    "GasUtility"
  ],
  "layout_format": "default"
}
```

Gives;

```json
{
    "result": {
        "global_address_key": "aWQ9Um9va2VyeSBIaWxsIEZhcm0sIFJvb2tlcnkgRHJpdmUsIFdlc3Rjb3R0LCBET1JLSU5HLCBTdXJyZXkgUkg0IDNMUSwgVW5pdGVkIEtpbmdkb21-YWx0X2tleT0yMDEwODk2M35kYXRhc2V0PUdCUn5mb3JtYXRfa2V5PUdCUiRHQlIkNy43MzBRT0dCUkZRUHBCd0FBQUFBQkF3RUFBQUFCUTgzajBnQWhBQVlBQUFBQUFBQUFBQUQuLjJRQUFBQUEuLi4uLndBQUFBQUFBQUFBQUFBQUFBQUFBRkpJTkNBelRGRUFBQUFBQUEtLX5lbGVjPXRydWV-cG9zPTd-Z2FrX3R5cGU9c2luZ2xlbGluZX5sb2NhbGl0eT1ET1JLSU5HfnBvc3RhbF9jb2RlPVJINCAzTFF-UUw9N35tYXhfc3VnZ2VzdGlvbnM9Nw",
        "confidence": "Verified match",
        "addresses_formatted": [
            {
                "layout_name": "ElectricityUtility",
                "address": {
                    "address_line_1": "Rookery Hill Farm",
                    "address_line_2": "Rookery Drive",
                    "address_line_3": "Westcott",
                    "locality": "Dorking",
                    "region": "Surrey",
                    "postal_code": "RH4 3LQ",
                    "country": "United Kingdom",
                    "electricity_meters": [
                        {
                            "mpan": "1900035068009",
                            "uprn": "100062495055",
                            "address_line_1": "",
                            "address_line_2": "",
                            "address_line_3": "ROOKERY HILL FARM",
                            "address_line_4": "",
                            "address_line_5": "ROOKERY DRIVE",
                            "address_line_6": "",
                            "address_line_7": "WESTCOTT",
                            "address_line_8": "DORKING",
                            "address_line_9": "",
                            "address_postal_code": "RH4 3LQ",
                            "trading_status": "T",
                            "trading_status_efd": "19971121",
                            "profile_class": "01",
                            "profile_class_efd": "20230315",
                            "meter_timeswitch_class": "801",
                            "meter_timeswitch_class_efd": "20230315",
                            "line_loss_factor": "001",
                            "line_loss_factor_efd": "20230315",
                            "standard_settlement_configuration": "0393",
                            "standard_settlement_configuration_efd": "20230315",
                            "energisation_status": "E",
                            "energisation_status_efd": "20210503",
                            "gsp_group_id": "_J",
                            "gsp_group_efd": "19971119",
                            "data_aggregator_mpid": "MANW",
                            "data_aggregator_efd": "20210503",
                            "data_collector_mpid": "MANW",
                            "data_collector_efd": "20210503",
                            "supplier_mpid": "SPOW",
                            "supplier_efd": "20210503",
                            "meter_operator_mpid": "LBSL",
                            "meter_operator_efd": "20210503",
                            "measurement_class": "A",
                            "measurement_class_efd": "20210503",
                            "green_deal_in_effect": "0",
                            "smso_mpid": "",
                            "smso_efd": "",
                            "dcc_service_flag": "",
                            "dcc_service_flag_efd": "",
                            "ihd_status": "",
                            "ihd_status_efd": "",
                            "smets_version": "",
                            "distributor_mpid": "SEEB",
                            "metered_indicator": "T",
                            "metered_indicator_efd": "20190315",
                            "metered_indicator_etd": "",
                            "consumer_type": "",
                            "relationship_status_indicator": "None",
                            "rmp_state": "O",
                            "rmp_efd": "19971121",
                            "domestic_consumer_indicator": "T",
                            "css_supplier_mpid": "",
                            "css_supply_start_date": "",
                            "meter_serial_number": "E12Z053193",
                            "meter_install_date": "20151215",
                            "meter_type": "RCAMY",
                            "map_mpid": "ACCU",
                            "map_mpid_efd": "20151215",
                            "installing_supplier_mpid": "ENRD",
                            "energy_direction": "I",
                            "energy_direction_efd": "19971119",
                            "energy_direction_etd": "",
                            "connection_type": "W",
                            "connection_type_efd": "19971208",
                            "connection_type_etd": "",
                            "esme_id": "",
                            "meter_location": "",
                            "register_digits": "",
                            "rel_address_primary_name": "ROOKERY HILL FARM",
                            "rel_address_secondary_name": "",
                            "rel_address_street1": "ROOKERY DRIVE",
                            "rel_address_street2": "",
                            "rel_address_locality1": "WESTCOTT",
                            "rel_address_locality2": "",
                            "rel_address_town": "DORKING",
                            "rel_address_postcode": "RH4 3LQ",
                            "rel_address_logical_status": "1",
                            "rel_address_language": "ENG",
                            "rel_address_organisation": "",
                            "rel_address_address_type": "DPA",
                            "rel_address_confidence_score": "100",
                            "rel_address_classification": "RD02",
                            "rel_address_latitude": "51.221603",
                            "rel_address_longitude": "-0.377399"
                        },
                        {
                            "mpan": "1900035068018",
                            "uprn": "100062495055",
                            "address_line_1": "",
                            "address_line_2": "",
                            "address_line_3": "ROOKERY HILL FARM",
                            "address_line_4": "",
                            "address_line_5": "ROOKERY DRIVE",
                            "address_line_6": "",
                            "address_line_7": "WESTCOTT",
                            "address_line_8": "DORKING",
                            "address_line_9": "",
                            "address_postal_code": "RH4 3LQ",
                            "trading_status": "T",
                            "trading_status_efd": "19971121",
                            "profile_class": "01",
                            "profile_class_efd": "20220511",
                            "meter_timeswitch_class": "801",
                            "meter_timeswitch_class_efd": "20210403",
                            "line_loss_factor": "001",
                            "line_loss_factor_efd": "20220511",
                            "standard_settlement_configuration": "0393",
                            "standard_settlement_configuration_efd": "20220511",
                            "energisation_status": "E",
                            "energisation_status_efd": "20210403",
                            "gsp_group_id": "_J",
                            "gsp_group_efd": "19971119",
                            "data_aggregator_mpid": "MANW",
                            "data_aggregator_efd": "20210403",
                            "data_collector_mpid": "MANW",
                            "data_collector_efd": "20210403",
                            "supplier_mpid": "SPOW",
                            "supplier_efd": "20210403",
                            "meter_operator_mpid": "LBSL",
                            "meter_operator_efd": "20210403",
                            "measurement_class": "A",
                            "measurement_class_efd": "20210403",
                            "green_deal_in_effect": "0",
                            "smso_mpid": "",
                            "smso_efd": "",
                            "dcc_service_flag": "",
                            "dcc_service_flag_efd": "",
                            "ihd_status": "",
                            "ihd_status_efd": "",
                            "smets_version": "",
                            "distributor_mpid": "SEEB",
                            "metered_indicator": "T",
                            "metered_indicator_efd": "20190315",
                            "metered_indicator_etd": "",
                            "consumer_type": "",
                            "relationship_status_indicator": "None",
                            "rmp_state": "O",
                            "rmp_efd": "19971121",
                            "domestic_consumer_indicator": "T",
                            "css_supplier_mpid": "",
                            "css_supply_start_date": "",
                            "meter_serial_number": "E15Z005745",
                            "meter_install_date": "20151204",
                            "meter_type": "RCAMY",
                            "map_mpid": "ACCU",
                            "map_mpid_efd": "20151204",
                            "installing_supplier_mpid": "ENRD",
                            "energy_direction": "I",
                            "energy_direction_efd": "19971119",
                            "energy_direction_etd": "",
                            "connection_type": "W",
                            "connection_type_efd": "19971208",
                            "connection_type_etd": "",
                            "esme_id": "",
                            "meter_location": "",
                            "register_digits": "",
                            "rel_address_primary_name": "ROOKERY HILL FARM",
                            "rel_address_secondary_name": "",
                            "rel_address_street1": "ROOKERY DRIVE",
                            "rel_address_street2": "",
                            "rel_address_locality1": "WESTCOTT",
                            "rel_address_locality2": "",
                            "rel_address_town": "DORKING",
                            "rel_address_postcode": "RH4 3LQ",
                            "rel_address_logical_status": "1",
                            "rel_address_language": "ENG",
                            "rel_address_organisation": "",
                            "rel_address_address_type": "DPA",
                            "rel_address_confidence_score": "100",
                            "rel_address_classification": "RD02",
                            "rel_address_latitude": "51.221603",
                            "rel_address_longitude": "-0.377399"
                        }
                    ]
                }
            }
        ]
    }
}
```

## Requirements

1. Enhance the existing `<AddressField />` component to allow the user to enter their address manually.
2. Users can click a button with link style text 'Enter address manually' to swap the address drop down menu picker with the individual address fields.
3. Which reveals 'Select Address' link text to swap the address fields with the address drop down menu picker.
4. Ensure the input fields follow the existing EntityAddress schema.
5. The user must then enter their address manually and then click 'Submit' to verify the address.
6. This then sends the users manually entered address to the Experian Aperture API and returns a list of suggestions.
7. The suggestions are then displayed to the user in a list and the user can select one of the suggestions or proceed with the manually entered address
8. If the user select the suggestion it calls the second endpoint
9. The individual fields for the manually entered address remain visible even if the user selects a suggestion from the list.
