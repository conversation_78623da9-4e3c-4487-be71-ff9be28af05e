name: "Quote Web job"

on:
  workflow_call:
    inputs:
      environment:
        description: "Environment"
        required: true
        type: string

jobs:
  # lint:
  #   runs-on: ubuntu-latest
  #   environment:
  #     name: ${{ inputs.environment }}
  #   timeout-minutes: 10
  #   permissions:
  #     id-token: write
  #     contents: read
  #   steps:
  #     - uses: actions/checkout@v3
  #     - uses: actions/setup-node@v4
  #       with:
  #         node-version: ${{ vars.NODE_VERSION }}
  #         cache: "yarn"
  #         cache-dependency-path: |
  #           frontend/yarn.lock
  #     - name: Installing dependencies
  #       working-directory: frontend
  #       run: yarn install

  #     - name: Run lint
  #       working-directory: frontend
  #       run: |
  #         yarn prettier-check
  #         yarn lint

  # unit-test:
  #   runs-on: ubuntu-latest
  #   environment:
  #     name: ${{ inputs.environment }}
  #   timeout-minutes: 10
  #   permissions:
  #     id-token: write
  #     contents: read
  #   steps:
  #     - uses: actions/checkout@v3
  #     - uses: actions/setup-node@v4
  #       with:
  #         node-version: ${{ vars.NODE_VERSION }}
  #         cache: "yarn"
  #         cache-dependency-path: |
  #           frontend/yarn.lock
  #     - name: Installing dependencies
  #       working-directory: frontend
  #       run: yarn install

  #     - name: Run Jest tests
  #       working-directory: frontend
  #       run: yarn test

  # integration-test:
  #   runs-on: ubuntu-latest
  #   environment:
  #     name: ${{ inputs.environment }}
  #   timeout-minutes: 10
  #   permissions:
  #     id-token: write
  #     contents: read
  #   steps:
  #     - uses: actions/checkout@v3
  #     - uses: actions/setup-node@v4
  #       with:
  #         node-version: ${{ vars.NODE_VERSION }}
  #         cache: "yarn"
  #         cache-dependency-path: |
  #           frontend/yarn.lock
  #     - name: Installing dependencies
  #       working-directory: frontend
  #       run: yarn install

  #     - name: Run Cypress tests
  #       uses: cypress-io/github-action@v5
  #       timeout-minutes: 10
  #       with:
  #         record: true
  #         working-directory: frontend/apps/quote
  #         build: yarn build # make it use the cached build
  #         start: yarn start
  #         wait-on: "http://localhost:3001"
  #         command: yarn cypress:run --env TAGS='@smoke'
  #         config: pageLoadTimeout=100000

  build:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    outputs:
      file_list: ${{ steps.generate_file_list.outputs.file_list }}
    # needs: [lint, unit-test]
    steps:
      - uses: actions/checkout@v3
      - name: Node Setup
        uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            frontend/yarn.lock
      - name: Installing dependencies
        working-directory: frontend
        run: |
          node --version
          yarn --version
          yarn install

      - name: Build quote web ${{ inputs.environment }}
        working-directory: frontend/apps/quote
        run: |
          yarn build
          echo "NEXT_PUBLIC_API_URL: $NEXT_PUBLIC_API_URL"
          echo "NEXT_PUBLIC_CRM_API_URL: $NEXT_PUBLIC_CRM_API_URL"
        env:
          NEXT_PUBLIC_API_URL: ${{ (inputs.environment == 'prod' && 'https://api.app.watt.co.uk') || format('https://api.{0}.watt.co.uk', inputs.environment) }}
          NEXT_PUBLIC_CRM_API_URL: ${{ (inputs.environment == 'prod' && 'https://crm.watt.co.uk') || format('https://{0}.crm.watt.co.uk', inputs.environment) }}
          NEXT_PUBLIC_GITHUB_SHA: ${{ github.sha }}
          NEXT_PUBLIC_WATT_HOMEPAGE_URL: ${{ vars.WATT_HOMEPAGE_URL }}

      - name: Cache build artifacts
        uses: actions/cache/save@v3
        with:
          path: frontend/apps/quote/build
          key: ${{ inputs.environment }}-${{ runner.os }}-build-${{ hashFiles('**/quote/build') }}

      - name: Generate file list for CloudFront invalidation
        id: generate_file_list
        run: |
          echo "file_list=$(find frontend/apps/quote/build -type d \( -name '_next' -o -name 'assets' \) -prune -o -type f -printf '/%P\n' | sed ':a;N;$!ba;s/\n/ /g')" >> $GITHUB_OUTPUT
        shell: bash

  deploy:
    if: ${{ inputs.environment != 'local' }}
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
      url: https://${{ (inputs.environment == 'prod' && 'app.watt.co.uk') || format('{0}.watt.co.uk', inputs.environment) }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    needs:
      - build
      # - integration-test
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            frontend/yarn.lock
      - name: Installing dependencies
        working-directory: frontend
        run: |
          node --version
          yarn --version
          yarn install

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Get distribution ID
        id: get_distribution_id
        run: |
          # Get the distribution ID for the environment
          distribution_id=$(aws cloudfront list-distributions | jq -r --arg env "${{ inputs.environment }}" 'first(.DistributionList.Items[] | select(.Origins.Items[]?.DomainName? | contains($env)) | .Id)')
          echo "Distribution ID: $distribution_id"
          if [ -z "$distribution_id" ]; then
            echo "No distribution ID found for ${{ inputs.environment }}"
            exit 1
          fi
          echo "distribution_id=$distribution_id" >> $GITHUB_OUTPUT

      - name: Restore build artifacts from cache
        uses: actions/cache/restore@v3
        id: restore-cache
        with:
          path: frontend/apps/quote/build
          key: ${{ inputs.environment }}-${{ runner.os }}-build-${{ hashFiles('**/quote/build') }}

      - name: Deploy Quote Web
        working-directory: frontend/apps/quote/build
        run: |
          # Copy everything to S3
          # clean s3 bucket
          aws s3 rm s3://${{ inputs.environment }}-quotation-website --recursive
          aws s3 cp --recursive --cache-control="max-age=0, no-cache, no-store, must-revalidate" . s3://${{ inputs.environment }}-quotation-website

      - name: Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ steps.get_distribution_id.outputs.distribution_id }} --paths ${{ needs.build.outputs.file_list }}
