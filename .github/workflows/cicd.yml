name: "Quote"

on:
  push:
    branches:
      - master
    tags:
      - v*
    paths:
      - "seeder/**"
      - "lambda/**"
      - "data/pdf/contracts/**"
      - "python/**"
      - "terraform/composition/**"
      - "terraform/modules/**"
      - ".github/workflows/*"
      - "frontend/**"
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
    branches:
      - master
    paths:
      - "seeder/**"
      - "lambda/**"
      - "data/pdf/contracts/**"
      - "python/**"
      - "terraform/composition/**"
      - "terraform/modules/**"
      - ".github/workflows/*"
      - "frontend/**"
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: qat
        type: choice
        options:
          # dev via PRs only
          - dev
          - qat
          - uat
          - pen
          - a11y
          - l10n
          - prod

jobs:
  # Determine the environment based on the event type
  determine-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
    steps:
      - name: Determine environment
        id: set-env
        run: |
          if [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == "refs/heads/master" && "${{ github.event.ref_type }}" == "tag" ]]; then
            echo "environment=prod" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "push" || "${{ github.event_name }}" == "pull_request" && "${{ github.event.pull_request.merged }}" == "true" ]]; then
            echo "environment=dev" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "pull_request" && "${{ github.event.pull_request.merged }}" == "false" ]]; then
            echo "environment=local" >> $GITHUB_OUTPUT
          else
            echo "environment=${{ inputs.environment }}" >> $GITHUB_OUTPUT
          fi

  # Check for changes
  check-paths:
    runs-on: ubuntu-latest
    outputs:
      quote-infrastructure-changed: ${{ steps.check-paths.outputs.quote-infrastructure-changed }}
      quote-app-changed: ${{ steps.check-paths.outputs.quote-app-changed }}
      quote-seeder-changed: ${{ steps.check-paths.outputs.quote-seeder-changed }}
      quote-lambda-changed: ${{ steps.check-paths.outputs.quote-lambda-changed }}
      quote-contracts-changed: ${{ steps.check-paths.outputs.quote-contracts-changed }}
      quote-api-changed: ${{ steps.check-paths.outputs.quote-api-changed }}
      terraform-composition-changed: ${{ steps.check-paths.outputs.terraform-composition-changed }}
      terraform-modules-changed: ${{ steps.check-paths.outputs.terraform-modules-changed }}
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - id: check-paths
        run: |
          # TODO target apps/quote not whole frontend
          echo "quote-infrastructure-changed=$(git diff --quiet HEAD^ HEAD terraform/ || echo true)" >> $GITHUB_OUTPUT
          echo "quote-app-changed=$(git diff --quiet HEAD^ HEAD frontend/ || echo true)" >> $GITHUB_OUTPUT
          echo "quote-seeder-changed=$(git diff --quiet HEAD^ HEAD seeder/ || echo true)" >> $GITHUB_OUTPUT
          echo "quote-lambda-changed=$(git diff --quiet HEAD^ HEAD lambda/ || echo true)" >> $GITHUB_OUTPUT
          echo "quote-contracts-changed=$(git diff --quiet HEAD^ HEAD data/pdf/contracts/ || echo true)" >> $GITHUB_OUTPUT
          echo "quote-api-changed=$(git diff --quiet HEAD^ HEAD python/ || echo true)" >> $GITHUB_OUTPUT
          echo "terraform-composition-changed=$(git diff --quiet HEAD^ HEAD terraform/composition/ || echo true)" >> $GITHUB_OUTPUT
          echo "terraform-modules-changed=$(git diff --quiet HEAD^ HEAD terraform/modules/ || echo true)" >> $GITHUB_OUTPUT

  quote-infrastructure:
    needs: [determine-env, check-paths]
    if: ${{ needs.check-paths.outputs.terraform-composition-changed || needs.check-paths.outputs.terraform-modules-changed }}
    uses: ./.github/workflows/infrastructure-quote-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}

  quote-seeder:
    needs: [determine-env, check-paths]
    if: ${{ needs.check-paths.outputs.quote-seeder-changed || github.event_name == 'workflow_dispatch'  }}
    uses: ./.github/workflows/backend-quote-seeder-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}

  quote-lambda:
    needs: [determine-env, check-paths]
    if: ${{ needs.check-paths.outputs.quote-lambda-changed || github.event_name == 'workflow_dispatch'  }}
    uses: ./.github/workflows/backend-quote-lambda-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}

  quote-contracts:
    needs: [determine-env, check-paths]
    if: ${{ (needs.check-paths.outputs.quote-contracts-changed || github.event_name == 'workflow_dispatch') && needs.determine-env.outputs.environment != 'local'  }}
    uses: ./.github/workflows/assets-quote-contracts-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}

  quote-api:
    needs: [determine-env, check-paths]
    if: ${{ (needs.check-paths.outputs.quote-api-changed || needs.check-paths.outputs.terraform-composition-changed || needs.check-paths.outputs.terraform-modules-changed) || github.event_name == 'workflow_dispatch' }}
    uses: ./.github/workflows/backend-quote-api-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}
    secrets:
      POSTMAN_API_KEY: ${{ secrets.POSTMAN_API_KEY }}

  quote-app:
    needs: [determine-env, check-paths]
    if: ${{ needs.check-paths.outputs.quote-app-changed || github.event_name == 'workflow_dispatch' }}
    uses: ./.github/workflows/frontend-quote-app-job.yml
    with:
      environment: ${{ needs.determine-env.outputs.environment }}
