name: "Quote API job"

on:
  workflow_call:
    inputs:
      environment:
        description: "Deployment environment"
        required: true
        type: string
    secrets:
      POSTMAN_API_KEY:
        required: true

jobs:
  lint:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: ${{ vars.PYTHON_VERSION }}

      - name: Install pipenv
        run: python -m pip install pipenv
        shell: bash

      - name: Install dependencies in dev mode
        run: python -m pipenv install --dev
        working-directory: python
        shell: bash

      - name: black check
        run: python -m pipenv run format-black-check
        working-directory: python
        shell: bash

      - name: isort check
        run: python -m pipenv run format-isort-check
        working-directory: python
        shell: bash

      - name: autoflake check
        run: python -m pipenv run format-unused-check
        working-directory: python
        shell: bash

      - name: hadolint Dockerfile check
        id: hadolint
        uses: hadolint/hadolint-action@v3.1.0
        with:
          dockerfile: python/Dockerfile

  # TODO (<PERSON>) this step handles installing pipenv internall within docker, and does a docker build which is
  # not used by the deploy step. Need to split this up and have the build artifacts re-used to speed up the workflow
  integration-tests:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          # cache: "yarn" # issue https://github.com/actions/setup-node/issues/317
          cache-dependency-path: lambda/yarn.lock
      - uses: actions/setup-python@v4
        with:
          python-version: ${{ vars.PYTHON_VERSION }}

      - name: Start Python API docker up
        id: docker-up
        run: docker-compose up --build -d

      - name: set yarn version
        id: set-yarn-version
        run: yarn set version berry

      - name: db setup
        id: db-setup
        run: |
          chmod +x ./local-helpers/db-setup.sh
          ./local-helpers/db-setup.sh
        shell: bash

      - name: run newman tests
        uses: matt-ball/newman-action@master
        with:
          apiKey: ${{ secrets.POSTMAN_API_KEY }}
          collection: 4391474-93e8e072-b8da-41a8-801a-e729034bed51
          environment: 4391474-3314b249-df36-446a-b6c3-b7ae1ea3a145
          bail: true

      - name: Dump quotation tool logs
        id: dump-quotation-tool-logs
        run: docker compose logs quotation-tool
        # if: failure()
        if: always()

  build:
    if: ${{ inputs.environment != 'local' }}
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    # needs:
    #   - lint

    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: check if image already exists
        id: check-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          IMAGE_EXISTS=$(aws ecr list-images --repository-name ${{ inputs.environment }}-quotation-app-repository | jq -r '.imageIds[] | select(.imageTag=="${{ github.sha }}") | .imageTag')
          if [[ -n "$IMAGE_EXISTS" ]]; then
            echo "Image already exists with the same tag"
            echo "::set-output name=image_exists::true"
          else
            echo "::set-output name=image_exists::false"
          fi

      - name: build quotation-tool image
        id: build-quotation-tool
        if: steps.check-image.outputs.image_exists == 'false'
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          docker build -t $ECR_REGISTRY/${{ inputs.environment }}-quotation-app-repository:${{
          github.sha }} -f ./python/Dockerfile ./python/

      - name: push quotation-tool image
        id: push-quotation-tool
        if: steps.check-image.outputs.image_exists == 'false'
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          docker push $ECR_REGISTRY/${{ inputs.environment }}-quotation-app-repository:${{ github.sha }}

  deploy:
    if: ${{ inputs.environment != 'local' }}
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
      url: https://${{ (inputs.environment == 'prod' && 'api.app.watt.co.uk') || format('api.{0}.watt.co.uk', inputs.environment) }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    needs:
      # - lint
      - build
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: pull quotation-tool task definition
        id: pull-quotation-tool-task-definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ inputs.environment }}-quotation-app-task --query taskDefinition | \
            jq 'del(.registeredBy, .registeredAt, .status, .revision, .requiresAttributes, .taskDefinitionArn, .compatibilities)' > ${{ inputs.environment }}-quotation-app-task.json
          echo "taskDefinition=${{ inputs.environment }}-quotation-app-task.json" >> $GITHUB_ENV

      - name: update ssm parameter for quotation-tool
        id: update-quotation-tool-ssm-parameter
        run: |
          aws ssm put-parameter --name /${{ inputs.environment }}/application/ecr/quotation-app-repository-image-tag --value ${{ github.sha }} --type String --overwrite

      - name: update quotation-tool task definition
        id: update-quotation-tool-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.taskDefinition }}
          container-name: ${{ inputs.environment }}-quotation-app-task
          image: ${{ steps.login-ecr.outputs.registry}}/${{ inputs.environment }}-quotation-app-repository:${{ github.sha }}

      - name: deploy quotation-tool task definition to service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{steps.update-quotation-tool-task-definition.outputs.task-definition}}
          cluster: ${{ inputs.environment }}-quotation-app
          service: ${{ inputs.environment }}-quotation-app-service

      - name: Deregister old task definitions
        run: |
          # Get all task definition ARNs
          TASK_ARN_LIST=$(aws ecs list-task-definitions --family-prefix ${{ inputs.environment }}-quotation-app-task | jq --raw-output '.taskDefinitionArns[]')
          echo "Total number of task definitions: $(echo $TASK_ARN_LIST | wc -w)"

          # Get the revision number of the latest task definition
          LATEST_REVISION=$(aws ecs describe-task-definition --task-definition ${{ inputs.environment }}-quotation-app-task --query taskDefinition | jq -r '.taskDefinitionArn' | awk -F: '{print $NF}')
          echo "Latest revision: $LATEST_REVISION"

          # Calculate the oldest revision to keep
          OLDEST_REVISION_TO_KEEP=$((LATEST_REVISION - 3))
          echo "Oldest revision to keep: $OLDEST_REVISION_TO_KEEP"

          # Deregister old task definitions
          for TASK_ARN in $TASK_ARN_LIST; do
            # Extract the revision number
            REVISION=$(echo $TASK_ARN | awk -F/ '{print $2}' | awk -F: '{print $2}')

            # Check if it's an old task definition
            if [ $REVISION -lt $OLDEST_REVISION_TO_KEEP ]; then
              # Deregister the task definition
              aws ecs deregister-task-definition --task-definition $TASK_ARN
              echo "Deregistered task definition revision: $REVISION"
            fi
          done

      - name: Check new task is running and old task has finished
        run: |
          # Wait for the new tasks to start
          echo "Waiting for the new tasks to start..."
          sleep 60

          # Get the revision number of the latest task definition
          LATEST_REVISION=$(aws ecs describe-task-definition --task-definition ${{ inputs.environment }}-quotation-app-task --query taskDefinition | jq -r '.taskDefinitionArn' | awk -F: '{print $NF}')
          echo "Latest revision: $LATEST_REVISION"

          # Keep checking for old tasks until they are stopped
          while true; do
            # Get the list of running tasks
            RUNNING_TASKS=$(aws ecs list-tasks --cluster ${{ inputs.environment }}-quotation-app --service-name ${{ inputs.environment }}-quotation-app-service --desired-status RUNNING)
            echo "Running tasks: $RUNNING_TASKS"

            # Check if there are any tasks still running with the old task definition
            OLD_TASKS_RUNNING=false
            for TASK_ARN in $(echo $RUNNING_TASKS | jq -r '.taskArns[]'); do
              TASK_DEFINITION=$(aws ecs describe-tasks --cluster ${{ inputs.environment }}-quotation-app --tasks $TASK_ARN | jq -r '.tasks[0].taskDefinitionArn')
              REVISION=$(echo $TASK_DEFINITION | awk -F/ '{print $2}' | awk -F: '{print $2}')

              if [ $REVISION -lt $LATEST_REVISION ]; then
                OLD_TASKS_RUNNING=true
                break
              fi
            done

            if $OLD_TASKS_RUNNING; then
              echo "There are still old tasks running. Waiting for them to stop..."
              sleep 30
            else
              echo "The new task is running, and the old task has finished."
              break
            fi
          done
