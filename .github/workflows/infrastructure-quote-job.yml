name: "IAC Terraform plan CI"

on:
  workflow_call:
    inputs:
      environment:
        description: "Deployment environment"
        required: true
        type: string

jobs:
  terraform:
    name: Terraform Plan
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    env:
      # Needed by the setup-terraform action
      TF_WORKSPACE: ${{ inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Get Terraform version from .terraform-version file
        id: terraform-version
        working-directory: terraform/composition/
        run: |
          echo "Version in .terraform-version file: $(cat .terraform-version)"
          echo "version=$(cat .terraform-version)" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ steps.terraform-version.outputs.version }}

      - name: Terraform Format
        id: fmt
        working-directory: terraform/composition/
        run: terraform fmt -check
        continue-on-error: true

      - name: Terraform Set Version
        working-directory: terraform/composition/
        id: version
        run: terraform version

      - name: Terraform Init
        working-directory: terraform/composition/
        id: init
        run: terraform init

      - name: Terraform Plan
        working-directory: terraform/composition/
        id: plan
        run: terraform plan -var-file=./environments/${{ inputs.environment }}.tfvars -out=plan.tfplan

      - name: Terraform Validate
        working-directory: terraform/composition/
        id: validate
        run: terraform validate

  terrascan:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Run Terrascan
        id: terrascan
        uses: tenable/terrascan-action@main
        with:
          iac_type: "terraform"
          iac_version: "v14"
          policy_type: "aws"
          iac_dir: "terraform/composition"
          verbose: true
          config_path: "terraform/composition/terrascan.toml"
          only_warn: true

  checkov:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python 3.9
        uses: actions/setup-python@v4
        with:
          python-version: ${{ vars.PYTHON_VERSION }}

      - name: Test with Checkov
        id: checkov
        uses: bridgecrewio/checkov-action@v12
        with:
          directory: terraform/composition
          framework: terraform
          config_file: .checkov.yml
