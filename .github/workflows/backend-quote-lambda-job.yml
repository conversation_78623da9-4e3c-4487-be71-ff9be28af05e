name: "Lambda job"

on:
  workflow_call:
    inputs:
      environment:
        description: "Deployment environment"
        required: true
        type: string

jobs:
  lint:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            lambda/yarn.lock
      - name: Installing dependencies
        working-directory: lambda
        run: yarn install --frozen-lockfile

      - name: Run lint
        working-directory: lambda
        run: |
          yarn format-check
          yarn lint

  # TODO (<PERSON>): add unit tests (see frontend-quote-app-job.yml) and integration tests (see frontend-quote-app-job.yml)
  # unit-test:
  # integration-test:

  package:
    if: ${{ inputs.environment == 'local' }}
    runs-on: ubuntu-latest
    needs: lint
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            lambda/yarn.lock
      - name: Installing dependencies
        working-directory: frontend
        run: yarn install --frozen-lockfile

      - name: Get changed files in everything but /lambdas folder
        id: changed-files-not-lambdas
        uses: tj-actions/changed-files@v37
        with:
          files: lambda/**/*
          files_ignore: lambda/lambdas/**/*

      - name: Configure AWS credentials Shared Account
        if: ${{ inputs.environment != 'local' }}
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        if: ${{ inputs.environment != 'local' }}
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Build all lambda projects
        if: steps.changed-files-not-lambdas.outputs.any_changed == 'true'
        working-directory: lambda
        run: |
          echo "Core lambda files have changed all are redeploying"
          yarn install --frozen-lockfile
          yarn package -s ${{ inputs.environment }}

      # only run below if core lambda files haven't changed
      - name: Get changed files in the lambda/lambdas folders
        id: changed-files-specific
        if: steps.changed-files-not-lambdas.outputs.any_changed == 'false'
        uses: tj-actions/changed-files@v37
        with:
          files: lambda/lambdas/**/*

      - name: Build specific lambda projects
        if: steps.changed-files-specific.outputs.any_changed == 'true'
        run: |
          echo "One or more files in the lambdas folder has changed."
          declare -A dirs=() # Declare an associative array to track processed directories
          for file in ${{ steps.changed-files-specific.outputs.all_changed_files }}; do
            echo "$file was changed"
            dir=$(echo $file | awk -F/ '{print $3}')
            if [[ -z "${dirs[$dir]}" ]]; then # Check if directory has already been processed
              echo "deploying: @watt/lambda-$dir"
              dirs[$dir]=1 # Mark directory as processed in the associative array
              echo "dir: $dir"
              cd "lambda/lambdas/$dir"
              echo "current dir: $(pwd)"
              yarn install --frozen-lockfile
              yarn workspace "@watt/lambda-$dir" package -s ${{ inputs.environment }}
            fi
          done

  deploy:
    if: ${{ inputs.environment != 'local' }}
    runs-on: ubuntu-latest
    needs: lint
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            frontend/yarn.lock
      - name: Installing dependencies
        working-directory: frontend
        run: yarn install --frozen-lockfile

      - name: Get changed files in everything but /lambdas folder
        id: changed-files-not-lambdas
        uses: tj-actions/changed-files@v37
        with:
          files: lambda/**/*
          files_ignore: lambda/lambdas/**/*

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Deploy all lambda projects
        if: steps.changed-files-not-lambdas.outputs.any_changed == 'true'
        working-directory: lambda
        run: |
          echo "Core lambda files have changed all are redeploying"
          yarn install --frozen-lockfile
          yarn deploy -s ${{ inputs.environment }}

      # only run below if core lambda files haven't changed
      - name: Get changed files in the lambda/lambdas folders
        id: changed-files-specific
        if: steps.changed-files-not-lambdas.outputs.any_changed == 'false'
        uses: tj-actions/changed-files@v37
        with:
          files: lambda/lambdas/**/*

      - name: Deploy specific lambda projects
        if: steps.changed-files-specific.outputs.any_changed == 'true'
        run: |
          echo "One or more files in the lambdas folder has changed."
          declare -A dirs=() # Declare an associative array to track processed directories
          for file in ${{ steps.changed-files-specific.outputs.all_changed_files }}; do
            echo "$file was changed"
            dir=$(echo $file | awk -F/ '{print $3}')
            if [[ -z "${dirs[$dir]}" ]]; then # Check if directory has already been processed
              echo "deploying: @watt/lambda-$dir"
              dirs[$dir]=1 # Mark directory as processed in the associative array
              echo "dir: $dir"
              cd "lambda/lambdas/$dir"
              echo "current dir: $(pwd)"
              yarn install --frozen-lockfile
              yarn workspace "@watt/lambda-$dir" deploy -s ${{ inputs.environment }}
            fi
          done
