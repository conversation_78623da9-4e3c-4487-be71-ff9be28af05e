name: "Seeder job"

on:
  workflow_call:
    inputs:
      environment:
        description: "Deployment environment"
        required: true
        type: string

jobs:
  lint:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            seeder/yarn.lock
      - name: Installing dependencies
        working-directory: seeder
        run: yarn install --frozen-lockfile

      - name: Run lint
        working-directory: seeder
        run: |
          yarn format-check
          yarn lint

  # TODO (Stephen): add unit tests (see frontend-quote-app-job.yml) and integration tests (see frontend-quote-app-job.yml)
  # unit-test:
  # integration-test:

  package:
    if: ${{ inputs.environment == 'local' }}
    runs-on: ubuntu-latest
    needs: lint
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            seeder/yarn.lock
      - name: Installing dependencies
        working-directory: seeder
        run: yarn install --frozen-lockfile

      - name: Build seeder
        working-directory: seeder
        run: yarn package -s ${{ inputs.environment }}

  deploy:
    if: ${{ inputs.environment != 'local' }}
    runs-on: ubuntu-latest
    needs: lint
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}
          cache: "yarn"
          cache-dependency-path: |
            frontend/yarn.lock
      - name: Installing dependencies
        working-directory: seeder
        run: yarn install --frozen-lockfile

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Deploy seeder
        working-directory: seeder
        run: yarn deploy -s ${{ inputs.environment }}
