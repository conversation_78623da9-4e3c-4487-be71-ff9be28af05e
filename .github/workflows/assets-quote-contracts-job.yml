name: "Contracts job"

on:
  workflow_call:
    inputs:
      environment:
        description: "Deployment environment"
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment }}
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    name: Upload Contracts to S3
    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS credentials Shared Account
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.WATT_SHARED_AWS_ACCOUNT_ID }}:role/github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt-shared

      - name: Configure other AWS Credentials
        uses: aws-actions/configure-aws-credentials@main
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/${{ inputs.environment }}-github-actions-role
          role-session-name: ${{ inputs.environment }}-aws-github-actions-watt
          role-chaining: true

      - name: Upload to S3
        run: |
          which aws
          chmod +x ./local-helpers/upload-s3-contracts.sh
          ./local-helpers/upload-s3-contracts.sh -s ${{ inputs.environment }}
