# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "npm"
    directory: "/lambda"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "npm"
    directory: "/seeder"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "docker"
    directory: "/python"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "pip"
    directory: "/python"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/composition"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/networking"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/remote_state"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/shared"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/wordpress"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
  - package-ecosystem: "terraform"
    directory: "/terraform/modules/**"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
