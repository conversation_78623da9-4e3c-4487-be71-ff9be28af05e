# Setting up a new environment

If you want to setup a new environment on AWS follow these steps:

## Lambdas

- Before deploying the terraform the lambdas need to be deployed with the
  appropriate new environment prefix (the lambdas are required to exist before
  deploying the terraform). Remove the environment variables (ssm params, vpc
  subnet ids, security group ids) from each lambda and deploy them.
- Run:
  `yarn workspace @watt/lambda-$lambda_workspace_name deploy -s $env_name` default env_name is `dev`

## Terraform

- Create a new terraform workspace, run (it swaps into it by default):
  - `tf workspace new $env_name`
- Create a new .tfvars file for your environment in the `/terraform/composition`
  folder. You can just copy and rename one that's already there but make sure
  you edit the variables to be correct for your env.
- In the .tfvars file make sure `environment` has the name you want, be _SURE_
  to change `app_vpc_cidr` and check that it doesn't clash with any other
  existing VPC subnets on aws.
- Run a plan and check through before running an apply (make sure you have
  correct aws tokens exported in your env before running):
  - terraform plan -var-file=$env_name.tfvars
- Then if everything is correct run:
  - terraform apply -var-file=$env_name.tfvars
  - (it will get stuck so check first step below in the AWS section)
- Redeploy all lambdas with env varibles back after the apply has gone through
- Run script to deploy new container in ECS (change name in script to suit env):
  - `sh ./local-helpers/deploy-quote-container-prod.sh`

## AWS

- After running an apply it will get stuck on acm validation step which is where
  you need to go into the console and get the NS values from the route53 record
  setup for the new env, copy those NS records and put them in the root watt
  accoount route53 record.
- Next step is to go to amplify and setup a new application with the new
  environment route53 record.
- Go to lambda service and run both the `seed` and `setup` lambdas (if this
  fails go into RDS and reset the password twice then try again.)
