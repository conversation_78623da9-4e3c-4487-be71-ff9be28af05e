# SQLAlchemy Tips

## Single-table inheritance

Some of our ORM models use single-table inheritance. (https://docs.sqlalchemy.org/en/14/orm/inheritance.html#single-table-inheritance)

Models currently using single-table inheritance are:

- `Company` (`LtdCompany`, `CharityCompany`, `SoleTraderCompany`)
- `Quote` (`ElectricityQuote`, `GasQuote`)

Single-table inheritance means that all subclasses of the main model are stored in a single table (as the name implies).

You should take care when building models using single-table inheritance, to ensure that shared properties go on the base class.

### Single-table inheritance quirks

- When using single-table inheritance, you **must not** set any fields on the subclasses to `nullable=False`. This is because the subclasses are stored in the same table as the main model, and the main model/other models will not have these fields set.
- When querying, you often want to use the `selectin_polymorphic` option, as this will instruct the ORM to load properties for subclasses rather than just the base class. Search the codebase for examples of this.
