
# Bastion Jump Box

A bastion jump box is used to connect to resources behind a private subnet. This is useful for connecting to the RDS database. Below are steps on how you can do this

## Usage

1. Set `bastion_enabled  = true` in the `*.tfvars` file and run `make apply s=<environment>`.
2. Manually create a key pair in the AWS console and download the private key.
   1. Log in to AWS via SSO > go to EC2 > Key Pairs > Create Key Pair.

Check if it already exists

```
aws ec2 describe-key-pairs --key-names prod-watt-bastion-host --query 'KeyPairs[*].KeyName' --output text
```

If not make it

```
aws ec2 create-key-pair --key-name prod-watt-bastion-host --query 'KeyMaterial' --output text > ~/.ssh/prod-watt-bastion-host.pem && chmod 400 ~/.ssh/prod-watt-bastion-host.pem
```

You can run psql directly from your terminal scope using;

where you need the Bastion IP.
And you need the connection string for RDS

`*************` and
`prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com`

Password can be found in configuration.

```
ssh -i ~/.ssh/prod-watt-bastion-host.pem ec2-user@************* "psql -h prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com -p 5432 -U myusername -d watt_portal -c '\dt'"
```

If you are here just to clear out test data use the following. Ensure you run with `ROLLBACK` first before switching to `COMMIT`.

```
cd python/alembic/manual
cat remove-test-data-from-production.sql | ssh -i ~/.ssh/prod-watt-bastion-host.pem ec2-user@************* "PGPASSWORD=<PASSWORD_GOES_HERE> psql -h prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com -p 5432 -U myusername -d watt_portal" > deletion_report.txt
```

ssh in

```
ssh -i ~/.ssh/prod-watt-bastion-host.pem ec2-user@<bastion-public-ip>
```

use psql

```
psql -h <rds-endpoint> -U superadmin -d watt_portal
```

   2. Leave the defaults for a pem file give it a name and save it to your local machine.
   3. Connect via SSH to the bastion host. `ssh -i <path to private key> ec2-user@<bastion public ip>`
   1. The `<bastion public ip>` is outputted from the `make apply` command or can be found in the AWS console > EC2 > Instances > Bastion > Public IP.
   2. E.g. `ssh -i prod-watt-bastion-host-prod.pem <EMAIL>`
   4. Once connected use `psql` to connect to the database. `PGPASSWORD=<database password> psql -h <database private ip> -U <database username> -d <database name>`
   1. The `<database private ip>` is outputted from the `make apply` command or can be found in the AWS console > RDS > Databases > Database > Connectivity & Security > Private IP.
   2. The username and password are in the terraform code.
   3. E.g. `PGPASSWORD=mypassword psql -h prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com -U myusername -d watt_portal`
   5. Enter the password when prompted.

## Manual bastion jumpbox setup

### Create a Key Pair (if not available)

1. Manually create a key pair in the AWS console and download the private key.
   1. Log in to AWS via SSO > go to EC2 > Key Pairs > Create Key Pair.
   2. Leave the defaults for a pem file give it a name and save it to your local machine.

### Create an EC2 bastion server

1. Go to EC2 > Instances > Launch a Instances
2. Give it a name e.g `dev-bastion-host-manual`
3. Select an image - we are using Amazon Linux
4. Select Key pair that was created in step 1 for login
5. On Network settings, click on edit then select the VPC that the application you want to access is using and set Auto-assign public IP to enable
6. Finaly Launch Instance

### Modify the RDS instance's security group

1. Go to RDS Dashboard > Databases > select your database > click on the link under Security group rules.
2. Add an inbound rule that allows database traffic from the security group that is attached to your bastion host. e.g `Type: PostgreSQL, Source type: Custom Source: <security group attached to the bastion server>`

### Connect to bastion server via ssh

1. Connect via SSH to the bastion host. `ssh -i <path to private key> ec2-user@<bastion public ip>`.
1. The `<bastion public ip>` can be found in the AWS console > EC2 > Instances > Bastion > Public IP.
2. E.g. `ssh -i migration-fix.pem <EMAIL>`. You may need to run `chmod 400 <path to private key>` to ensure your key is not publicly viewable.
2. Once connected use `psql` to connect to the database. `PGPASSWORD=<database password> psql -h <database private ip> -U <database username> -d <database name>`
   1. The `<database private ip>` can be found in the AWS console > RDS > Databases > Database > Connectivity & Security > Private IP.
   2. The username and password are in the terraform code.
   3. E.g. `PGPASSWORD=mypassword psql -h prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com -U myusername -d watt_portal`

### Copy snapshot from db1 to db2

1. Create a snapshot of db1 using pg_dump

    ```
      pg_dump -h production-db-endpoint -U production-db-username -W -F c -b -v -f "/path/to/your/backup/file.dump" production-db-name
      -h: Specifies the host or socket directory for the connection.
      -U: Connects to the database as this username.
      -W: Forces pg_dump to prompt for a password before connecting to a database.
      -F c: Outputs a custom-format archive suitable for input into pg_restore.
      -b: Includes large objects in the dump.
      -v: Enables verbose mode.
      -f: Specifies the name of the file to write the dump to.
      production-db-name: Replace this with the name of your production database.
    ```

    E.g script `pg_dump -h prod-quotation-db-2023012014390383920000001e.cnlqcmltptrb.eu-west-2.rds.amazonaws.com -U myusername -W -F c -b -v -f "/home/<USER>/file.dump" watt_portal`
2. Copy snapshot from bastion server1 (db1) to local
    `scp -i <path to private key> ec2-user@<bastion public ip>:/path/to/file.dump /local/path/where/to/save/file.dump`
    E.g script `scp -i migration-fix.pem <EMAIL>:/home/<USER>/file.dump /Users/<USER>/Desktop/file.dump`
3. Copy snapshot from local to bastion server2 (db2)
    `scp -i <path to private key> /local/path/where/to/save/file.dump ec2-user@<bastion public ip>:/path/to/file.dump`
    E.g script `scp -i watt-bastion-host-staging.pem /Users/<USER>/Desktop/file.dump <EMAIL>:/home/<USER>/file.dump`
4. Restore the snapshot to db2
  `PGPASSWORD=mypassword pg_restore -h dev-quotation-db-20231129172809814000000001.c8ykldba3p5u.eu-west-2.rds.amazonaws.com -U myusername -d watt_portal -c -v "/home/<USER>/file.dump"`

## Useful commands

These are some useful commands to use when connected to the database. To run a sql command type `SELECT * FROM <table name>;` and press enter. Ensure you end the command with a semi-colon.

- \q: Quit psql
- \d: List all tables in the current database
- \dt: List all tables in the current schema
- \l: List all available databases
- \du: List all roles
- \dn: List all schemas
- \df: List all functions
- \dv: List all views
- \dS: List all sequences
- \d [table_name]: List the columns of a table
- \d [table_name].[column_name]: List the column definition
- \c [database_name]: Connect to a database
- \timing: Toggle timing of queries on and off
- \e: Open the current query buffer in the default editor
- \dp: List all grants and privileges
- \conninfo: Show information about the current database connection
- \i [filename]: Execute commands from a file

## Troubleshooting

- If you get `WARNING: UNPROTECTED PRIVATE KEY FILE!` use `chmod 600 <file_name>.pem`
- If you get an error saying `psql: error: could not connect to server: Connection refused` then the database is not running. You can check this by going to the AWS console > RDS > Databases > Database > Monitoring > Database Status.
- If you get an error saying `psql: error: could not connect to server: Connection timed out` then the database is running but the security group is not allowing the connection. You can check using telnet `sudo yum install -y telnet` and `telnet <database private ip> 5432` to check the port is open and the security group is correct.
- If you get an error saying `psql: error: could not connect to server: No route to host` then the database is running but the security group is not allowing the connection. You can check this by going to the AWS console > RDS > Databases > Database > Connectivity & Security > Security Groups.
- If you get an error saying `psql: error: could not connect to server: Operation timed out` then the database is running but the security group is not allowing the connection. You can check this by going to the AWS console > RDS > Databases > Database > Connectivity & Security > Security Groups.
