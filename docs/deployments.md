[<- Return to README](../README.md)

# Deployments

## Quote deployments

The [Quote service](../python/README.md) will automatically build and deploy any new commits to the `master` branch into the `dev` environment (i.e. any merged PRs).

This is handled by the Github Actions "Deploy to ECR" step. You can see how this works in the [`deploy-to-ecr.yml`](/.github/workflows/deploy-to-ecr.yml) workflow file.

### Deploying to prod

To deploy to prod you need to tag and push a commit in master.

```sh
git checkout master
git tag v0.1.26
git push origin v0.1.26

## Serverless Lambda function deployments

_If you are looking to deploy the **seeder** go to the next section below_

1. To deploy all lambda use `yarn deploy -s $(environment)` from the root of the `lambda/` mono repo.
2. To deploy a specifc lambda use `yarn workspace @watt/NAME deploy -s $(environment)`

## Seeder function deployment

You can read about how to deploy the seeder function in the [seeder README file](../seeder/README.md).
