[<- Return to README](../README.md)

# Requirements and Setup

Initial local setup instructions.

## Requirements

_These commands use `python`, if your install is something else, e.g. `python3` or `python3.9`, you must use that instead_

- [`docker` and `docker-compose`](https://docs.docker.com/get-docker/)
  - `docker-compose` is included with Docker Desktop on Mac and Windows
  - `brew install --cask docker` (MacOS)
  - `scoop bucket add main && scoop install docker` (Windows)
- [`pipenv`](https://docs.python-guide.org/dev/virtualenvs/)
  - `python -m pip install pipenv`
- [`yarn`](https://yarnpkg.com/)
  - `npm i -g yarn@1.19.1` (Open issue with Yarn 1.19.1+)
- [`aws`](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
  - `brew install awscli` (MacOS/Linux)
  - `scoop install aws` (Windows)
- [`hadolint`](https://github.com/hadolint/hadolint)
  - `brew install hadolint` (MacOS/Linux)
  - `scoop install hadolint` (Windows)

## Setup

_These commands use `python`, if your install is something else, e.g. `python3` or `python3.9`, you must use that instead_

- Run from the `/python/` folder run `pipenv --python 3.9` to create a new virtual environment
- Run `pipenv install --dev --verbose` to install the dependencies
- Run `make rebuild` from the project root
  - this will build and run the Docker containers
- Run `make db-setup` from the project root
  - this will create the local db (deleting it beforehand if needed)
  - run the [migrations](/python/docs/db_migrations.md)
  - run the [seeder](/seeder/README.md)

Docker listens for file changes to the `/python/` folder automatically.

If you want to get a fresh database (e.g. to clear the data you have accumulated in there) run `make db-setup` again.

### VS Code setup

If you are using VS Code, you need to set your Python interpreter correctly.

- Run the command `pipenv --venv` from the `/python/` folder
- Note the venv name that is displayed, something like `C:\Users\<USER>\.virtualenvs\python-0Mp9diZ2` or `/Users/<USER>/.local/share/virtualenvs/python-0Mp9diZ2`
    - the important part here is `python-0Mp9diZ2`
- In VS Code, open the _Command Palette_ (CTRL + SHIFT + P) and select **Python: Select Interpreter**
- Select the venv listed in the previous step, e.g. `python-0Mp9diZ2`
