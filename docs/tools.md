[&lt;- Return to README](../README.md)

# Tools

In order to start docker with the tools you need to run:

`docker compose -f docker-compose.yml -f docker-compose.local.yml up -d`

## Swagger

You can find **Swagger** documentation at

- http://localhost:8080/swagger/

## PDF Generation

Make sure the key/values are correct in `contract_template_list.ts`

To check a **specific** PDF template, update the **test_data.json's** _key name_ to _match a template key_. ie: _"smartest_electric"_

`cd lambda` and then run:

    -``yarn test:pdflocal``

If you've previously opened the PDF in VSCode (or in browser) and have regenerated the PDF -- ensure that you **REFRESH** your page to show changes.

If you want to read about the PDF field generation go to: [PDF README](../lambda/packages/pdf/README.md)

## Redis

You can inspect the local **Redis** instance using **Redis Commander**, which
can be viewed in a GUI at:

- http://localhost:6780/

The default login credentials are `root:qwerty`

You need to select `Db1` like so:

```
select 1
```

Then you will be able to see the list of keys in the instance and explore
further.

## pgAdmin (Postgres)

You can connect to the local **Postgres** instance using **pgAdmin**, which can
be viewed at:

- http://localhost:5050/

Login with credentials:

- username: `<EMAIL>`
- password: `root`

To connect to the local sever:

- Create a new connection
- Under _General_ set `Name` to `postgres`
- Under _Connection_:
  - set `Host name/address` to `host.docker.internal`
  - set `Username` to `postgres`
  - set `Password` to `postgres`

Data stored is in:

- Databases/watt_portal/Schemas/public/tables

These values come from `docker-compose.yml` (they are passed as environment
variables to the `postgres` image)

You can find data under Schemas > Tables. To view the content, right click the
table `View/Edit data` then `All Rows`.

## tfsec (vscode extension)

To be able to run tfsec extension you have to have tfsec installed:

For Mac:

- `brew install tfsec`

Linux:

- `curl -s https://raw.githubusercontent.com/aquasecurity/tfsec/master/scripts/install_linux.sh | bash`

Or install with GO:

- `go install github.com/aquasecurity/tfsec/cmd/tfsec@latest`

## MPAN puller script

We have a script that pulls MPANs from the Electralink API and groups them by profile class.
This is useful for testing the quote service with a large number of MPANs, of different
various profile classes.

You can run the script from the root of the repo with:

```shell
EL_API_KEY=123 EL_API_PASSWORD=456 node local-helpers/mpan_puller.js
```

Where `EL_API_KEY` and `EL_API_PASSWORD` are the credentials for the Electralink API.
