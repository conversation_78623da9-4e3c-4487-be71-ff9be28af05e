[<- Return to README](../README.md)

# Coding standards

(**todo**: (@jameskmonger) explain project structure)

(**todo**: (@jameskmonger) explain flake8 etc)

(**todo**: (@jameskmonger) explain DDD)

## AWS / Terraform Naming Conventions

The requirements for AWS resource names are:

- Length 1 - 63
- Lower case
- Separated by hyphens
- First character must be a letter
- Last character can't be a hyphen
