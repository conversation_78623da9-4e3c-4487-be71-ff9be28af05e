[<- Return to README](../README.md)

# Teardown

To remove the environment entirely, you can run `sh ./local-helpers/docker-clean.sh`

**WARNING:** If you have other things running in Docker, this will destroy them too.

If you encounter any odd leftovers, check the Docker GUI to see if there's anything hanging around. There are tools in the interface to help with deletion.

When setting the environment up again, instead of using the `./local-helpers/rebuild.sh` script, use the following command to build, just to be safe:

- `docker-compose build --no-cache && docker-compose up -d`

The `--no-cache` argument will prevent cached layers being used in the build.
