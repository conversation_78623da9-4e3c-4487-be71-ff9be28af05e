---
description: Best practices for building high-performance Sanic applications
globs: **/*.{py}
alwaysApply: false
---
- Use async/await for I/O-bound operations to leverage <PERSON><PERSON>'s async nature
- Implement proper error handling with custom exception handlers
- Use Sanic's built-in middleware for request/response processing
- Optimize performance by using <PERSON><PERSON>'s worker processes

---
name: sqlalchemy-best-practices.mdc
description: Best practices for using SQLAlchemy ORM in Python applications
globs: **/*.{py}
---

- Use declarative base for defining models
- Implement proper relationships between models
- Use bulk operations for improved performance on large datasets
- Utilize SQLAlchemy's query API for efficient database queries

---
name: pydantic-best-practices.mdc
description: Best practices for data validation and settings management with Pydantic
globs: **/*.{py}
---

- Use Pydantic models for request/response validation in API endpoints
- Implement custom validators for complex validation logic
- Use Pydantic's settings management for configuration
- Leverage Pydantic's type hints for improved code readability and maintainability

---
name: aiohttp-best-practices.mdc
description: Best practices for asynchronous HTTP client/server with aiohttp
globs: **/*.{py}
---

- Use async/await for handling concurrent requests
- Implement proper error handling and timeouts
- Use aiohttp's client session for improved performance
- Leverage aiohttp's middleware for request/response processing

---
name: pytest-best-practices.mdc
description: Best practices for writing and running tests with Pytest
globs: **/*.{py}
---

- Use fixtures for setup and teardown of test resources
- Implement parameterized tests for testing multiple scenarios
- Use Pytest's markers for categorizing and running specific tests
- Leverage Pytest's plugins for extended functionality (e.g., pytest-asyncio)

---
name: black-best-practices.mdc
description: Best practices for code formatting with Black
globs: **/*.{py}
---

- Use Black's default settings for consistent formatting across the project
- Integrate Black into your CI/CD pipeline for automated formatting checks
- Use Black's `--check` option for code review and pull request validation
- Combine Black with other tools like isort for comprehensive code formatting

---
name: pylint-best-practices.mdc
description: Best practices for code linting with Pylint
globs: **/*.{py}
---

- Customize Pylint configuration to suit your project's needs
- Use Pylint's `--disable` option to ignore specific warnings/errors
- Integrate Pylint into your CI/CD pipeline for automated code quality checks
- Use Pylint's plugins (e.g., pylint-pydantic) for extended linting capabilities