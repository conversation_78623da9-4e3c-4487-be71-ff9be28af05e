{"[python]": {"editor.tabSize": 4, "editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "never"}}, "[typescript][typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "never", "source.fixAll.eslint": "never"}}, "isort.check": true, "isort.importStrategy": "fromEnvironment", "isort.args": ["--settings-file", "./python/pyproject.toml"], "editor.detectIndentation": false, "python.linting.flake8Enabled": true, "python.linting.enabled": true, "python.linting.lintOnSave": true, "python.formatting.provider": "black", "python.autoComplete.extraPaths": ["./python"], "python.analysis.extraPaths": ["./python"], "pylint.args": ["--rcfile=./python/.pylintrc"], "python.languageServer": "<PERSON><PERSON><PERSON>", "python.testing.pytestEnabled": true, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.formatOnSave": true, "files.trimFinalNewlines": true, "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "search.exclude": {"**/.yarn": true, "**/.pnp.*": true}, "typescript.enablePromptUseWorkspaceTsdk": true, "eslint.nodePath": "lambda/.yarn/sdks", "prettier.prettierPath": "lambda/.yarn/sdks/prettier/index.js", "tfsec.excludedPaths": ["terraform/composition/.terraform", "terraform/composition/environments", "terraform/composition/scripts"], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}}