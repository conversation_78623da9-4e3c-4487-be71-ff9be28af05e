{"recommendations": ["ms-python.pylint", "ms-python.vscode-pylance", "ms-python.python", "ms-python.isort", "ms-python.flake8", "ms-python.black-formatter", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "arcanis.vscode-zipfs", "q.typescript-mono-repo-import-helper", "bungcip.better-toml", "ex<PERSON><PERSON>.had<PERSON>t", "vivaxy.vscode-conventional-commits", "hediet.vscode-drawio", "donjayamanne.git-extension-pack", "hashicorp.terraform", "infracost.infracost", "william-voyek.vscode-nginx", "redhat.vscode-yaml", "editorconfig.editorconfig", "tfsec.tfsec", "github.copilot"]}