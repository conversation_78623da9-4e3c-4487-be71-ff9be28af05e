# Infrastructure

<img src="./diagrams/watt architecture-Main architecture.drawio.png" />

Infrastructure diagram showing the environment composition. When making changes to the infrastructure as code please update the diagram.

## Requirements

Install the following tools.

- [`tfenv`](https://github.com/tfutils/tfenv)
- [`aws`](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
  - Follow the link or just use pip. `pip install awscli`
- [`make`](https://www.gnu.org/software/make/)
- [`infracost`](https://www.infracost.io/docs/)

## Structure

This section explains the structure of the terraform directory.

1. [Composition](./composition/README.md)

This is the main Quot App infrastructure, it makes use of the reusable `./modules/` directory to create the resourcess needed for the environment. See the below section `Quote App` on how to use the composition.

2. [Diagrams](./diagrams/README.md)

A static directory which contains the architecture diagrams for the infrastructure. These need to be updated manually.

3. [Lambda](./lambda/README.md)

Contains a basic javascript code base for the `cloudfront_interceptor` lambda function. It overcomes an issue when using S3 with React where the `index.html` file is not served correctly.

4. [Networking](./networking/README.md)

This is deployed into a separate AWS account and is used to create the Route53 hosted zone and the Route53 delegate role which allows the production and staging environments to attach their subdomain Route53 hosted zones to the root watt.co.uk domain. This approach is used to separate the networking from the main infrastructure and to allow the networking to be reused across multiple AWS accounts safely.

The network infrastricture is deployed into the `networking(************)` AWS account. This rarely needs updating but when it does you should run `terraform` CLI commands from within the `ntworking` directory. A make file has not been created for this directory.

1. [Remote state](./remote-state/README.md)

In order to use Terraform with multiple developers the remote state is stored in an S3 bucket. This allows the state to be shared and locked when multiple developers are working on the same environment. For this we use DynamoDB to track the state locks and S3 to store the terraform state files.

6. [Shared](./shard/README.md)

The CICD pipelines need authorised access to the AWS accounts. The safest way to do this is using OIDC which is located int he shared directory and deploted into the `shared-services(************)` AWS account. This sets up th IAM > Identity providers > `token.actions.githubusercontent.com`. This is used to create short lived tokens for the CICD pipelines to use using AWS STS.

## Quote App

The quote app is located in the `./composition/` directory and uses `make` to simplify the process for creating and maintaining environments. The code base uses `Terraform` and `Serverless Framework`.

### Setup

We use `Makefile` to simplify the commands, run `make help` from the `/terraform/composition/` folder to see the available commands. Add `s=dev` to set the stage to environment `dev`.

- Authenticate [AWS SSO](https://watt-development.awsapps.com/start#/) for command line or programatic acces for the account you want to work with.
- Run `make plan s=dev` to see what changes will be made to the infrastructure
- Run `make apply s=dev` to apply the changes, If there is an issue with the proposed changes use `CTRL+C` or type `no`. It is import you do this to disconnect the state lock.

### New environments

Ensure your AWS credentials are correctly set from AWS SSO. Making a new environment is done with a single command, this handles absolutely everything, just be patient as it can take a while.

Ensure you run `chmod +x aws-terraform-checks.sh create-new-environment-tfvars.sh deploy-lambdas.sh`

You only need to authenticate with the `watt-prod` or `watt-staging` account to create a new environment. The system automatically handles cross-account access to the `networking` account through IAM role assumption. The Terraform configuration uses the `TerraformStateManager` role to access the networking account's state and the `networking_route53_delegate_role_arn` to manage Route53 records in the root domain.

- Run `make new s=foo` to a create new environment `foo`
- Run `make destroy s=foo` **WARNING** this will destroy all resources in the environment.
- Run `make list` to see the list of environments

## Infrascost

[Infracost](https://www.infracost.io/docs/) provides a static analysis of the resources **price**, not to be confused with **cost** which is a dynamic analysis of the resources **usage**. The [Infracost Github Action](.github/../../.github/workflows/infracost.yml) will run on every pull request and will comment on the PR with the cost breakdown of the resources in the Terraform plan.

1. Run `cd terraform/ && make infracost` to see the cost breakdown of the resources in the Terraform plan.
2. Modify the usage file `.infracost-usage.yml` to see the **cost** breakdown of the resources in the Terraform plan with the new usage.

## AWS Authentication

You only need to authenticate with a single AWS account (either production or staging) to work with the infrastructure. The cross-account access to the networking account is handled automatically through IAM role assumption.

```ini
# Configure your AWS credentials for the environment you want to update
[default]
region=eu-west-2
sts_regional_endpoints=regional
aws_access_key_id=<access_key_id>
aws_secret_access_key=<secret_access_key>
aws_session_token=<session_token>
```

The Terraform configuration will:

1. Use your default credentials to authenticate with the staging/production account
2. Use the `TerraformStateManager` role to access the networking account's Terraform state
3. Use the `networking_route53_delegate_role_arn` (obtained from the networking state) to assume a role in the networking account for Route53 operations

This approach eliminates the need to manually authenticate with multiple AWS accounts.
