app_vpc_ip_cidr  = "INSERT_VPC_IP_CIDR_RANGE"
aws_account_name = "watt-staging"
environment      = "INSERT_ENVIRONMENT_NAME"
domain_name      = "watt.co.uk"
debug_mode       = false
bastion_enabled     = false
crm_private_api_key = ""


quotation_db_config = {
  db_name     = "watt_portal"
  db_port     = "5432"
  db_used     = "postgresql+asyncpg"
  db_user     = "superadmin"
  db_password = "_Bt2br4mFZ2+oQM?"

  instance_class        = "db.t3.small"
  storage_type          = "gp3"
  allocated_storage     = 20
  max_allocated_storage = 30

  iam_database_authentication_enabled = false
  multi_az                            = false

  storage_encrypted        = false
  deletion_protection      = false
  delete_automated_backups = false

  allow_major_version_upgrade = false
  auto_minor_version_upgrade  = false
  apply_immediately           = false
  maintenance_window          = "Wed:00:00-Wed:03:00"

  backup_retention_period = 0
  backup_window           = "03:00-06:00"
  skip_final_snapshot     = true

  performance_insights_enabled          = true
  performance_insights_retention_period = 7
  create_monitoring_role                = true
  monitoring_interval                   = 60
}

# TODO (<PERSON>): We need <NAME_EMAIL> and have it forward to us devs.
dlq_alarm_subscription_email = "<EMAIL>"
from_email_address           = "Watt DEV <<EMAIL>>"
contract_completion_email    = "ALL_DEVELOPERS"
quote_failure_email          = "ALL_DEVELOPERS"
