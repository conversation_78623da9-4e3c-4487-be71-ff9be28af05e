# Domain & DNS Management

This document explains <PERSON>'s DNS architecture, Route53 delegation strategy, and certificate management across multiple AWS accounts and environments.

## 🌐 DNS Architecture Overview

```mermaid
graph TB
    subgraph "Networking Account (************)"
        ROOT[watt.co.uk<br/>Root Hosted Zone]
    end

    subgraph "Production Account (************)"
        PROD_APP[app.watt.co.uk<br/>Production App]
        PROD_CRM[crm.watt.co.uk<br/>Production CRM]
    end

    subgraph "Staging Account (************)"
        DEV_APP[dev.app.watt.co.uk<br/>Development App]
        STAGING_APP[staging.app.watt.co.uk<br/>Staging App]
        DEV_CRM[dev.crm.watt.co.uk<br/>Development CRM]
    end

    ROOT -.->|NS Delegation| PROD_APP
    ROOT -.->|NS Delegation| PROD_CRM
    ROOT -.->|NS Delegation| DEV_APP
    ROOT -.->|NS Delegation| STAGING_APP
    ROOT -.->|NS Delegation| DEV_CRM
```

## 🏗️ Domain Strategy

### Domain Hierarchy

```
watt.co.uk (Root Domain - Networking Account)
├── app.watt.co.uk (Production Application)
├── crm.watt.co.uk (Production CRM)
├── dev.app.watt.co.uk (Development Application)
├── staging.app.watt.co.uk (Staging Application)
├── dev.crm.watt.co.uk (Development CRM)
└── [future].watt.co.uk (Future Services)
```

### Environment-Based Domain Mapping

| Environment | App Domain | CRM Domain | Account |
|-------------|------------|------------|---------|
| **Production** | `app.watt.co.uk` | `crm.watt.co.uk` | watt-prod |
| **Staging** | `staging.app.watt.co.uk` | `staging.crm.watt.co.uk` | watt-staging |
| **Development** | `dev.app.watt.co.uk` | `dev.crm.watt.co.uk` | watt-staging |

## 🔗 Route53 Delegation Process

### 1. Root Domain Setup (Networking Account)

The networking account hosts the authoritative DNS for `watt.co.uk`:

```hcl
# networking/main.tf
resource "aws_route53_zone" "watt_root" {
  name = "watt.co.uk"

  tags = {
    Environment = "shared"
    Purpose     = "root-domain"
  }
}

# Cross-account delegation role
resource "aws_iam_role" "route53_delegate_role" {
  name = "route53-delegate-role"

  assume_role_policy = jsonencode({
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        AWS = [
          "arn:aws:iam::************:root",  # watt-prod
          "arn:aws:iam::************:root"   # watt-staging
        ]
      }
    }]
  })
}

resource "aws_iam_role_policy" "route53_delegate_policy" {
  name = "route53-delegate-policy"
  role = aws_iam_role.route53_delegate_role.id

  policy = jsonencode({
    Statement = [{
      Effect = "Allow"
      Action = [
        "route53:ChangeResourceRecordSets",
        "route53:GetChange",
        "route53:ListResourceRecordSets"
      ]
      Resource = [
        aws_route53_zone.watt_root.arn,
        "arn:aws:route53:::change/*"
      ]
    }]
  })
}
```

### 2. Application Hosted Zone Creation

Each application environment creates its own hosted zone:

```hcl
# composition/main.tf
module "app_route53_hosted_zone" {
  source = "../modules/resource/networking/route53"

  domain_name = local.full_domain  # e.g., dev.app.watt.co.uk
  tags        = merge(module.common_data.common_tags, local.tags)
}

locals {
  # Environment-aware domain logic
  full_domain = var.environment == "prod" ? var.domain_name : "${var.environment}.${var.domain_name}"
  # For prod: app.watt.co.uk
  # For dev: dev.app.watt.co.uk
}
```

### 3. NS Record Delegation

The application accounts delegate their subdomains through the networking account:

```hcl
# composition/main.tf
module "app_route53_ns_record_delegation" {
  source = "../modules/infrastructure/route53_ns_record_delegation"

  # Name servers from the application hosted zone
  environments_name_servers = module.app_route53_hosted_zone.name_servers
  environments_domain_name  = local.full_domain

  # Cross-account access to networking account
  root_route53_hosted_zone_id          = data.terraform_remote_state.networking.outputs.root_route53_hosted_zone_id
  networking_route53_delegate_role_arn = data.terraform_remote_state.networking.outputs.networking_route53_delegate_role_arn
}
```

### 4. NS Record Delegation Module Implementation

```hcl
# modules/infrastructure/route53_ns_record_delegation/main.tf
resource "aws_route53_record" "ns_record" {
  provider = aws.networking

  zone_id = var.root_route53_hosted_zone_id
  name    = var.environments_domain_name
  type    = "NS"
  ttl     = "300"
  records = var.environments_name_servers
}

# Provider configuration for cross-account access
provider "aws" {
  alias = "networking"

  assume_role {
    role_arn     = var.networking_route53_delegate_role_arn
    session_name = "terraform-dns-delegation"
  }
}
```

## 🔒 Certificate Management

### ACM Certificate Strategy

Each account manages its own SSL/TLS certificates:

```hcl
# composition/main.tf
module "app_acm_certificate" {
  source  = "terraform-aws-modules/acm/aws"
  version = "4.3.2"

  domain_name = local.full_domain
  zone_id     = module.app_route53_hosted_zone.zone_id

  subject_alternative_names = [
    "*.${local.full_domain}"  # Wildcard for subdomains
  ]

  # DNS validation using the local hosted zone
  validation_method = "DNS"

  tags = merge(module.common_data.common_tags, local.tags)
}
```

### Certificate Validation Process

1. **Certificate Request**: ACM creates a certificate request
2. **DNS Validation Records**: ACM provides CNAME records for validation
3. **Automatic Validation**: Terraform creates validation records in the hosted zone
4. **Certificate Issuance**: ACM validates domain ownership and issues certificate

### Regional Certificate Considerations

For CloudFront distributions, certificates must be in `us-east-1`:

```hcl
# US East 1 certificate for CloudFront
module "cloudfront_certificate" {
  source  = "terraform-aws-modules/acm/aws"
  version = "4.3.2"

  providers = {
    aws = aws.us_east_1
  }

  domain_name = local.full_domain
  zone_id     = module.app_route53_hosted_zone.zone_id

  subject_alternative_names = ["*.${local.full_domain}"]
  validation_method         = "DNS"

  tags = merge(module.common_data.common_tags, local.tags)
}
```

## 🔧 DNS Configuration Examples

### Application Load Balancer DNS

```hcl
# A record pointing to Application Load Balancer
resource "aws_route53_record" "app" {
  zone_id = module.app_route53_hosted_zone.zone_id
  name    = local.full_domain
  type    = "A"

  alias {
    name                   = module.app_load_balancer.dns_name
    zone_id                = module.app_load_balancer.zone_id
    evaluate_target_health = true
  }
}
```

### CloudFront Distribution DNS

```hcl
# A record pointing to CloudFront distribution
resource "aws_route53_record" "cloudfront" {
  zone_id = module.app_route53_hosted_zone.zone_id
  name    = local.full_domain
  type    = "A"

  alias {
    name                   = module.quotation_cloudfront.distribution_domain_name
    zone_id                = module.quotation_cloudfront.distribution_hosted_zone_id
    evaluate_target_health = false
  }
}
```

### API Subdomain

```hcl
# API subdomain pointing to API Gateway
resource "aws_route53_record" "api" {
  zone_id = module.app_route53_hosted_zone.zone_id
  name    = "api.${local.full_domain}"
  type    = "A"

  alias {
    name                   = aws_api_gateway_domain_name.api.cloudfront_domain_name
    zone_id                = aws_api_gateway_domain_name.api.cloudfront_zone_id
    evaluate_target_health = false
  }
}
```

## 🔍 DNS Troubleshooting

### Common Issues and Solutions

#### 1. NS Record Delegation Failures

```bash
# Check NS records in root domain
dig NS dev.app.watt.co.uk @*******

# Verify delegation is working
dig A dev.app.watt.co.uk @*******
```

#### 2. Certificate Validation Issues

```bash
# Check DNS validation records
aws acm describe-certificate --certificate-arn <certificate-arn>

# Verify validation records exist
dig _<validation-record>.dev.app.watt.co.uk TXT
```

#### 3. Cross-Account Access Issues

```bash
# Test role assumption
aws sts assume-role --role-arn arn:aws:iam::************:role/route53-delegate-role --role-session-name test

# Check Route53 permissions
aws route53 list-hosted-zones-by-name --dns-name watt.co.uk
```

### DNS Propagation Checks

```bash
# Check global DNS propagation
dig +trace dev.app.watt.co.uk

# Check specific DNS servers
dig @******* dev.app.watt.co.uk
dig @******* dev.app.watt.co.uk
```

## 🚀 Best Practices

### Domain Naming Conventions

- **Production**: Use clean domains (`app.watt.co.uk`)
- **Non-Production**: Use environment prefixes (`dev.app.watt.co.uk`)
- **Services**: Use descriptive subdomains (`api.app.watt.co.uk`)

### Security Considerations

- **Certificate Rotation**: Use ACM for automatic certificate renewal
- **DNS Security**: Enable DNSSEC for additional security (future consideration)
- **Access Control**: Limit Route53 permissions to specific zones

### Operational Guidelines

- **TTL Settings**: Use appropriate TTL values (300s for NS records, 60s for A records)
- **Monitoring**: Set up CloudWatch alarms for certificate expiration
- **Documentation**: Maintain DNS record documentation for complex setups

### Automation Principles

- **Infrastructure as Code**: All DNS changes through Terraform
- **Validation**: Automated testing of DNS resolution
- **Rollback**: Maintain ability to quickly revert DNS changes

## 📋 DNS Record Types Reference

| Record Type | Purpose | Example |
|-------------|---------|---------|
| **A** | IPv4 address mapping | `app.watt.co.uk → *******` |
| **AAAA** | IPv6 address mapping | `app.watt.co.uk → 2001:db8::1` |
| **CNAME** | Canonical name alias | `www.app.watt.co.uk → app.watt.co.uk` |
| **NS** | Name server delegation | `dev.app.watt.co.uk → ns-xxx.awsdns-xxx.com` |
| **MX** | Mail exchange | `watt.co.uk → 10 mail.google.com` |
| **TXT** | Text records | `_acme-challenge.app.watt.co.uk → "validation-string"` |

This DNS architecture provides a scalable, secure, and manageable foundation for Watt's domain infrastructure while maintaining clear separation between environments and accounts.
