# Makefile Commands Reference

This document provides a comprehensive reference for all Makefile commands available in the Watt Terraform composition layer.

## 📋 Command Overview

The Makefile simplifies Terraform operations by providing environment-aware commands with built-in validation and security checks.

### Quick Reference

| Command | Purpose | Example |
|---------|---------|---------|
| `help` | Show available commands | `make help` |
| `init` | Initialize Terraform environment | `make init s=dev` |
| `plan` | Show planned changes | `make plan s=dev` |
| `apply` | Apply infrastructure changes | `make apply s=dev` |
| `destroy` | Destroy infrastructure | `make destroy s=dev` |
| `new` | Create new environment | `make new s=feature-branch` |
| `list` | List workspaces | `make list` |
| `validate` | Validate configuration | `make validate` |
| `sso` | AWS SSO authentication | `make sso p=watt-staging` |
| `tfsec` | Security scanning | `make tfsec` |
| `infracost` | Cost analysis | `make infracost` |

## 🔧 Core Commands

### `make help`

**Purpose**: Display all available commands with descriptions.

```bash
make help
```

**Output Example**:

```
Usage: make [command]

The available commands for execution are listed below.
The primary workflow commands are given first, followed by
less common or more advanced commands.

  apply                     Create or update infrastructure Usage: make init [s=stage] default dev
  init                      Setup the environment Usage: make init [s=stage] default dev
  plan                      Show changes required by the current configuration Usage: make init [s=stage] default dev
```

---

### `make init`

**Purpose**: Initialize Terraform for a specific environment.

```bash
make init [s=stage]
```

**Parameters**:

- `s` - Stage/environment name (default: `dev`)

**What it does**:

1. Runs AWS Terraform checks (`./scripts/aws-terraform-checks.sh`)
2. Initializes Terraform backend
3. Selects or creates workspace
4. Downloads providers and modules

**Examples**:

```bash
make init s=dev      # Initialize development environment
make init s=staging  # Initialize staging environment
make init s=prod     # Initialize production environment
make init            # Initialize development (default)
```

---

### `make plan`

**Purpose**: Show what changes Terraform will make without applying them.

```bash
make plan [s=stage]
```

**Parameters**:

- `s` - Stage/environment name (default: `dev`)

**What it does**:

1. Runs AWS Terraform checks
2. Creates a plan using environment-specific tfvars
3. Saves plan to `./plans/` directory
4. Exports plan as JSON for analysis
5. Runs security scans with checkov

**Examples**:

```bash
make plan s=dev      # Plan changes for development
make plan s=staging  # Plan changes for staging
make plan s=prod     # Plan changes for production
```

**Output Files**:

- `./plans/dev` - Binary plan file
- `./plans/dev.json` - JSON plan for analysis

---

### `make apply`

**Purpose**: Apply infrastructure changes to the specified environment.

```bash
make apply [s=stage]
```

**Parameters**:

- `s` - Stage/environment name (default: `dev`)

**What it does**:

1. Runs AWS Terraform checks
2. Applies changes using environment-specific tfvars
3. Prompts for confirmation unless auto-approved

**Examples**:

```bash
make apply s=dev      # Apply changes to development
make apply s=staging  # Apply changes to staging
make apply s=prod     # Apply changes to production
```

**⚠️ Safety Notes**:

- Always run `make plan` first
- Review the plan output carefully
- Use extra caution with production environments

---

### `make destroy`

**Purpose**: Destroy all infrastructure in the specified environment.

```bash
make destroy [s=stage]
```

**Parameters**:

- `s` - Stage/environment name (default: `dev`)

**What it does**:

1. Runs AWS Terraform checks
2. Plans destruction of all resources
3. Prompts for confirmation
4. Destroys all infrastructure

**Examples**:

```bash
make destroy s=dev      # Destroy development environment
make destroy s=staging  # Destroy staging environment
```

**⚠️ Danger Zone**:

- This command is **irreversible**
- All data will be lost
- Use with extreme caution
- Never use on production without team approval

---

## 🚀 Environment Management

### `make new`

**Purpose**: Create a completely new environment from scratch.

```bash
make new [s=stage]
```

**Parameters**:

- `s` - Stage/environment name (default: `dev`)

**What it does**:

1. Checks if workspace already exists
2. Runs AWS Terraform checks and configures workspace
3. Sets up Lambda placeholders
4. Creates new environment tfvars file
5. Applies Terraform configuration
6. Deploys Lambda functions

**Examples**:

```bash
make new s=feature-branch    # Create environment for feature development
make new s=hotfix           # Create environment for hotfix testing
make new s=experiment       # Create environment for experimentation
```

**Prerequisites**:

- Ensure scripts are executable: `chmod +x scripts/*.sh`
- AWS credentials configured
- Access to target AWS account

---

### `make list`

**Purpose**: List all Terraform workspaces (environments).

```bash
make list
```

**What it does**:

1. Runs AWS Terraform checks
2. Lists all available workspaces

**Example Output**:

```
  default
  dev
* staging
  prod
  feature-branch
```

The `*` indicates the currently selected workspace.

---

## 🔐 Authentication & Security

### `make sso`

**Purpose**: Authenticate with AWS using Single Sign-On.

```bash
make sso [p=profile]
```

**Parameters**:

- `p` - AWS profile name (default: `null`)

**Examples**:

```bash
make sso p=watt-staging    # Login to staging account
make sso p=watt-prod       # Login to production account
```

**What it does**:

1. Runs AWS SSO login flow
2. Caches credentials for CLI usage
3. Enables access to AWS resources

---

### `make tfsec`

**Purpose**: Run security scanning on Terraform configuration.

```bash
make tfsec
```

**What it does**:

1. Scans all `.tf` files for security issues
2. Excludes downloaded modules from scanning
3. Reports security violations and recommendations

**Example Output**:

```
Result #1 HIGH AWS VPC flow logs are not enabled
────────────────────────────────────────────────────────────────────────────────
  main.tf:15-25

   12 │ resource "aws_vpc" "main" {
   13 │   cidr_block           = var.cidr
   14 │   enable_dns_hostnames = true
   15 │   enable_dns_support   = true
```

---

### `make validate`

**Purpose**: Validate Terraform configuration syntax and consistency.

```bash
make validate
```

**What it does**:

1. Runs AWS Terraform checks
2. Validates syntax of all `.tf` files
3. Checks for missing variables and providers
4. Validates resource references

---

## 💰 Cost Analysis

### `make infracost`

**Purpose**: Generate cost estimates for infrastructure.

```bash
make infracost
```

**What it does**:

1. Analyzes Terraform configuration
2. Uses `infracost-usage.yml` for usage patterns
3. Generates cost breakdown report

**Example Output**:

```
Name                                                  Monthly Qty  Unit         Monthly Cost

aws_instance.app
├─ Instance usage (Linux/UNIX, on-demand, t3.medium)         730  hours              $30.37
└─ root_block_device
   └─ Storage (general purpose SSD, gp2)                       8  GB                  $0.80

aws_rds_instance.database
├─ Database instance (on-demand, Single-AZ, db.t3.small)     730  hours              $16.79
├─ Storage (general purpose SSD, gp2)                         20  GB                  $2.30
└─ Additional backup storage                                   20  GB                  $2.00

OVERALL TOTAL                                                                      $52.26
```

---

## 🔄 Parameter Usage

### Environment Parameter (`s=stage`)

The `s` parameter specifies which environment to work with:

```bash
# Environment-specific examples
make plan s=dev        # Uses environments/dev.tfvars
make plan s=staging    # Uses environments/staging.tfvars
make plan s=prod       # Uses environments/prod.tfvars
make plan s=my-feature # Uses environments/my-feature.tfvars (if exists)
```

### Profile Parameter (`p=profile`)

The `p` parameter specifies which AWS profile to use:

```bash
# Profile-specific examples
make sso p=watt-staging    # Login to staging account
make sso p=watt-prod       # Login to production account
make sso p=watt-sandbox    # Login to sandbox account
```

## 🛡️ Built-in Safety Features

### AWS Account Validation

Every command runs `./scripts/aws-terraform-checks.sh` which:

- Validates AWS credentials
- Checks account ID matches environment
- Ensures correct workspace selection
- Prevents cross-account mistakes

### State Management

- Automatic workspace selection based on environment
- Remote state backend configuration
- State locking with DynamoDB
- Backup and versioning enabled

### Security Scanning

- Checkov security scans on every plan
- tfsec scanning for security issues
- Infrastructure validation before apply

## 🚨 Error Handling

### Common Error Scenarios

1. **Authentication Failure**

   ```bash
   Error: NoCredentialProviders
   # Solution: make sso p=<profile>
   ```

2. **State Lock**

   ```bash
   Error: Error acquiring the state lock
   # Solution: Wait or use terraform force-unlock <lock-id>
   ```

3. **Workspace Issues**

   ```bash
   Error: Workspace "feature" doesn't exist
   # Solution: make new s=feature
   ```

### Debug Mode

Enable debug mode for verbose output:

```bash
DBG_MAKEFILE=1 make plan s=dev
```

This shows:

- Detailed command execution
- Timing information
- Debug output from scripts

## 📝 Best Practices

### Command Sequencing

1. Always start with authentication: `make sso`
2. Initialize environment: `make init s=<env>`
3. Plan before applying: `make plan s=<env>`
4. Review plan output carefully
5. Apply when ready: `make apply s=<env>`

### Environment Strategy

- Use `dev` for daily development work
- Use `staging` for integration testing
- Use `prod` only for production releases
- Create feature environments for major changes

### Safety Guidelines

- Never skip the plan step
- Always review security scan results
- Coordinate team changes on shared environments
- Use descriptive environment names for features

This Makefile provides a safe, consistent, and automated way to manage Watt's infrastructure across multiple environments and AWS accounts.
