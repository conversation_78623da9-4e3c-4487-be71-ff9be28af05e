# Best Practices

This document outlines the best practices for developing, deploying, and maintaining infrastructure at Watt. Following these guidelines ensures consistency, security, and reliability across our multi-account AWS environment.

## 🏗️ Infrastructure Design Principles

### 1. Modularity and Reusability

**Design for Reuse**

```hcl
# ✅ Good: Parameterized and reusable
module "vpc" {
  source = "../modules/infrastructure/networking"

  name        = var.vpc_name
  cidr        = var.vpc_cidr
  environment = var.environment
}

# ❌ Bad: Hardcoded values
resource "aws_vpc" "main" {
  cidr_block = "10.0.0.0/16"  # Hardcoded

  tags = {
    Name = "dev-vpc"  # Hardcoded environment
  }
}
```

**Single Responsibility Principle**

- Each module should have one clear purpose
- Avoid "kitchen sink" modules that do everything
- Keep module interfaces simple and focused

### 2. Environment Consistency

**Use Environment-Aware Configuration**

```hcl
# locals.tf
locals {
  name        = length(var.prefix) > 0 ? "${var.environment}-${var.prefix}" : var.environment
  full_domain = var.environment == "prod" ? var.domain_name : "${var.environment}.${var.domain_name}"

  common_tags = merge(var.tags, {
    Environment = var.environment
    Terraform   = "true"
    Repository  = "watt-terraform"
  })
}
```

**Environment-Specific Sizing**

```hcl
# Environment-appropriate resource sizing
locals {
  instance_size = {
    dev     = "t3.small"
    staging = "t3.medium"
    prod    = "t3.large"
  }

  database_config = {
    dev = {
      instance_class    = "db.t3.small"
      allocated_storage = 20
      multi_az         = false
    }
    prod = {
      instance_class    = "db.r5.large"
      allocated_storage = 100
      multi_az         = true
    }
  }
}
```

## 🔒 Security Best Practices

### 1. Principle of Least Privilege

**IAM Role Design**

```hcl
# ✅ Good: Specific permissions
resource "aws_iam_policy" "app_policy" {
  name        = "${var.name}-app-policy"
  description = "Policy for ${var.name} application"

  policy = jsonencode({
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ]
        Resource = [
          "${aws_s3_bucket.app_bucket.arn}/*"
        ]
      }
    ]
  })
}

# ❌ Bad: Overly broad permissions
resource "aws_iam_policy" "bad_policy" {
  policy = jsonencode({
    Statement = [{
      Effect   = "Allow"
      Action   = "*"        # Too broad
      Resource = "*"        # Too broad
    }]
  })
}
```

### 2. Secrets Management

**Use AWS Secrets Manager**

```hcl
# ✅ Good: Secrets in AWS Secrets Manager
resource "aws_secretsmanager_secret" "api_key" {
  name                    = "${var.environment}-api-key"
  description             = "API key for external service"
  recovery_window_in_days = 7
}

# Reference in application
data "aws_secretsmanager_secret_version" "api_key" {
  secret_id = aws_secretsmanager_secret.api_key.id
}

# ❌ Bad: Secrets in variables or code
variable "api_key" {
  type    = string
  default = "sk-1234567890abcdef"  # Never do this
}
```

### 3. Network Security

**Use Security Groups Defensively**

```hcl
# ✅ Good: Specific ingress rules
resource "aws_security_group" "app" {
  name_prefix = "${var.name}-app-"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]  # Specific source
  }

  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # HTTPS to internet
  }
}

# ❌ Bad: Overly permissive rules
resource "aws_security_group" "bad" {
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Too open
  }
}
```

## 📝 Code Quality Standards

### 1. Naming Conventions

**Resource Naming**

```hcl
# Pattern: {environment}-{component}-{resource-type}
resource "aws_instance" "app" {
  # Instance will be named: dev-quotation-app-instance
  tags = {
    Name = "${var.environment}-${var.component}-instance"
  }
}

# S3 bucket naming (must be globally unique)
resource "aws_s3_bucket" "app_data" {
  bucket = "${var.environment}-${var.component}-data-${random_id.bucket_suffix.hex}"
}
```

**Variable Naming**

```hcl
# ✅ Good: Descriptive and consistent
variable "quotation_db_config" {
  description = "Configuration for the quotation database"
  type = object({
    instance_class    = string
    allocated_storage = number
    multi_az         = bool
  })
}

# ❌ Bad: Vague or inconsistent
variable "db" {
  type = any  # Too generic
}
```

### 2. Documentation Standards

**Module Documentation**

```hcl
# modules/infrastructure/quotation_app/README.md
# Quotation Application Module

This module creates an ECS-based quotation application with:
- ECS service and task definition
- Application Load Balancer target group integration
- IAM roles and policies
- CloudWatch logging

## Usage

```hcl
module "quotation_app" {
  source = "../modules/infrastructure/quotation_app"

  name             = "my-app"
  vpc_id           = module.vpc.vpc_id
  private_subnets  = module.vpc.private_subnets
  target_group_arn = module.alb.target_group_arn
}
```

**Variable Documentation**

```hcl
variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be dev, staging, or prod."
  }
}

variable "app_vpc_ip_cidr" {
  description = "CIDR block for the application VPC. Should not overlap with other VPCs."
  type        = string
  default     = "********/16"

  validation {
    condition     = can(cidrhost(var.app_vpc_ip_cidr, 0))
    error_message = "The app_vpc_ip_cidr must be a valid CIDR block."
  }
}
```

### 3. File Organization

**Standard Module Structure**

```
modules/infrastructure/quotation_app/
├── main.tf              # Primary resources
├── variables.tf         # Input variables
├── outputs.tf           # Output values
├── versions.tf          # Provider requirements
├── iam.tf              # IAM resources (if substantial)
├── cloudwatch.tf       # Monitoring resources (if substantial)
├── README.md           # Module documentation
└── examples/
    └── basic/
        ├── main.tf
        └── README.md
```

## 🚀 Deployment Best Practices

### 1. Environment Progression

**Change Flow**

```mermaid
graph LR
    A[Local Dev] --> B[Dev Environment]
    B --> C[Staging Environment]
    C --> D[Production Environment]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#ffebee
```

**Testing Strategy**

1. **Local**: `terraform plan` and `terraform validate`
2. **Dev**: Full deployment and testing
3. **Staging**: Integration and performance testing
4. **Production**: Careful, monitored deployment

### 2. State Management

**Backend Configuration**

```hcl
# ✅ Good: Environment-specific backends
terraform {
  backend "s3" {
    bucket         = "watt-terraform-state-staging"
    key            = "environments/dev/terraform.tfstate"
    region         = "eu-west-2"
    encrypt        = true
    dynamodb_table = "terraform-state-locks"
  }
}
```

**State Safety**

- Never edit state files manually
- Use `terraform import` for existing resources
- Regular state backups and versioning
- Coordinate team changes to avoid conflicts

### 3. Change Management

**Pull Request Process**

1. Create feature branch: `git checkout -b feature/new-component`
2. Make changes and test locally
3. Deploy to dev environment for testing
4. Create pull request with:
   - Clear description of changes
   - Terraform plan output
   - Screenshots/testing evidence
5. Get code review and approval
6. Deploy to staging for integration testing
7. Merge to main and deploy to production

**Rollback Strategy**

```bash
# Keep previous plan files for rollback
make plan s=prod  # Creates plans/prod and plans/prod.json

# If rollback needed, can reference previous state
terraform apply plans/previous-known-good-plan
```

## 🔧 Operational Excellence

### 1. Monitoring and Alerting

**Resource Tagging for Monitoring**

```hcl
# Consistent tagging strategy
locals {
  common_tags = {
    Environment = var.environment
    Project     = "quotation-platform"
    Owner       = "platform-team"
    Terraform   = "true"
    CostCenter  = var.cost_center
    Backup      = var.backup_required ? "daily" : "none"
  }
}

resource "aws_instance" "app" {
  # ... configuration ...

  tags = merge(local.common_tags, {
    Name      = "${var.environment}-quotation-app"
    Component = "application"
    Role      = "web-server"
  })
}
```

**CloudWatch Integration**

```hcl
# Application-level monitoring
resource "aws_cloudwatch_log_group" "app" {
  name              = "/aws/ecs/${var.name}"
  retention_in_days = var.environment == "prod" ? 30 : 7

  tags = local.common_tags
}

# Cost monitoring
resource "aws_budgets_budget" "environment" {
  name         = "${var.environment}-monthly-budget"
  budget_type  = "COST"
  limit_amount = var.monthly_budget_limit
  limit_unit   = "USD"
  time_unit    = "MONTHLY"

  cost_filters = {
    Tag = ["Environment:${var.environment}"]
  }
}
```

### 2. Performance Optimization

**Right-Sizing Resources**

```hcl
# Environment-appropriate sizing
locals {
  performance_config = {
    dev = {
      min_capacity = 1
      max_capacity = 2
      cpu_target   = 50
    }
    staging = {
      min_capacity = 1
      max_capacity = 3
      cpu_target   = 60
    }
    prod = {
      min_capacity = 2
      max_capacity = 10
      cpu_target   = 70
    }
  }
}

resource "aws_appautoscaling_target" "app" {
  max_capacity       = local.performance_config[var.environment].max_capacity
  min_capacity       = local.performance_config[var.environment].min_capacity
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.app.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}
```

### 3. Cost Optimization

**Resource Lifecycle Management**

```hcl
# S3 lifecycle for cost optimization
resource "aws_s3_bucket_lifecycle_configuration" "app_data" {
  bucket = aws_s3_bucket.app_data.id

  rule {
    id     = "transition_to_ia"
    status = "Enabled"

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 90
      storage_class = "GLACIER"
    }
  }
}

# Spot instances for non-production
resource "aws_instance" "worker" {
  count = var.environment != "prod" ? var.worker_count : 0

  instance_type = "t3.medium"
  spot_price    = "0.05"  # Use spot instances in dev/staging

  tags = merge(local.common_tags, {
    Name = "${var.environment}-worker-${count.index}"
  })
}
```

## 🤝 Team Collaboration

### 1. Code Review Guidelines

**What to Review**

- [ ] Security implications of changes
- [ ] Resource sizing appropriateness
- [ ] Naming consistency
- [ ] Documentation updates
- [ ] Environment-specific impacts
- [ ] Cost implications
- [ ] Backup and recovery considerations

**Review Checklist**

```markdown
## Infrastructure Change Review

### Security
- [ ] No hardcoded secrets or credentials
- [ ] IAM permissions follow least privilege
- [ ] Security groups are appropriately restrictive
- [ ] Data encryption in transit and at rest

### Operations
- [ ] Appropriate monitoring and alerting
- [ ] Resource tagging is complete and consistent
- [ ] Change is reversible
- [ ] Documentation is updated

### Cost
- [ ] Resource sizing is appropriate for environment
- [ ] No unnecessary premium features in dev/staging
- [ ] Lifecycle policies are configured where appropriate
```

### 2. Communication Standards

**Change Notifications**

- Announce infrastructure changes in team channels
- Schedule production changes during maintenance windows
- Provide rollback plans for significant changes
- Document any temporary workarounds

**Documentation Maintenance**

- Update README files when modules change
- Keep architecture diagrams current
- Document any manual intervention required
- Maintain runbooks for common operations

## 🔄 Continuous Improvement

### 1. Regular Reviews

**Monthly Infrastructure Review**

- Review cost trends and optimization opportunities
- Assess security posture and compliance
- Evaluate performance metrics
- Plan capacity and scaling needs

**Quarterly Architecture Review**

- Assess module design and reusability
- Review cross-cutting concerns
- Plan technology upgrades
- Evaluate new AWS services

### 2. Automation Opportunities

**Identify Manual Processes**

- Look for repetitive manual tasks
- Automate testing and validation
- Implement self-healing capabilities
- Automate backup and recovery procedures

**Tool Integration**

- Integrate security scanning in CI/CD
- Automate cost reporting
- Implement automated testing for infrastructure
- Use infrastructure testing frameworks

By following these best practices, we ensure that our infrastructure is secure, reliable, cost-effective, and maintainable while supporting the team's development velocity and operational requirements.
