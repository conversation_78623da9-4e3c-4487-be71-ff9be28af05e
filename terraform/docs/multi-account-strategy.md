# Multi-Account Strategy

This document explains <PERSON>'s multi-account AWS architecture, which provides security boundaries, cost isolation, and operational separation between different environments and services.

## 🏢 Account Architecture Overview

```mermaid
graph TB
    subgraph "Management & Security"
        SHARED[shared-services<br/>************<br/>OIDC, IAM]
        LOG[log-archive<br/>************<br/>Centralized Logging]
    end

    subgraph "Networking & DNS"
        NET[networking<br/>************<br/>Route53 Root Domain]
    end

    subgraph "Application Environments"
        PROD[watt-prod<br/>************<br/>Production Apps]
        STAGING[watt-staging<br/>************<br/>Dev/Staging Apps]
    end

    subgraph "Supporting Services"
        SANDBOX[aws-sandbox<br/>************<br/>Experimentation]
        TFSTATE_PROD[watt-prod-tfstate<br/>************<br/>Prod State Storage]
        TFSTATE_STAGING[watt-staging-tfstate<br/>************<br/>Staging State Storage]
    end

    SHARED -.->|OIDC Auth| PROD
    SHARED -.->|OIDC Auth| STAGING
    NET -.->|Route53 Delegation| PROD
    NET -.->|Route53 Delegation| STAGING
    PROD -.->|Cross-account State| TFSTATE_PROD
    STAGING -.->|Cross-account State| TFSTATE_STAGING
```

## 📋 Account Details

| Account Name | Account ID | Primary Purpose | Access Pattern |
|--------------|------------|-----------------|----------------|
| **networking** | ************ | Root Route53 hosted zone for `watt.co.uk` | Cross-account delegation roles |
| **watt-prod** | ************ | Production applications and data | Restricted access, monitoring |
| **watt-staging** | ************ | Development and staging environments | Developer access |
| **shared-services** | ************ | OIDC provider, shared IAM roles | Service account access |
| **watt-prod-tfstate** | ************ | Production Terraform state storage | Backend access only |
| **watt-staging-tfstate** | ************ | Staging/dev Terraform state storage | Backend access only |
| **log-archive** | ************ | Centralized logging and audit trails | Read-only for compliance |
| **aws-sandbox** | ************ | Experimentation and testing | Unrestricted for learning |

## 🔐 Security Boundaries

### Account Separation Benefits

1. **Blast Radius Limitation**: Issues in one account don't affect others
2. **Cost Isolation**: Clear cost attribution per environment
3. **Access Control**: Fine-grained permissions based on account boundaries
4. **Compliance**: Separate production data from development activities
5. **Resource Limits**: Independent service quotas and limits

### Cross-Account Access Patterns

#### 1. Route53 DNS Delegation

```hcl
# In networking account - creates delegation role
resource "aws_iam_role" "route53_delegate_role" {
  name = "route53-delegate-role"

  assume_role_policy = jsonencode({
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        AWS = [
          "arn:aws:iam::************:root",  # watt-prod
          "arn:aws:iam::************:root"   # watt-staging
        ]
      }
    }]
  })
}

# In application accounts - assumes delegation role
provider "aws" {
  alias = "networking"

  assume_role {
    role_arn     = data.terraform_remote_state.networking.outputs.networking_route53_delegate_role_arn
    session_name = "terraform-dns-delegation"
  }
}
```

#### 2. Terraform State Cross-Account Access

```hcl
# Remote state configuration for cross-account access
data "terraform_remote_state" "networking" {
  backend = "s3"

  config = {
    bucket       = "tf-remote-state-20220826095408330800000001"
    region       = "eu-west-2"
    key          = "networking/terraform.tfstate"
    role_arn     = "arn:aws:iam::************:role/TerraformStateManager"
    session_name = "provisioner"
  }
}
```

#### 3. GitHub Actions OIDC Authentication

```hcl
# In shared-services account
resource "aws_iam_openid_connect_provider" "github" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["6938fd4d98bab03faadb97b34396831e3780aea1"]
}

# Cross-account role assumption for CI/CD
resource "aws_iam_role" "github_actions_prod" {
  name = "github-actions-prod-role"

  assume_role_policy = jsonencode({
    Statement = [{
      Action = ["sts:AssumeRole", "sts:TagSession"]
      Effect = "Allow"
      Principal = {
        Federated = aws_iam_openid_connect_provider.github.arn
      }
      Condition = {
        StringEquals = {
          "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          "token.actions.githubusercontent.com:sub" = "repo:watt-org/infrastructure:ref:refs/heads/main"
        }
      }
    }]
  })
}
```

## 🌐 Domain and DNS Strategy

### Domain Hierarchy

```
watt.co.uk (networking account)
├── app.watt.co.uk (production)
├── dev.app.watt.co.uk (development)
├── staging.app.watt.co.uk (staging)
├── crm.watt.co.uk (production CRM)
├── dev.crm.watt.co.uk (development CRM)
└── *.watt.co.uk (future services)
```

### DNS Delegation Flow

1. **Root Domain**: `watt.co.uk` hosted zone in networking account
2. **Application Domains**: Each environment creates its own hosted zone
3. **NS Record Delegation**: Application accounts create NS records in root domain
4. **Certificate Management**: Each account manages its own ACM certificates

### Implementation Pattern

```hcl
# Step 1: Create hosted zone in application account
module "app_route53_hosted_zone" {
  source = "../modules/resource/networking/route53"

  domain_name = local.full_domain  # e.g., dev.app.watt.co.uk
}

# Step 2: Delegate from root domain in networking account
module "route53_ns_record_delegation" {
  source = "../modules/infrastructure/route53_ns_record_delegation"

  environments_name_servers       = module.app_route53_hosted_zone.name_servers
  environments_domain_name        = local.full_domain
  root_route53_hosted_zone_id     = data.terraform_remote_state.networking.outputs.root_route53_hosted_zone_id
  networking_route53_delegate_role_arn = data.terraform_remote_state.networking.outputs.networking_route53_delegate_role_arn
}
```

## 🔄 State Management Strategy

### State Storage Accounts

- **Separation**: Production and staging use different state storage accounts
- **Security**: State buckets have restrictive access policies
- **Backup**: Versioning and replication enabled
- **Encryption**: Server-side encryption with KMS

### State Access Pattern

```hcl
# Backend configuration for production
terraform {
  backend "s3" {
    bucket         = "prod-terraform-state-bucket"
    key            = "environments/prod/terraform.tfstate"
    region         = "eu-west-2"
    encrypt        = true
    dynamodb_table = "terraform-state-locks"
    role_arn       = "arn:aws:iam::************:role/TerraformStateManager"
  }
}
```

## 🚀 Deployment Patterns

### Environment Deployment Strategy

1. **Development**: Deploy to `watt-staging` account with `dev` environment
2. **Staging**: Deploy to `watt-staging` account with `staging` environment
3. **Production**: Deploy to `watt-prod` account with `prod` environment

### Cross-Account Deployment Workflow

```bash
# Authenticate with target account
aws sso login --profile watt-staging

# Deploy to specific environment
cd terraform/composition
make plan s=dev
make apply s=dev
```

### Account-Specific Configurations

```hcl
# Account-specific settings in tfvars
# dev.tfvars (deployed to watt-staging)
aws_account_name = "watt-staging"
environment      = "dev"
domain_name      = "watt.co.uk"

# prod.tfvars (deployed to watt-prod)
aws_account_name = "watt-prod"
environment      = "prod"
domain_name      = "app.watt.co.uk"
```

## 🛡️ Security Best Practices

### Principle of Least Privilege

- Account-specific IAM roles with minimal permissions
- Time-bounded access through AWS SSO
- Regular access reviews and rotation

### Network Isolation

- Separate VPCs in each account
- No cross-account network connectivity by default
- VPC endpoints for AWS service access

### Data Protection

- Account boundaries prevent accidental data access
- Production data never accessible from development
- Audit trails for all cross-account access

### Monitoring and Compliance

- CloudTrail in each account forwarding to log-archive
- AWS Config for compliance monitoring
- Cross-account alerting for security events

## 🔧 Operational Considerations

### Cost Management

- **Cost Allocation**: Clear cost attribution per account
- **Budgets**: Account-level budget alerts
- **Reserved Instances**: Account-specific RI planning

### Backup and Recovery

- **Account-specific**: Backup strategies per environment
- **Cross-region**: Replication for production accounts
- **Testing**: Regular disaster recovery testing

### Monitoring

- **Centralized**: Logs forwarded to log-archive account
- **Distributed**: Account-specific monitoring dashboards
- **Alerting**: Environment-appropriate alerting thresholds

## 🎯 Adding New Accounts

When adding new AWS accounts to the organization:

1. **Update account_ids map** in `modules/common_data/variables.tf`
2. **Configure cross-account roles** for necessary access patterns
3. **Update Route53 delegation** if DNS is required
4. **Configure state backend** if Terraform is used
5. **Update CI/CD permissions** for deployment access
6. **Document** the account purpose and access patterns

This multi-account strategy provides a robust foundation for secure, scalable, and manageable infrastructure operations while maintaining clear boundaries between environments and services.
