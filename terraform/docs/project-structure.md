# Project Structure

This document explains the three-layer architecture that provides clear separation of concerns and promotes reusability across the Watt Terraform infrastructure.

## 🏗️ Three-Layer Architecture

```
terraform/
├── composition/                # Layer 3: Environment Orchestration
│   ├── main.tf                # Orchestrates infrastructure modules
│   ├── environments/          # Environment-specific configurations
│   │   ├── dev.tfvars
│   │   ├── staging.tfvars
│   │   └── prod.tfvars
│   └── Makefile               # Workflow automation
├── modules/
│   ├── infrastructure/        # Layer 2: Application Components
│   │   ├── networking/        # VPC, Route53, ACM
│   │   ├── quotation_app/     # ECS application
│   │   ├── quotation_db/      # RDS database
│   │   └── ...
│   └── resource/              # Layer 1: AWS Resources
│       ├── compute/           # EC2, ECS, Lambda
│       ├── networking/        # VPC components
│       └── storage/           # S3, EBS
├── networking/                # Cross-account Route53 management
├── shared/                    # OIDC and shared services
└── docs/                      # Documentation
```

## Layer 1: Resource Modules (`modules/resource/`)

**Purpose**: Define reusable AWS resources with minimal business logic.

**Characteristics**:

- Single AWS resource or tightly coupled resources
- Generic and reusable across different applications
- No environment-specific logic
- Focus on AWS best practices and security defaults

**Example Structure**:

```
modules/resource/compute/ec2/
├── main.tf                    # EC2 instance resource
├── variables.tf               # Input parameters
├── outputs.tf                 # Resource attributes
└── README.md                  # Module documentation
```

**When to Create**:

- New AWS resource not covered by existing modules
- Need custom configuration beyond community modules
- Standardizing resource configuration across teams

## Layer 2: Infrastructure Modules (`modules/infrastructure/`)

**Purpose**: Combine resources to create application-focused components.

**Characteristics**:

- Business logic and application requirements
- Combine multiple AWS resources
- Environment-aware configurations
- Specific to application architecture

**Example - Quotation App Module**:

```
modules/infrastructure/quotation_app/
├── main.tf                    # ECS service, task definition, IAM
├── variables.tf               # Application configuration
├── outputs.tf                 # Service endpoints, ARNs
├── iam.tf                     # Application-specific permissions
├── cloudwatch.tf              # Logging and monitoring
└── README.md                  # Module documentation
```

**Key Modules**:

### networking

- **Purpose**: VPC, subnets, Route53 hosted zones, ACM certificates
- **Outputs**: VPC ID, subnet IDs, zone IDs, certificate ARNs
- **Dependencies**: None (foundational)

### quotation_app

- **Purpose**: ECS-based application deployment
- **Outputs**: Service name, task definition ARN
- **Dependencies**: networking, quotation_db, app_load_balancer

### quotation_db

- **Purpose**: RDS PostgreSQL with security groups
- **Outputs**: Database endpoint, security group ID
- **Dependencies**: networking

### app_load_balancer

- **Purpose**: Application Load Balancer with target groups
- **Outputs**: Target group ARN, DNS name
- **Dependencies**: networking

### quotation_cloudfront

- **Purpose**: CloudFront distribution for caching
- **Outputs**: Distribution domain name
- **Dependencies**: app_load_balancer

### messaging

- **Purpose**: SQS queues, SNS topics, EventBridge rules
- **Outputs**: Queue URLs, topic ARNs
- **Dependencies**: networking

## Layer 3: Composition (`composition/`)

**Purpose**: Orchestrate infrastructure modules to create complete environments.

**Characteristics**:

- Environment-specific configurations
- Module integration and data flow
- Cross-module dependencies
- Shared configuration management

**Key Files**:

### main.tf

Orchestrates all infrastructure modules:

```hcl
module "app_vpc" {
  source = "../modules/infrastructure/networking"

  name        = "${local.name}-app-vpc"
  domain_name = local.full_domain
  cidr        = var.app_vpc_ip_cidr
  # ... configuration
}

module "quotation_app" {
  source = "../modules/infrastructure/quotation_app"

  name                = "${local.name}-quotation-app"
  vpc_id              = module.app_vpc.vpc_id
  target_group_arn    = module.app_load_balancer.target_group_arn
  # ... configuration

  depends_on = [module.app_vpc]
}
```

### environments/

Environment-specific variable files:

- `dev.tfvars` - Development configuration
- `staging.tfvars` - Staging configuration
- `prod.tfvars` - Production configuration

### locals.tf

Environment-aware naming and domain logic:

```hcl
locals {
  name = length(var.prefix) > 0 ? "${var.environment}-${var.prefix}" : "${var.environment}"
  full_domain = var.environment == "prod" ? var.domain_name : "${var.environment}.${var.domain_name}"
}
```

## 🔗 Module Dependencies

```mermaid
graph TD
    A[networking] --> B[app_load_balancer]
    A --> C[quotation_db]
    A --> D[bastion]
    A --> E[messaging]

    B --> F[quotation_app]
    C --> F
    E --> F

    B --> G[quotation_cloudfront]

    H[secrets] --> F
```

**Dependency Guidelines**:

1. **networking** is foundational - no dependencies
2. **Database and Load Balancer** depend on networking
3. **Application** depends on database, load balancer, and networking
4. **CloudFront** depends on load balancer
5. **Secrets** can be consumed by any module needing credentials

## 📁 File Organization Standards

### Module Structure

Every module should follow this structure:

```
module_name/
├── main.tf                    # Primary resources
├── variables.tf               # Input variables
├── outputs.tf                 # Output values
├── versions.tf                # Provider requirements
├── README.md                  # Module documentation
├── examples/                  # Usage examples
│   └── basic/
│       ├── main.tf
│       └── README.md
└── tests/                     # Automated tests
    └── basic_test.go
```

### Variable Naming

- Use descriptive names: `quotation_db_config` not `db_config`
- Group related variables with prefixes: `vpc_cidr`, `vpc_azs`
- Use objects for complex configurations:

```hcl
variable "quotation_db_config" {
  type = object({
    instance_class    = string
    allocated_storage = number
    multi_az         = bool
  })
}
```

### Output Naming

- Include resource type: `vpc_id`, `subnet_ids`, `security_group_id`
- Use plural for lists: `public_subnet_ids`, `private_subnet_ids`
- Include purpose when ambiguous: `app_security_group_id`

## 🔄 Data Flow

1. **Environment Variables** (`*.tfvars`) provide environment-specific configuration
2. **Composition** (`main.tf`) orchestrates modules with shared configuration
3. **Infrastructure Modules** combine resources with business logic
4. **Resource Modules** create individual AWS resources
5. **Outputs** flow back up through the layers for consumption

## 🎯 Best Practices

### Module Development

1. **Single Responsibility**: Each module has one clear purpose
2. **Loose Coupling**: Minimize dependencies between modules
3. **High Cohesion**: Related resources stay together
4. **Interface Segregation**: Only expose necessary outputs

### Naming Conventions

- **Resources**: `{environment}-{component}-{resource_type}`
- **Modules**: Use descriptive names matching their purpose
- **Variables**: Use snake_case with descriptive prefixes

### Version Management

- Pin module versions in composition
- Use semantic versioning for custom modules
- Test modules independently before composition changes

This structure ensures that changes at the resource level don't impact application logic, and application changes don't require infrastructure rewrites, providing a stable foundation for the Watt platform.
