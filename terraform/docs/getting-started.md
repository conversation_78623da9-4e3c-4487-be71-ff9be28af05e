# Getting Started

This guide will help you set up your development environment and make your first infrastructure changes to the Watt platform.

## 🚀 Prerequisites

### Required Tools

1. **AWS CLI v2**

   ```bash
   # macOS
   brew install awscli

   # Linux
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   ```

2. **Terraform** (managed via tfenv)

   ```bash
   # Install tfenv
   brew install tfenv

   # Install Terraform (version specified in .terraform-version)
   cd terraform/composition
   tfenv install
   tfenv use
   ```

3. **Make**

   ```bash
   # macOS (usually pre-installed)
   xcode-select --install

   # Linux
   sudo apt-get install build-essential  # Ubuntu/Debian
   sudo yum groupinstall "Development Tools"  # CentOS/RHEL
   ```

4. **Infracost** (optional, for cost analysis)

   ```bash
   # macOS
   brew install infracost/brew/infracost

   # Linux
   curl -fsSL https://raw.githubusercontent.com/infracost/infracost/master/scripts/install.sh | sh
   ```

### Verify Installation

```bash
aws --version          # Should be v2.x.x
terraform version      # Should match .terraform-version
make --version         # Should be 3.x.x or higher
infracost --version    # Should be 0.x.x
```

## 🔐 AWS Authentication Setup

### 1. AWS SSO Configuration

Configure your AWS CLI for SSO access:

```bash
# Configure AWS SSO
aws configure sso --profile watt-staging

# Follow the prompts:
# SSO start URL: https://watt-development.awsapps.com/start
# SSO region: eu-west-2
# Account ID: ************ (for staging) or ************ (for prod)
# Role name: AdministratorAccess
# CLI default region: eu-west-2
# CLI default output format: json
```

### 2. Test Authentication

```bash
# Login to AWS
aws sso login --profile watt-staging

# Verify access
aws sts get-caller-identity --profile watt-staging

# Expected output:
# {
#     "UserId": "AIDACKCEVSQ6C2EXAMPLE",
#     "Account": "************",
#     "Arn": "arn:aws:sts::************:assumed-role/AdministratorAccess/<EMAIL>"
# }
```

### 3. Set Default Profile (Optional)

```bash
# Set environment variable for session
export AWS_PROFILE=watt-staging

# Or add to your shell profile
echo 'export AWS_PROFILE=watt-staging' >> ~/.zshrc  # or ~/.bashrc
source ~/.zshrc
```

## 🏗️ Project Structure Overview

```
terraform/
├── composition/          # 🎯 Your main working directory
│   ├── Makefile         # Commands you'll use daily
│   ├── main.tf          # Infrastructure orchestration
│   ├── environments/    # Environment configurations
│   │   ├── dev.tfvars
│   │   ├── staging.tfvars
│   │   └── prod.tfvars
│   └── scripts/         # Helper scripts
├── modules/
│   ├── infrastructure/ # Application-level modules
│   └── resource/       # Low-level AWS resources
├── networking/         # Cross-account DNS management
├── shared/            # OIDC and shared services
└── docs/              # Documentation (you are here!)
```

## 🛠️ Your First Infrastructure Change

### 1. Navigate to Composition Directory

```bash
cd terraform/composition
```

### 2. Understand Available Commands

```bash
make help
```

You'll see output like:

```
Usage: make [command]

The available commands for execution are listed below.
The primary workflow commands are given first, followed by
less common or more advanced commands.

  apply                     Create or update infrastructure Usage: make init [s=stage] default dev
  init                      Setup the environment Usage: make init [s=stage] default dev
  plan                      Show changes required by the current configuration Usage: make init [s=stage] default dev
```

### 3. Initialize Your Environment

```bash
# Initialize for development environment
make init s=dev

# This will:
# - Check AWS credentials and account access
# - Initialize Terraform with the correct backend
# - Select or create the 'dev' workspace
# - Download required providers and modules
```

### 4. Plan Your Changes

Before making any changes, always see what Terraform plans to do:

```bash
make plan s=dev
```

This will:

- Show you what resources would be created, modified, or destroyed
- Validate your configuration
- Create a plan file for review
- Run security scans with checkov

### 5. Apply Changes (When Ready)

```bash
make apply s=dev
```

**⚠️ Important**: Only apply after reviewing the plan output carefully!

## 🔄 Common Workflows

### Development Workflow

1. **Start your day**: Login to AWS

   ```bash
   aws sso login --profile watt-staging
   ```

2. **Check current state**: Plan before making changes

   ```bash
   cd terraform/composition
   make plan s=dev
   ```

3. **Make changes**: Edit `.tf` files or `environments/dev.tfvars`

4. **Test changes**: Always plan before applying

   ```bash
   make plan s=dev
   ```

5. **Apply changes**: If plan looks good

   ```bash
   make apply s=dev
   ```

### Environment-Specific Operations

```bash
# Work with different environments
make plan s=dev        # Development
make plan s=staging    # Staging
make plan s=prod       # Production (be careful!)

# Create a new environment
make new s=my-feature  # Creates a new environment named "my-feature"

# List all environments
make list
```

### Security and Validation

```bash
# Run security scans
make tfsec

# Validate configuration
make validate

# Check costs
make infracost
```

## 📝 Making Your First Change

Let's make a simple, safe change to understand the workflow:

### 1. Edit Environment Variables

Open `environments/dev.tfvars` and modify a safe variable:

```hcl
# Change debug mode
debug_mode = true  # was false
```

### 2. Plan the Change

```bash
make plan s=dev
```

You should see output showing the change to the debug_mode variable.

### 3. Apply the Change

```bash
make apply s=dev
```

### 4. Verify the Change

Check that your change was applied by viewing the Terraform state or AWS console.

## 🌍 Working with Different Environments

### Environment Mapping

| Environment | Domain | AWS Account | Purpose |
|-------------|--------|-------------|---------|
| `dev` | `dev.app.watt.co.uk` | watt-staging | Your development work |
| `staging` | `staging.app.watt.co.uk` | watt-staging | Integration testing |
| `prod` | `app.watt.co.uk` | watt-prod | Production (restricted access) |

### Account Authentication

```bash
# For dev and staging environments
aws sso login --profile watt-staging

# For production (restricted access)
aws sso login --profile watt-prod
```

### Environment-Specific Configurations

Each environment has its own configuration in `environments/`:

```bash
# Development configuration
cat environments/dev.tfvars

# Production configuration
cat environments/prod.tfvars
```

## 🔍 Troubleshooting Common Issues

### 1. Authentication Issues

**Problem**: `Error: NoCredentialProviders`

**Solution**:

```bash
# Check if you're logged in
aws sts get-caller-identity

# If not, login again
aws sso login --profile watt-staging
```

### 2. Backend Access Issues

**Problem**: `Error: Failed to get existing workspaces`

**Solution**:

```bash
# Ensure you're in the right directory
cd terraform/composition

# Verify backend configuration
terraform init
```

### 3. State Lock Issues

**Problem**: `Error: Error acquiring the state lock`

**Solution**:

```bash
# Check who has the lock (if it's stuck)
terraform force-unlock <lock-id>

# Only use force-unlock if you're sure no one else is applying changes!
```

### 4. Cross-Account Access Issues

**Problem**: Route53 delegation failures

**Solution**:

```bash
# Verify networking account access
aws sts assume-role --role-arn arn:aws:iam::************:role/route53-delegate-role --role-session-name test
```

## 📚 Next Steps

Now that you have the basics down:

1. **Read the Architecture Docs**: Understand the [Project Structure](./project-structure.md)
2. **Learn About Modules**: Review [Module Development](./module-development.md)
3. **Understand DNS**: Read [Domain & DNS Management](./domain-dns-management.md)
4. **Follow Best Practices**: Check [Best Practices](./best-practices.md)
5. **Join the Team**: Ask questions and contribute improvements!

## 🤝 Getting Help

- **Documentation**: Check other files in this `docs/` folder
- **Troubleshooting**: See [Troubleshooting Guide](./troubleshooting.md)
- **Team Chat**: Ask questions in your team channels
- **Code Review**: Request reviews for infrastructure changes

## ⚠️ Important Safety Tips

1. **Always Plan First**: Never run `make apply` without reviewing `make plan`
2. **Test in Dev**: Make changes in `dev` environment first
3. **Production Care**: Extra caution with production (`prod`) environment
4. **State Locks**: If a process fails, check for stuck state locks
5. **Backup Awareness**: Understand that infrastructure changes can affect data
6. **Team Communication**: Coordinate with team on shared environments

Remember: Infrastructure changes affect everyone, so communicate and be careful!
