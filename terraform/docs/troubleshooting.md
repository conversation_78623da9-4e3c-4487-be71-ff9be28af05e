# Troubleshooting Guide

This guide helps you diagnose and resolve common issues when working with the Watt Terraform infrastructure.

## 🔐 Authentication Issues

### Problem: No credential providers found

```
Error: NoCredentialProviders: no valid providers in chain. Deprecated.
For verbose messaging see aws.Config.CredentialsChainVerboseErrors
```

**Diagnosis:**

```bash
# Check if you're logged in
aws sts get-caller-identity

# Check configured profiles
aws configure list-profiles
```

**Solutions:**

```bash
# Option 1: Login via SSO
make sso p=watt-staging

# Option 2: Set AWS profile environment variable
export AWS_PROFILE=watt-staging

# Option 3: Login directly
aws sso login --profile watt-staging
```

---

### Problem: Access denied with valid credentials

```
Error: AccessDenied: User is not authorized to perform: sts:AssumeRole
```

**Diagnosis:**

```bash
# Check current identity
aws sts get-caller-identity

# Verify account ID matches expected environment
echo "Expected account for staging: ************"
aws sts get-caller-identity --query Account --output text
```

**Solutions:**

1. **Wrong Account**: Switch to correct AWS profile

   ```bash
   make sso p=watt-staging  # For dev/staging environments
   make sso p=watt-prod     # For production environment
   ```

2. **Expired Session**: Re-authenticate

   ```bash
   aws sso logout
   make sso p=watt-staging
   ```

3. **Missing Permissions**: Contact platform team to verify IAM permissions

---

## 🗃️ State Management Issues

### Problem: State lock acquisition timeout

```
Error: Error acquiring the state lock
Lock Info:
  ID:        ********-1234-1234-1234-************
  Path:      terraform-state/environments/dev/terraform.tfstate
  Operation: OperationTypeApply
  Who:       <EMAIL>
  Version:   1.0.0
  Created:   2023-10-25 10:30:00 +0000 UTC
```

**Diagnosis:**

```bash
# Check if the lock holder is still active
# Look at the "Who" and "Created" fields in the error message

# Check current Terraform processes
ps aux | grep terraform
```

**Solutions:**

1. **Wait for lock release**: If someone else is actively applying changes
2. **Force unlock** (use with caution):

   ```bash
   cd terraform/composition
   terraform force-unlock ********-1234-1234-1234-************
   ```

3. **Contact lock holder**: If the lock is old and holder is not responsive

**⚠️ Warning**: Only force-unlock if you're certain no one else is applying changes!

---

### Problem: Backend configuration issues

```
Error: Failed to get existing workspaces: S3 bucket does not exist.
```

**Diagnosis:**

```bash
# Check backend configuration
cat terraform/composition/backend.tf

# Verify you're in the correct directory
pwd  # Should be .../terraform/composition
```

**Solutions:**

```bash
# Re-initialize backend
cd terraform/composition
terraform init -reconfigure

# If still failing, check AWS account and permissions
aws s3 ls s3://your-terraform-state-bucket/
```

---

## 🏗️ Infrastructure Deployment Issues

### Problem: Resource already exists

```
Error: Resource already exists
│ A resource with the ID "dev-quotation-app-lb" already exists.
```

**Diagnosis:**

```bash
# Check if resource exists in AWS
aws elbv2 describe-load-balancers --names dev-quotation-app-lb

# Check Terraform state
terraform state list | grep load_balancer
```

**Solutions:**

1. **Import existing resource**:

   ```bash
   terraform import aws_lb.app_load_balancer arn:aws:elasticloadbalancing:...
   ```

2. **Remove from AWS and recreate**:

   ```bash
   # If safe to delete
   aws elbv2 delete-load-balancer --load-balancer-arn arn:aws:...
   make apply s=dev
   ```

3. **Use different name**: Modify configuration to use unique names

---

### Problem: Dependency violation on destroy

```
Error: DependencyViolation: The vpc 'vpc-12345' has dependencies and cannot be deleted.
```

**Diagnosis:**

```bash
# List VPC dependencies
aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=vpc-12345"
aws ec2 describe-instances --filters "Name=vpc-id,Values=vpc-12345"
aws ec2 describe-security-groups --filters "Name=vpc-id,Values=vpc-12345"
```

**Solutions:**

1. **Gradual destruction**: Remove dependent resources first

   ```bash
   # Target specific resources for destruction
   terraform destroy -target=module.quotation_app
   terraform destroy -target=module.app_load_balancer
   terraform destroy  # Then destroy everything else
   ```

2. **Manual cleanup**: Remove dependencies via AWS CLI/Console
3. **Wait and retry**: Some resources take time to fully delete

---

## 🌐 DNS and Route53 Issues

### Problem: Certificate validation timeout

```
Error: Error waiting for certificate validation: timeout while waiting for state to become 'ISSUED'
```

**Diagnosis:**

```bash
# Check certificate status
aws acm describe-certificate --certificate-arn arn:aws:acm:...

# Check DNS validation records
dig _validation-string.dev.app.watt.co.uk TXT
```

**Solutions:**

1. **Verify DNS delegation**:

   ```bash
   # Check NS delegation
   dig NS dev.app.watt.co.uk

   # Check if delegation is working
   dig dev.app.watt.co.uk
   ```

2. **Check cross-account access**:

   ```bash
   # Test networking account role assumption
   aws sts assume-role \
     --role-arn arn:aws:iam::************:role/route53-delegate-role \
     --role-session-name test
   ```

3. **Manual validation record creation**: Create DNS records manually if automation fails

---

### Problem: Route53 delegation failures

```
Error: Error creating Route53 record: AccessDenied
```

**Diagnosis:**

```bash
# Check networking account access
aws sts assume-role \
  --role-arn arn:aws:iam::************:role/route53-delegate-role \
  --role-session-name debug

# Check if role exists and has permissions
aws iam get-role --role-name route53-delegate-role
```

**Solutions:**

1. **Verify cross-account trust**: Ensure networking account trusts your account
2. **Check IAM permissions**: Verify role has Route53 permissions
3. **Manual delegation**: Create NS records manually in networking account

---

## 🔧 Module and Configuration Issues

### Problem: Module source not found

```
Error: Module not found: The module source "../../modules/infrastructure/quotation_app" was not found.
```

**Diagnosis:**

```bash
# Check if module path exists
ls -la ../../modules/infrastructure/quotation_app/

# Verify current working directory
pwd
```

**Solutions:**

```bash
# Ensure you're in the correct directory
cd terraform/composition

# Check relative path from current location
ls -la ../modules/infrastructure/
```

---

### Problem: Variable validation failures

```
Error: Invalid value for variable "environment"
The environment must be dev, staging, or prod.
```

**Diagnosis:**
Check the tfvars file for the environment:

```bash
grep environment environments/dev.tfvars
```

**Solutions:**

```bash
# Fix the tfvars file
vim environments/dev.tfvars

# Ensure environment value is correct
environment = "dev"  # Must be exactly "dev", "staging", or "prod"
```

---

## 🔍 Debugging Techniques

### Enable Detailed Logging

```bash
# Terraform debug logging
export TF_LOG=DEBUG
export TF_LOG_PATH=./terraform-debug.log

# AWS CLI debug logging
aws --debug sts get-caller-identity

# Makefile debug mode
DBG_MAKEFILE=1 make plan s=dev
```

### Validate Configuration

```bash
# Check syntax and configuration
make validate

# Check for security issues
make tfsec

# Validate specific environment
terraform validate -var-file=environments/dev.tfvars
```

### Check Resource State

```bash
# List all resources in state
terraform state list

# Show specific resource details
terraform state show module.app_vpc.aws_vpc.vpc

# Show current workspace
terraform workspace show

# List all workspaces
terraform workspace list
```

## 🚨 Emergency Procedures

### Complete Environment Recovery

If an environment is completely broken:

1. **Backup current state**:

   ```bash
   terraform state pull > backup-$(date +%Y%m%d-%H%M%S).tfstate
   ```

2. **Try targeted recovery**:

   ```bash
   # Remove problematic resources from state
   terraform state rm problematic.resource

   # Re-import or recreate
   terraform import aws_instance.app i-********90abcdef0
   ```

3. **Nuclear option - rebuild environment**:

   ```bash
   # Only for non-production environments!
   make destroy s=broken-env
   make new s=replacement-env
   ```

### Production Emergency Rollback

For production issues:

1. **Immediate rollback**:

   ```bash
   # Use previous known-good plan if available
   terraform apply plans/previous-good-plan
   ```

2. **Selective resource rollback**:

   ```bash
   # Target specific resources
   terraform apply -target=module.problematic_module -var-file=environments/prod.tfvars
   ```

3. **State restoration**:

   ```bash
   # Restore from backup (extreme caution!)
   terraform state push backup-state-file.tfstate
   ```

## 📞 Getting Help

### Information to Gather

When asking for help, provide:

1. **Environment details**:

   ```bash
   terraform version
   aws --version
   echo $AWS_PROFILE
   terraform workspace show
   ```

2. **Error context**:
   - Full error message
   - Command that caused the error
   - Recent changes made
   - Environment being used

3. **Current state**:

   ```bash
   terraform state list
   terraform plan -var-file=environments/dev.tfvars
   ```

### Escalation Path

1. **Self-service**: Check this troubleshooting guide
2. **Team consultation**: Ask in team channels with context
3. **Platform team**: For cross-account or complex issues
4. **AWS support**: For AWS service-specific issues

### Useful Commands for Support

```bash
# Generate support bundle
terraform version > support-info.txt
aws sts get-caller-identity >> support-info.txt
terraform workspace show >> support-info.txt
terraform state list >> support-info.txt

# Anonymize and share configuration
terraform plan -var-file=environments/dev.tfvars -no-color > plan-output.txt
```

Remember: It's always better to ask for help early rather than making the problem worse through guesswork!
