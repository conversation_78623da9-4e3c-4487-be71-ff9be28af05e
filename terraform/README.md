# Watt Terraform Infrastructure

This repository contains the Infrastructure as Code (IaC) for the Watt platform, implementing a robust, scalable, and secure multi-account AWS architecture.

## 🏗️ Architecture Overview

Our infrastructure follows a **three-layer modular architecture** that provides clear separation of concerns and promotes reusability:

```
terraform/
├── composition/          # 🎯 Environment orchestration layer
├── modules/
│   ├── infrastructure/   # 🧩 Application component modules
│   └── resource/         # ⚙️  Low-level AWS resource modules
├── networking/          # 🌐 Root domain & Route53 management
├── shared/              # 🔑 Cross-account OIDC & shared services
└── docs/                # 📚 Detailed documentation
```

### 🌍 Multi-Account Strategy

| Account | Purpose | Domain Pattern | Account ID |
|---------|---------|----------------|------------|
| **networking** | Root Route53 hosted zone | `watt.co.uk` | ************ |
| **watt-prod** | Production environment | `app.watt.co.uk` | ************ |
| **watt-staging** | Staging environments | `{env}.app.watt.co.uk` | ************ |
| **shared** | OIDC & shared services | - | ************ |

## 🚀 Quick Start

### Prerequisites

- [AWS CLI v2](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- [Terraform](https://developer.hashicorp.com/terraform/downloads) (managed via tfenv)
- [Make](https://www.gnu.org/software/make/)
- [Infracost](https://www.infracost.io/docs/) (optional, for cost analysis)

### Basic Usage

1. **Navigate to composition directory:**

   ```bash
   cd terraform/composition
   ```

2. **Authenticate with AWS:**

   ```bash
   make sso p=watt-staging  # or watt-prod
   ```

3. **Initialize environment:**

   ```bash
   make init s=dev
   ```

4. **Plan changes:**

   ```bash
   make plan s=dev
   ```

5. **Apply changes:**

   ```bash
   make apply s=dev
   ```

### Available Environments

- `dev` - Development environment (`dev.app.watt.co.uk`)
- `staging` - Staging environment (`staging.app.watt.co.uk`)
- `prod` - Production environment (`app.watt.co.uk`)

## 📖 Documentation

### Core Concepts

- **[Project Structure](./docs/project-structure.md)** - Detailed explanation of the three-layer architecture
- **[Multi-Account Strategy](./docs/multi-account-strategy.md)** - AWS account separation and cross-account access
- **[Domain & DNS Management](./docs/domain-dns-management.md)** - Route53 delegation and domain structure

### Operational Guides

- **[Getting Started](./docs/getting-started.md)** - Complete setup guide for new developers
- **[Environment Management](./docs/environment-management.md)** - Creating, updating, and destroying environments
- **[Deployment Workflow](./docs/deployment-workflow.md)** - CI/CD and manual deployment processes

### Development & Best Practices

- **[Module Development](./docs/module-development.md)** - Creating new infrastructure and resource modules
- **[Best Practices](./docs/best-practices.md)** - Coding standards, security, and operational guidelines
- **[Troubleshooting](./docs/troubleshooting.md)** - Common issues and solutions

### Reference

- **[Makefile Commands](./docs/makefile-commands.md)** - Complete reference of available commands
- **[Variables Reference](./docs/variables-reference.md)** - Environment variables and configuration options
- **[Architecture Diagrams](./docs/architecture-diagrams.md)** - Visual representations of the infrastructure

## 🏗️ Infrastructure Components

Our composition layer orchestrates the following infrastructure modules:

| Module | Purpose | AWS Services |
|--------|---------|--------------|
| **networking** | VPC, subnets, Route53 | VPC, Route53, ACM |
| **app_load_balancer** | Application load balancing | ALB, Target Groups |
| **quotation_app** | ECS-based application | ECS, ECR, IAM |
| **quotation_db** | PostgreSQL database | RDS |
| **quotation_cloudfront** | CDN and edge caching | CloudFront, WAF |
| **messaging** | Async messaging | SQS, SNS, EventBridge |
| **secrets** | Secrets management | Secrets Manager |
| **bastion** | Database access | EC2 |

## 🔒 Security & Compliance

- **Cross-account IAM roles** for secure resource access
- **Secrets Manager** for sensitive configuration
- **VPC** with public/private subnet architecture
- **Security groups** with least-privilege access
- **TLS/SSL** certificates via ACM
- **Infrastructure scanning** with tfsec and checkov

## 💰 Cost Management

We use [Infracost](https://www.infracost.io/) for cost analysis:

```bash
cd terraform/composition
make infracost
```

Cost estimates are automatically generated for pull requests via GitHub Actions.

## 🤝 Contributing

1. Read the [Best Practices Guide](./docs/best-practices.md)
2. Follow the [Module Development Guide](./docs/module-development.md) for new components
3. Test changes in a development environment first
4. Ensure all security scans pass
5. Update documentation for any architectural changes

## 📞 Support

- **Issues**: Create GitHub issues for bugs or feature requests
- **Questions**: Refer to [Troubleshooting Guide](./docs/troubleshooting.md)
- **Architecture Reviews**: Contact the platform team

## 🔄 Migration Notes

If you're migrating from the old documentation structure, see [Migration Guide](./docs/migration-guide.md) for updated workflows and breaking changes.

---

**⚠️ Important**: Always run `make plan` before `make apply` to review changes. Use `make destroy` with extreme caution, especially in production environments.
