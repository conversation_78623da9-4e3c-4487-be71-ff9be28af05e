<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Error ({{error.code}}):
  {{error.message}}
</p>
<br />

<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Hello Team,
</p>
<br />

<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  System failed to generate a quote for a customer. Please see information
  captured for the business below:
</p>
<br />

<div>
  <table>
    <tr>
      <td>
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Company Information
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:<PERSON>o,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Utilities:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.Utilities}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Site address:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.SiteAddress}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Site postcode:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.SitePostcode}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business type:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessType}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business name:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business number:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact email:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactEmail}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact phone number:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactPhoneNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact name:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sole Trader D/O/B (If a sole trader):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.soleTraderDOB}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sole Trader Address History (If a sole trader):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.soleTraderAddresses}}
        </p>
      </td>
    </tr>

    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business address:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessAddress}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business postcode:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessPostcode}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Authorized:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.Authorized}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Credit check:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.CreditCheck}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          LOA signed:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.LOASigned}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Terms and conditions agreed to:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.TermsAndConditionsAgreedTo}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to smart meter:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.SmartMeterAgreement}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to direct debit:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.DirectDebitAgreement}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Electric Supplier
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Current supplier:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{electricSupplier.CurrentSupplier}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          New Contract Start Date:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{electricSupplier.NewContractStartDate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          MPAN:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{electricSupplier.MPAN}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Annual estimated usage (kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{electricSupplier.AnnualEstimatedUsage}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Gas Supplier
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Current supplier:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{gasSupplier.CurrentSupplier}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          New Contract Start Date:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{gasSupplier.NewContractStartDate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          MPRN:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{gasSupplier.MPRN}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Annual estimated usage (kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{gasSupplier.AnnualEstimatedUsage}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Other
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Experian soft credit score result:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{other.ExperianSoftCreditScoreResult}}
        </p>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          If the credit score is below 50 then the user has been shown quotes
          with a credit score of 50.
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Date/time quote submitted:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{other.DateTimeQuoteSubmitted}}
        </p>
      </td>
    </tr>
  </table>
</div>
<br />
<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Why is it **<b>NOT AVAILABLE</b>**? Two reasons the information could be
  missing. 1. The user was yet to provide it before the system had an error
  which resulted in this email. 2. When trying to save that information the
  system had an error and was not successful.
</p>
