<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Hello Team,
</p>
<br />

<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Quote has been completed for:
  {{companyInfo.ContactName}}. Please find LOA and Contract PDF attached.
</p>
<br />

<div>
  <table>
    <tr>
      <td>
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Company Information
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Utility
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.UtilityTypeName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Site address
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.SiteAddress}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Site postcode:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.SitePostcode}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business type:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessType}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business name:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business number:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact email:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactEmail}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact phone number:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactPhoneNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contact name:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.ContactName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business address:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessAddress}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Business postcode:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.BusinessPostcode}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Authorized:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.Authorized}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to Credit check:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.CreditCheck}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          LOA signed:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.LOASigned}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to Terms and conditions:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.TermsAndConditionsAgreedTo}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to smart meter:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.SmartMeterAgreement}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Agrees to direct debit:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{agreement_set.DirectDebitAgreement}}
        </p>
      </td>
    </tr>
    {{! Sole Trader Information: }}
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sole Trader Full Name
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.soleTraderFullName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sole Trader Date of Birth
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.soleTraderDOB}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sole Trader Home Addresses
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{companyInfo.soleTraderHomeAddresses}}
        </p>
      </td>
    </tr>

    {{! Current Supplier Information }}

    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Current Supplier Information
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Current supplier:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{currentSupplier.CurrentSupplier}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          New contract start date:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{currentSupplier.NewContractStartDate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          {{currentSupplier.MeterName}}:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{currentSupplier.MeterNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Annual estimated usage (kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{currentSupplier.AnnualEstimatedUsage}}
        </p>
      </td>
    </tr>

    {{! Other Section }}

    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Other
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Experian soft credit score result:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{other.ExperianSoftCreditScoreResult}}
        </p>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          If the credit score is below 50 then the user has been shown quotes
          with a credit score of 50.
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Date/time quote submitted:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{other.DateTimeQuoteSubmitted}}
        </p>
      </td>
    </tr>
    {{! Contract Information }}
    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Contract Information
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          New Supplier
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.NewSupplier}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contract start date:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.ContractStartDate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contract end date:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.ContractEndDate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contract Duration (months):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.ContractDuration}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contract Comments:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.ContractComments}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Day unit rate (pence/kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.DayUnitRate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Night unit rate (pence/kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.NightUnitRate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Weekend unit rate (pence/kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.WeekendUnitRate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Evening unit rate (pence/kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.EveningUnitRate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Off peak unit rate (pence/kWh):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.OffPeakUnitRate}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Annual Price (estimate):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          £
          {{contract.AnnualPrice}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Standing charge (pence/day):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.StandingCharge}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Capacity Charge KVA:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.CapacityChargeKVA}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Price Guaranteed (months):
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.PriceGuaranteed}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Contract Type:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{contract.ContractType}}
        </p>
      </td>
    </tr>
    {{! Bank Details }}
    <tr>
      <td>
        <br />
        <h1
          style="margin:0; font-family:Roboto,sans-serif; font-size:24px; line-height:32px; color: #0b3f55;"
        >
          Bank details
        </h1>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Bank Name
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{bankDetails.BankName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Account holder name:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{bankDetails.AccountHolderName}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Account number:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{bankDetails.AccountNumber}}
        </p>
      </td>
    </tr>
    <tr>
      <td>
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
        >
          Sort code:
        </p>
      </td>
      <td style="background-color: #96ab4f;">
        <p
          style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55; padding: 5px 8px;"
        >
          {{bankDetails.SortCode}}
        </p>
      </td>
    </tr>
  </table>
</div>
<br />
<p
  style="margin:0; font-family:Roboto,sans-serif; font-size:18px; line-height:24px; color: #0b3f55;"
>
  Why is it **<b>NOT AVAILABLE</b>**? Two reasons the information could be
  missing. 1. The user was yet to provide it before the system had an error
  which resulted in this email. 2. When trying to save that information the
  system had an error and was not successful.
</p>